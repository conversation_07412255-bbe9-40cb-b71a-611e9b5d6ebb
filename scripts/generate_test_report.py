"""
Test Report Generator

This module generates comprehensive test reports with metrics and insights.
"""

import json
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional


class TestReportGenerator:
    """Generate comprehensive test reports."""

    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path.cwd()
        self.report_data = {
            "timestamp": datetime.now().isoformat(),
            "project": "RegBot Automation",
            "summary": {},
            "test_categories": {},
            "coverage": {},
            "quality_metrics": {},
        }

    def parse_coverage_xml(self, coverage_file: Path = None) -> Dict:
        """Parse coverage XML report."""
        coverage_file = coverage_file or self.project_root / "coverage.xml"

        if not coverage_file.exists():
            return {}

        try:
            tree = ET.parse(coverage_file)
            root = tree.getroot()

            # Extract overall coverage
            coverage_data = {
                "line_rate": float(root.get("line-rate", 0)) * 100,
                "branch_rate": float(root.get("branch-rate", 0)) * 100,
                "lines_covered": int(root.get("lines-covered", 0)),
                "lines_valid": int(root.get("lines-valid", 0)),
                "branches_covered": int(root.get("branches-covered", 0)),
                "branches_valid": int(root.get("branches-valid", 0)),
                "packages": {},
            }

            # Extract package-level coverage
            for package in root.findall(".//package"):
                package_name = package.get("name", "unknown")
                coverage_data["packages"][package_name] = {
                    "line_rate": float(package.get("line-rate", 0)) * 100,
                    "branch_rate": float(package.get("branch-rate", 0)) * 100,
                    "complexity": float(package.get("complexity", 0)),
                }

            return coverage_data

        except Exception as e:
            print(f"Error parsing coverage XML: {e}")
            return {}

    def parse_junit_xml(self, junit_file: Path) -> Dict:
        """Parse JUnit XML test results."""
        if not junit_file.exists():
            return {}

        try:
            tree = ET.parse(junit_file)
            root = tree.getroot()

            return {
                "tests": int(root.get("tests", 0)),
                "failures": int(root.get("failures", 0)),
                "errors": int(root.get("errors", 0)),
                "skipped": int(root.get("skipped", 0)),
                "time": float(root.get("time", 0)),
                "name": root.get("name", "unknown"),
            }

        except Exception as e:
            print(f"Error parsing JUnit XML: {e}")
            return {}

    def parse_bandit_json(self, bandit_file: Path = None) -> Dict:
        """Parse Bandit security scan results."""
        bandit_file = bandit_file or self.project_root / "bandit-report.json"

        if not bandit_file.exists():
            return {}

        try:
            with open(bandit_file) as f:
                data = json.load(f)

            results = data.get("results", [])
            metrics = data.get("metrics", {})

            severity_counts = {"HIGH": 0, "MEDIUM": 0, "LOW": 0}
            confidence_counts = {"HIGH": 0, "MEDIUM": 0, "LOW": 0}

            for result in results:
                severity = result.get("issue_severity", "UNKNOWN")
                confidence = result.get("issue_confidence", "UNKNOWN")

                if severity in severity_counts:
                    severity_counts[severity] += 1
                if confidence in confidence_counts:
                    confidence_counts[confidence] += 1

            return {
                "total_issues": len(results),
                "severity_breakdown": severity_counts,
                "confidence_breakdown": confidence_counts,
                "lines_of_code": metrics.get("_totals", {}).get("loc", 0),
                "files_skipped": metrics.get("_totals", {}).get("skipped_files", 0),
            }

        except Exception as e:
            print(f"Error parsing Bandit JSON: {e}")
            return {}

    def parse_benchmark_json(self, benchmark_file: Path = None) -> Dict:
        """Parse pytest-benchmark results."""
        benchmark_file = benchmark_file or self.project_root / "benchmark.json"

        if not benchmark_file.exists():
            return {}

        try:
            with open(benchmark_file) as f:
                data = json.load(f)

            benchmarks = data.get("benchmarks", [])

            if not benchmarks:
                return {}

            # Calculate summary statistics
            times = [b["stats"]["mean"] for b in benchmarks]

            return {
                "total_benchmarks": len(benchmarks),
                "mean_time": sum(times) / len(times),
                "min_time": min(times),
                "max_time": max(times),
                "benchmarks": [
                    {
                        "name": b["name"],
                        "mean": b["stats"]["mean"],
                        "stddev": b["stats"]["stddev"],
                        "min": b["stats"]["min"],
                        "max": b["stats"]["max"],
                    }
                    for b in benchmarks
                ],
            }

        except Exception as e:
            print(f"Error parsing benchmark JSON: {e}")
            return {}

    def calculate_quality_score(self) -> float:
        """Calculate overall quality score."""
        scores = []

        # Coverage score (0-30 points)
        coverage = self.report_data["coverage"].get("line_rate", 0)
        coverage_score = min(30, coverage * 0.3)
        scores.append(coverage_score)

        # Security score (0-25 points)
        security = self.report_data["quality_metrics"].get("security", {})
        high_issues = security.get("severity_breakdown", {}).get("HIGH", 0)
        medium_issues = security.get("severity_breakdown", {}).get("MEDIUM", 0)

        security_score = 25
        security_score -= high_issues * 10  # -10 for each high severity issue
        security_score -= medium_issues * 2  # -2 for each medium severity issue
        security_score = max(0, security_score)
        scores.append(security_score)

        # Test success score (0-35 points)
        test_summary = self.report_data["summary"]
        total_tests = test_summary.get("total_tests", 0)
        failed_tests = test_summary.get("failed_tests", 0)

        if total_tests > 0:
            test_success_rate = (total_tests - failed_tests) / total_tests
            test_score = test_success_rate * 35
        else:
            test_score = 0

        scores.append(test_score)

        # Performance score (0-10 points)
        # Award points if benchmarks exist and run successfully
        benchmarks = self.report_data["quality_metrics"].get("performance", {})
        if benchmarks.get("total_benchmarks", 0) > 0:
            performance_score = 10
        else:
            performance_score = 5  # Partial credit if no benchmarks

        scores.append(performance_score)

        return sum(scores)

    def generate_report(self) -> Dict:
        """Generate comprehensive test report."""
        # Parse coverage data
        self.report_data["coverage"] = self.parse_coverage_xml()

        # Parse test results
        test_results_dir = self.project_root / "test-results"
        test_categories = ["unit", "integration", "browser", "e2e"]

        total_tests = 0
        total_failures = 0
        total_errors = 0
        total_skipped = 0
        total_time = 0

        for category in test_categories:
            junit_file = test_results_dir / f"{category}-tests.xml"
            category_data = self.parse_junit_xml(junit_file)

            if category_data:
                self.report_data["test_categories"][category] = category_data
                total_tests += category_data.get("tests", 0)
                total_failures += category_data.get("failures", 0)
                total_errors += category_data.get("errors", 0)
                total_skipped += category_data.get("skipped", 0)
                total_time += category_data.get("time", 0)

        # Summary statistics
        self.report_data["summary"] = {
            "total_tests": total_tests,
            "passed_tests": total_tests - total_failures - total_errors,
            "failed_tests": total_failures,
            "error_tests": total_errors,
            "skipped_tests": total_skipped,
            "total_time": total_time,
            "success_rate": (total_tests - total_failures - total_errors)
            / total_tests
            * 100
            if total_tests > 0
            else 0,
        }

        # Parse quality metrics
        self.report_data["quality_metrics"] = {
            "security": self.parse_bandit_json(),
            "performance": self.parse_benchmark_json(),
        }

        # Calculate overall quality score
        self.report_data["quality_score"] = self.calculate_quality_score()

        return self.report_data

    def generate_html_report(self, output_file: Path = None) -> Path:
        """Generate HTML report."""
        output_file = output_file or self.project_root / "test-report.html"

        report_data = self.generate_report()

        html_template = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RegBot Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; border-bottom: 2px solid #eee; padding-bottom: 20px; margin-bottom: 30px; }}
        .metric-card {{ background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 4px; }}
        .metric-card.success {{ border-left-color: #28a745; }}
        .metric-card.warning {{ border-left-color: #ffc107; }}
        .metric-card.danger {{ border-left-color: #dc3545; }}
        .grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }}
        .progress-bar {{ background: #e9ecef; border-radius: 4px; overflow: hidden; height: 20px; }}
        .progress-fill {{ height: 100%; background: #28a745; transition: width 0.3s ease; }}
        .quality-score {{ font-size: 2em; font-weight: bold; text-align: center; padding: 20px; }}
        .table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        .table th, .table td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
        .table th {{ background-color: #f8f9fa; font-weight: bold; }}
        .timestamp {{ color: #666; font-size: 0.9em; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 RegBot Automation Test Report</h1>
            <p class="timestamp">Generated on {report_data["timestamp"]}</p>
            <div class="quality-score">
                Quality Score: {report_data["quality_score"]:.1f}/100
            </div>
        </div>
        
        <div class="grid">
            <div class="metric-card {"success" if report_data["summary"].get("success_rate", 0) > 90 else "warning" if report_data["summary"].get("success_rate", 0) > 70 else "danger"}">
                <h3>📊 Test Summary</h3>
                <p><strong>Total Tests:</strong> {report_data["summary"].get("total_tests", 0)}</p>
                <p><strong>Passed:</strong> {report_data["summary"].get("passed_tests", 0)}</p>
                <p><strong>Failed:</strong> {report_data["summary"].get("failed_tests", 0)}</p>
                <p><strong>Success Rate:</strong> {report_data["summary"].get("success_rate", 0):.1f}%</p>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {report_data["summary"].get("success_rate", 0)}%"></div>
                </div>
            </div>
            
            <div class="metric-card {"success" if report_data["coverage"].get("line_rate", 0) > 80 else "warning" if report_data["coverage"].get("line_rate", 0) > 60 else "danger"}">
                <h3>📈 Code Coverage</h3>
                <p><strong>Line Coverage:</strong> {report_data["coverage"].get("line_rate", 0):.1f}%</p>
                <p><strong>Branch Coverage:</strong> {report_data["coverage"].get("branch_rate", 0):.1f}%</p>
                <p><strong>Lines Covered:</strong> {report_data["coverage"].get("lines_covered", 0)}</p>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {report_data["coverage"].get("line_rate", 0)}%"></div>
                </div>
            </div>
            
            <div class="metric-card {"success" if report_data["quality_metrics"].get("security", {}).get("severity_breakdown", {}).get("HIGH", 0) == 0 else "danger"}">
                <h3>🔒 Security Scan</h3>
                <p><strong>High Severity:</strong> {report_data["quality_metrics"].get("security", {}).get("severity_breakdown", {}).get("HIGH", 0)}</p>
                <p><strong>Medium Severity:</strong> {report_data["quality_metrics"].get("security", {}).get("severity_breakdown", {}).get("MEDIUM", 0)}</p>
                <p><strong>Low Severity:</strong> {report_data["quality_metrics"].get("security", {}).get("severity_breakdown", {}).get("LOW", 0)}</p>
                <p><strong>Total Issues:</strong> {report_data["quality_metrics"].get("security", {}).get("total_issues", 0)}</p>
            </div>
            
            <div class="metric-card success">
                <h3>⚡ Performance</h3>
                <p><strong>Benchmarks:</strong> {report_data["quality_metrics"].get("performance", {}).get("total_benchmarks", 0)}</p>
                <p><strong>Mean Time:</strong> {report_data["quality_metrics"].get("performance", {}).get("mean_time", 0):.4f}s</p>
                <p><strong>Min Time:</strong> {report_data["quality_metrics"].get("performance", {}).get("min_time", 0):.4f}s</p>
                <p><strong>Max Time:</strong> {report_data["quality_metrics"].get("performance", {}).get("max_time", 0):.4f}s</p>
            </div>
        </div>
        
        <h2>📋 Test Categories</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>Category</th>
                    <th>Tests</th>
                    <th>Passed</th>
                    <th>Failed</th>
                    <th>Skipped</th>
                    <th>Time (s)</th>
                    <th>Success Rate</th>
                </tr>
            </thead>
            <tbody>
        """

        for category, data in report_data["test_categories"].items():
            total = data.get("tests", 0)
            failures = data.get("failures", 0)
            errors = data.get("errors", 0)
            passed = total - failures - errors
            success_rate = (passed / total * 100) if total > 0 else 0

            html_template += f"""
                <tr>
                    <td>{category.title()}</td>
                    <td>{total}</td>
                    <td>{passed}</td>
                    <td>{failures + errors}</td>
                    <td>{data.get("skipped", 0)}</td>
                    <td>{data.get("time", 0):.2f}</td>
                    <td>{success_rate:.1f}%</td>
                </tr>
            """

        html_template += """
            </tbody>
        </table>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666;">
            <p>Generated by RegBot Quality Assurance System</p>
        </div>
    </div>
</body>
</html>
        """

        with open(output_file, "w") as f:
            f.write(html_template)

        return output_file

    def generate_json_report(self, output_file: Path = None) -> Path:
        """Generate JSON report."""
        output_file = output_file or self.project_root / "test-report.json"

        report_data = self.generate_report()

        with open(output_file, "w") as f:
            json.dump(report_data, f, indent=2)

        return output_file


def main():
    """Generate test reports."""
    generator = TestReportGenerator()

    print("📊 Generating test reports...")

    html_file = generator.generate_html_report()
    json_file = generator.generate_json_report()

    print(f"✅ HTML report generated: {html_file}")
    print(f"✅ JSON report generated: {json_file}")


if __name__ == "__main__":
    main()
