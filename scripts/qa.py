#!/usr/bin/env python3
"""
Quality Assurance Script for RegBot Automation

This script runs comprehensive quality checks including:
- Code formatting and linting
- Type checking
- Security scanning
- Test execution with coverage
- Performance benchmarking
"""

import subprocess
import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Tuple


class QualityAssurance:
    """Quality assurance automation."""

    def __init__(self):
        self.project_root = Path.cwd()
        self.results = {}
        self.start_time = time.time()

    def run_command(
        self, command: List[str], description: str
    ) -> Tuple[bool, str, str]:
        """Run a command and capture output."""
        print(f"🔄 {description}...")
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                cwd=self.project_root,
                timeout=300,  # 5 minutes timeout
            )
            success = result.returncode == 0
            symbol = "✅" if success else "❌"
            print(f"{symbol} {description} {'completed' if success else 'failed'}")
            return success, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            print(f"⏰ {description} timed out")
            return False, "", "Command timed out"
        except Exception as e:
            print(f"💥 {description} crashed: {e}")
            return False, "", str(e)

    def check_formatting(self) -> bool:
        """Check code formatting with ruff."""
        success, stdout, stderr = self.run_command(
            ["uv", "run", "ruff", "format", "--check", "."], "Checking code formatting"
        )
        self.results["formatting"] = {
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
        }
        return success

    def run_linting(self) -> bool:
        """Run linting with ruff."""
        success, stdout, stderr = self.run_command(
            ["uv", "run", "ruff", "check", "."], "Running code linting"
        )
        self.results["linting"] = {
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
        }
        return success

    def run_type_checking(self) -> bool:
        """Run type checking with mypy."""
        success, stdout, stderr = self.run_command(
            ["uv", "run", "mypy", "core/", "services/", "--ignore-missing-imports"],
            "Running type checking",
        )
        self.results["type_checking"] = {
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
        }
        return success

    def run_security_scan(self) -> bool:
        """Run security scanning with bandit."""
        success, stdout, stderr = self.run_command(
            [
                "uv",
                "run",
                "bandit",
                "-r",
                "core/",
                "services/",
                "-f",
                "json",
                "-o",
                "bandit-report.json",
            ],
            "Running security scan",
        )

        # Parse bandit results
        try:
            if Path("bandit-report.json").exists():
                with open("bandit-report.json") as f:
                    bandit_data = json.load(f)
                    high_severity = len(
                        [
                            issue
                            for issue in bandit_data.get("results", [])
                            if issue.get("issue_severity") == "HIGH"
                        ]
                    )
                    medium_severity = len(
                        [
                            issue
                            for issue in bandit_data.get("results", [])
                            if issue.get("issue_severity") == "MEDIUM"
                        ]
                    )

                    print(
                        f"🔒 Security scan found {high_severity} high and {medium_severity} medium severity issues"
                    )
                    success = high_severity == 0  # Only fail on high severity issues
        except Exception as e:
            print(f"⚠️ Could not parse security scan results: {e}")

        self.results["security"] = {
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
        }
        return success

    def run_unit_tests(self) -> bool:
        """Run unit tests with coverage."""
        success, stdout, stderr = self.run_command(
            [
                "uv",
                "run",
                "pytest",
                "tests/unit/",
                "-v",
                "--cov=core",
                "--cov=services",
                "--cov-report=term-missing",
                "--cov-report=xml",
                "--cov-report=html",
            ],
            "Running unit tests with coverage",
        )
        self.results["unit_tests"] = {
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
        }
        return success

    def run_integration_tests(self) -> bool:
        """Run integration tests."""
        success, stdout, stderr = self.run_command(
            [
                "uv",
                "run",
                "pytest",
                "tests/integration/",
                "-v",
                "--cov=core",
                "--cov=services",
                "--cov-report=term-missing",
            ],
            "Running integration tests",
        )
        self.results["integration_tests"] = {
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
        }
        return success

    def run_fast_tests(self) -> bool:
        """Run only fast tests (no browser/e2e)."""
        success, stdout, stderr = self.run_command(
            [
                "uv",
                "run",
                "pytest",
                "tests/",
                "-v",
                "-m",
                "not slow and not browser",
                "--cov=core",
                "--cov=services",
                "--cov-report=term-missing",
            ],
            "Running fast tests",
        )
        self.results["fast_tests"] = {
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
        }
        return success

    def generate_coverage_report(self) -> bool:
        """Generate comprehensive coverage report."""
        success, stdout, stderr = self.run_command(
            ["uv", "run", "coverage", "html"], "Generating HTML coverage report"
        )

        if success and Path("htmlcov/index.html").exists():
            print("📊 Coverage report generated at htmlcov/index.html")

        return success

    def run_performance_tests(self) -> bool:
        """Run performance benchmarks."""
        success, stdout, stderr = self.run_command(
            [
                "uv",
                "run",
                "pytest",
                "tests/",
                "--benchmark-only",
                "--benchmark-json=benchmark.json",
            ],
            "Running performance benchmarks",
        )

        # Parse benchmark results
        try:
            if Path("benchmark.json").exists():
                with open("benchmark.json") as f:
                    benchmark_data = json.load(f)
                    benchmarks = benchmark_data.get("benchmarks", [])
                    print(f"⚡ Ran {len(benchmarks)} performance benchmarks")
        except Exception as e:
            print(f"⚠️ Could not parse benchmark results: {e}")

        self.results["performance"] = {
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
        }
        return success

    def check_dependencies(self) -> bool:
        """Check for dependency issues."""
        success, stdout, stderr = self.run_command(
            ["uv", "pip", "check"], "Checking dependencies"
        )
        self.results["dependencies"] = {
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
        }
        return success

    def build_package(self) -> bool:
        """Test package building."""
        success, stdout, stderr = self.run_command(["uv", "build"], "Building package")
        self.results["build"] = {"success": success, "stdout": stdout, "stderr": stderr}
        return success

    def generate_report(self):
        """Generate comprehensive QA report."""
        duration = time.time() - self.start_time

        print("\n" + "=" * 60)
        print("🎯 QUALITY ASSURANCE REPORT")
        print("=" * 60)
        print(f"⏱️ Total execution time: {duration:.2f} seconds")
        print()

        checks = [
            (
                "Code Formatting",
                self.results.get("formatting", {}).get("success", False),
            ),
            ("Linting", self.results.get("linting", {}).get("success", False)),
            (
                "Type Checking",
                self.results.get("type_checking", {}).get("success", False),
            ),
            ("Security Scan", self.results.get("security", {}).get("success", False)),
            ("Unit Tests", self.results.get("unit_tests", {}).get("success", False)),
            (
                "Integration Tests",
                self.results.get("integration_tests", {}).get("success", False),
            ),
            (
                "Dependencies",
                self.results.get("dependencies", {}).get("success", False),
            ),
            ("Package Build", self.results.get("build", {}).get("success", False)),
        ]

        passed = sum(1 for _, success in checks if success)
        total = len(checks)

        for check_name, success in checks:
            symbol = "✅" if success else "❌"
            print(f"{symbol} {check_name}")

        print()
        print(f"📈 Overall Score: {passed}/{total} ({passed / total * 100:.1f}%)")

        if passed == total:
            print("🎉 All quality checks passed!")
            return True
        else:
            print("⚠️ Some quality checks failed. Review the output above.")
            return False

    def run_full_qa(self, include_performance: bool = False) -> bool:
        """Run full quality assurance suite."""
        print("🚀 Starting Quality Assurance Suite")
        print("=" * 50)

        # Core quality checks
        checks = [
            self.check_formatting,
            self.run_linting,
            self.run_type_checking,
            self.run_security_scan,
            self.check_dependencies,
            self.run_fast_tests,  # Use fast tests for QA
            self.build_package,
        ]

        # Add performance tests if requested
        if include_performance:
            checks.append(self.run_performance_tests)

        # Run all checks
        all_passed = True
        for check in checks:
            try:
                success = check()
                if not success:
                    all_passed = False
            except Exception as e:
                print(f"💥 Check failed with exception: {e}")
                all_passed = False

        # Generate final report
        return self.generate_report() and all_passed


def main():
    """Main entry point."""
    import argparse

    parser = argparse.ArgumentParser(description="Run quality assurance checks")
    parser.add_argument(
        "--performance", action="store_true", help="Include performance benchmarks"
    )
    parser.add_argument(
        "--format-only", action="store_true", help="Only check code formatting"
    )
    parser.add_argument("--tests-only", action="store_true", help="Only run tests")

    args = parser.parse_args()

    qa = QualityAssurance()

    if args.format_only:
        success = qa.check_formatting()
        sys.exit(0 if success else 1)
    elif args.tests_only:
        success = qa.run_fast_tests()
        sys.exit(0 if success else 1)
    else:
        success = qa.run_full_qa(include_performance=args.performance)
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
