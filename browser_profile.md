  🎯 Core Integration Strategy

  1. BrowserManager Enhancement Architecture

  # core/browser_manager.py - Enhanced structure
  class BrowserProfile:
      def __init__(self):
          self.window_size: Tuple[int, int]
          self.screen_resolution: Tuple[int, int]
          self.user_agent: str
          self.platform: str
          self.device_scale_factor: float
          self.timezone: str
          self.language: str
          self.webgl_vendor: str
          self.webgl_renderer: str

  class BrowserManager:
      def __init__(self, profile: BrowserProfile = None):
          self.profile = profile or self._generate_random_profile()

      def launch_with_profile(self) -> Browser:
          # Apply profile settings to browser launch
          pass

  🔧 BitBrowser Key Settings Analysis

  From the BitBrowser demo and documentation, here are the essential fingerprint parameters for basic antibot evasion:

  Core Fingerprint Settings (browserFingerPrint object):

  CORE_FINGERPRINT_SETTINGS = {
      # Essential - Browser Engine
      'coreVersion': '124',  # Chrome version (always use latest stable)

      # Essential - Screen & Window
      'screenWidth': 1920,
      'screenHeight': 1080,
      'availScreenWidth': 1920,
      'availScreenHeight': 1040,  # Slightly less than full height

      # Essential - User Agent Components
      'userAgent': 'custom_ua_string',
      'platform': 'Win32',  # Win32, MacIntel, Linux x86_64, etc.

      # Essential - WebGL Fingerprint (critical for detection)
      'webglVendor': 'Google Inc. (NVIDIA)',
      'webglRenderer': 'ANGLE (NVIDIA, NVIDIA GeForce RTX 3070)',

      # Important - Device characteristics
      'deviceScaleFactor': 1,
      'colorDepth': 24,
      'pixelDepth': 24,
      'hardwareConcurrency': 8,  # CPU cores

      # Important - Locale
      'timezone': 'America/New_York',
      'language': 'en-US,en;q=0.9',
      'languages': ['en-US', 'en'],
  }

  Profile-Level Settings (browser behavior):

  PROFILE_SETTINGS = {
      # Window behavior
      'name': 'auto_generated_name',
      'remark': '',

      # Anti-detection features
      'disableClipboard': False,
      'disableNotifications': False,
      'clearCacheFilesBeforeLaunch': True,  # Your preference
      'clearCookiesBeforeLaunch': True,     # Your preference

      # Stealth settings
      'allowedSignin': True,
      'credentialsEnableService': False,  # Disable autofill popups
      'abortImage': False,  # Don't block images
      'muteAudio': False,   # Don't mute
  }

  📱 Platform-Specific Profiles

  1. Windows 10/11 Profile:

  WINDOWS_PROFILE = {
      'platform': 'Win32',
      'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'screenWidth': random.choice([1920, 1366, 1536, 2560]),
      'screenHeight': random.choice([1080, 768, 864, 1440]),
      'webglVendor': random.choice(['Google Inc. (NVIDIA)', 'Google Inc. (AMD)', 'Google Inc. (Intel)']),
      'webglRenderer': random.choice([
          'ANGLE (NVIDIA, NVIDIA GeForce RTX 3070)',
          'ANGLE (AMD, AMD Radeon RX 6700 XT)',
          'ANGLE (Intel, Intel(R) UHD Graphics 630)'
      ]),
      'hardwareConcurrency': random.choice([4, 6, 8, 12, 16]),
      'timezone': random.choice(['America/New_York', 'America/Chicago', 'America/Los_Angeles']),
  }

  2. macOS Profile:

  MACOS_PROFILE = {
      'platform': 'MacIntel',
      'userAgent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'screenWidth': random.choice([1440, 1920, 2560]),
      'screenHeight': random.choice([900, 1080, 1440]),
      'webglVendor': 'Google Inc. (Apple)',
      'webglRenderer': random.choice([
          'ANGLE (Apple, Apple M1, OpenGL 4.1)',
          'ANGLE (Apple, AMD Radeon Pro 5500M, OpenGL 4.1)',
          'ANGLE (Apple, Intel Iris Pro, OpenGL 4.1)'
      ]),
      'hardwareConcurrency': random.choice([4, 8, 10]),
      'timezone': random.choice(['America/New_York', 'America/Los_Angeles']),
      'deviceScaleFactor': random.choice([1, 2]),  # Retina vs non-Retina
  }

  3. Android 13 Profile:

  ANDROID_PROFILE = {
      'platform': 'Linux armv8l',
      'userAgent': 'Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
      'screenWidth': random.choice([393, 412, 360, 414]),
      'screenHeight': random.choice([851, 915, 800, 896]),
      'webglVendor': 'Google Inc. (Qualcomm)',
      'webglRenderer': random.choice([
          'Adreno (TM) 730',
          'Adreno (TM) 660',
          'Mali-G78 MP20'
      ]),
      'hardwareConcurrency': random.choice([6, 8]),
      'deviceScaleFactor': random.choice([2.75, 3, 3.5]),
      'isMobile': True,
  }

  4. iOS Latest Profile:

  IOS_PROFILE = {
      'platform': 'iPhone',
      'userAgent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/********* Mobile/15E148 Safari/604.1',
      'screenWidth': random.choice([393, 428, 414, 375]),
      'screenHeight': random.choice([852, 926, 896, 812]),
      'webglVendor': 'Apple Inc.',
      'webglRenderer': 'Apple GPU',
      'hardwareConcurrency': random.choice([6, 8]),
      'deviceScaleFactor': random.choice([2, 3]),
      'isMobile': True,
  }

  🔧 Implementation Instructions for Your Teammate

  Phase 1: Core Profile Generator

  # core/browser_profiles.py
  class BrowserProfileGenerator:
      """Generate realistic browser profiles for antibot evasion."""

      PLATFORMS = ['windows10', 'windows11', 'macos', 'android13', 'ios']

      def generate_profile(self, platform: str = None) -> dict:
          """Generate a complete browser profile."""
          platform = platform or random.choice(self.PLATFORMS)

          base_profile = self._get_platform_base(platform)
          fingerprint = self._generate_fingerprint(base_profile)

          return {
              'profile_settings': base_profile,
              'browser_fingerprint': fingerprint,
              'playwright_args': self._convert_to_playwright_args(base_profile, fingerprint)
          }

      def _convert_to_playwright_args(self, profile: dict, fingerprint: dict) -> dict:
          """Convert profile to Playwright launch arguments."""
          return {
              'args': [
                  f'--window-size={fingerprint["screenWidth"]},{fingerprint["screenHeight"]}',
                  '--disable-blink-features=AutomationControlled',
                  '--disable-features=VizDisplayCompositor',
              ],
              'viewport': {
                  'width': fingerprint['screenWidth'],
                  'height': fingerprint['screenHeight']
              },
              'user_agent': fingerprint['userAgent'],
              'device_scale_factor': fingerprint.get('deviceScaleFactor', 1),
              'locale': fingerprint.get('language', 'en-US'),
              'timezone_id': fingerprint.get('timezone', 'America/New_York'),
          }

  Phase 2: Enhanced BrowserManager Integration

  # core/browser_manager.py - Update existing class
  class BrowserManager:
      def __init__(self, browser_type: str = 'chromium', profile_platform: str = None):
          self.browser_type = browser_type
          self.profile_generator = BrowserProfileGenerator()
          self.current_profile = self.profile_generator.generate_profile(profile_platform)

      async def launch_browser(self, **kwargs) -> Browser:
          """Launch browser with generated profile."""
          # Merge profile args with any passed kwargs
          launch_args = {**self.current_profile['playwright_args'], **kwargs}

          # Add stealth and antibot evasion
          if 'args' not in launch_args:
              launch_args['args'] = []

          launch_args['args'].extend([
              '--disable-blink-features=AutomationControlled',
              '--disable-features=VizDisplayCompositor',
              '--no-first-run',
              '--disable-default-apps',
              '--disable-dev-shm-usage',
          ])

          browser = await self.playwright_instance.chromium.launch(**launch_args)

          # Apply additional stealth after browser launch
          await self._apply_stealth_settings(browser)

          return browser

      async def _apply_stealth_settings(self, browser: Browser):
          """Apply additional stealth settings post-launch."""
          # This is where you'd add playwright-stealth or custom stealth injections
          pass

  Phase 3: Configuration Integration

  # Add to your existing config structure
  BROWSER_PROFILES = {
      'desktop_windows': {'platform': 'windows10', 'weight': 40},
      'desktop_macos': {'platform': 'macos', 'weight': 30},
      'mobile_android': {'platform': 'android13', 'weight': 20},
      'mobile_ios': {'platform': 'ios', 'weight': 10},
  }

  # In your service classes
  class FirecrawlService:
      def __init__(self, profile_platform: str = None):
          selected_platform = profile_platform or self._select_weighted_platform()
          self.browser_manager = BrowserManager(profile_platform=selected_platform)

  🎯 Migration Strategy

  Immediate (Keep Current Approach + Add Profiles):

  1. Keep your existing browser_manager.py working
  2. Add BrowserProfileGenerator as new module
  3. Add optional profile parameter to existing methods
  4. Test with one service (like Firecrawl) first

  Gradual Enhancement:

  1. Week 1: Implement basic profile generation for Windows/macOS
  2. Week 2: Add mobile profiles and test with different services
  3. Week 3: Add randomization and rotation logic
  4. Week 4: Full integration and performance testing

  🔑 Key Success Factors

  1. Start Simple: Focus on Windows/macOS desktop profiles first
  2. Test Incrementally: Validate each platform profile works with your target sites
  3. Monitor Detection: Track success rates and adjust fingerprints based on results
  4. Keep Current Logic: Don't break existing functionality during migration