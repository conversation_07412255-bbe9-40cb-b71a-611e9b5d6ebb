"""Main entry point for regbot automation - E2E testing and verification."""

import asyncio
import logging
import argparse
import sys
from pathlib import Path
from typing import Optional

from core.config import Config
from core.results_storage import ResultsStorage
from core.service_registry import ServiceRegistry

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def register_service(
    service_name: str, config: Config, storage: ResultsStorage, debug_mode: bool = False
):
    """Register a service and store credentials."""

    service_config = config.services.get(service_name)
    if not service_config:
        logger.error(f"Service {service_name} not configured")
        return False

    if not service_config.enabled:
        logger.info(f"Service {service_name} is disabled")
        return False

    logger.info(f"Starting registration for {service_name}")

    # Use service registry to get the appropriate service class
    service_class = ServiceRegistry.get_service_class(service_name)
    if not service_class:
        logger.error(f"Service {service_name} implementation not found")
        return False

    try:
        service = service_class(service_config, config)

        # Enable debug mode if requested
        if debug_mode:
            service.debug_mode = True
            logger.info(f"Debug mode enabled for {service_name}")

        result = await service.register_and_get_api_key()

        if result.success and result.api_key:
            from core.models import ServiceCredentials

            # Generic credential creation
            credential_data = {
                "service": service_name,
                "api_key": result.api_key,
                "email": result.email,
                "metadata": {},
            }
            if result.username:
                credential_data["username"] = result.username
            if result.password:
                credential_data["password"] = result.password

            # For ExaAI, handle the search field
            if service_name == "exaai":
                if (
                    hasattr(result, "search_successful")
                    and result.search_successful is not None
                ):
                    credential_data["metadata"]["search"] = result.search_successful
                # ExaAI does not have a username
                if "username" in credential_data:
                    del credential_data["username"]

            credential = ServiceCredentials(**credential_data)

            storage.add_credential(credential)
            logger.info(f"✅ Successfully registered {service_name} and stored API key")
            print(f"\n🎉 Success! Generated API key for {service_name}")
            print(f"   API Key: {result.api_key}")
            print(f"   Email: {result.email}")
            if result.username:
                print(f"   Username: {result.username}")
            if result.password:
                # Mask password for security
                masked_pass = f"{result.password[:3]}...{result.password[-3:]}"
                print(f"   Password: {masked_pass}")
            if (
                hasattr(result, "search_successful")
                and result.search_successful is not None
            ):
                print(
                    f"   Search: {'successful' if result.search_successful else 'failed'}"
                )
            return True
        else:
            logger.error(
                f"❌ Failed to register {service_name}: {result.error_message}"
            )
            print(f"\n❌ Registration failed for {service_name}")
            if result.error_message:
                print(f"   Error: {result.error_message}")
            return False

    except Exception as e:
        logger.error(f"❌ Error during {service_name} registration: {e}")
        print(f"\n❌ Registration failed for {service_name}")
        print(f"   Error: {str(e)}")
        return False


async def list_services(config: Config):
    """List all available services and their status."""
    print("\n📋 Available Services:")
    print("=" * 50)

    supported_services = ServiceRegistry.get_supported_services()

    for name, service_config in config.services.items():
        status = "✅ enabled" if service_config.enabled else "❌ disabled"
        implemented = (
            "🔧 implemented" if name in supported_services else "⚠️  not implemented"
        )

        print(f"  {name}:")
        print(f"    Name: {service_config.name}")
        print(f"    Status: {status}")
        print(f"    Implementation: {implemented}")
        print(f"    Start URL: {service_config.start_url}")
        print(f"    Domain Groups: {', '.join(service_config.email_domain_groups)}")
        print()


async def test_service(
    service_name: str, config: Config, storage: ResultsStorage, debug_mode: bool = False
):
    """Test a specific service registration (e2e verification)."""
    print(f"\n🧪 Testing {service_name} registration...")
    if debug_mode:
        print("🐛 Debug mode: Screenshots and email content will be saved")
    print("=" * 50)

    # Check if service is configured
    if service_name not in config.services:
        available = list(config.services.keys())
        print(f"❌ Service '{service_name}' not found in configuration")
        print(f"Available services: {', '.join(available)}")
        return False

    # Check if service is implemented
    supported = ServiceRegistry.get_supported_services()
    if service_name not in supported:
        print(f"❌ Service '{service_name}' is not yet implemented")
        print(f"Implemented services: {', '.join(supported)}")
        return False

    # Run the registration test
    return await register_service(service_name, config, storage, debug_mode)


async def show_credentials(storage: ResultsStorage):
    """Display stored credentials summary."""
    print("\n🔑 Stored Credentials:")
    print("=" * 50)

    summary = storage.get_service_summary()
    if summary:
        for service, count in summary.items():
            latest = storage.get_latest_credential(service)
            if latest:
                created_at = latest.get("created_at", "Unknown")
                email = latest.get("email", "Unknown")
                api_key = latest.get("api_key", "Unknown")
                # Mask API key for security
                masked_key = (
                    f"{api_key[:8]}...{api_key[-4:]}" if len(api_key) > 12 else api_key
                )

                print(f"  {service}: {count} API key{'s' if count != 1 else ''}")
                print(f"    Latest: {created_at}")
                print(f"    Email: {email}")
                print(f"    API Key: {masked_key}")
                if latest.get("password"):
                    # Mask password for security
                    password = latest.get("password", "")
                    masked_pass = (
                        f"{password[:3]}...{password[-3:]}"
                        if len(password) > 6
                        else "******"
                    )
                    print(f"    Password: {masked_pass}")
                print()
    else:
        print("  No credentials stored yet")


async def main():
    """Main function for e2e testing and verification."""
    parser = argparse.ArgumentParser(
        description="RegBot E2E Testing and Service Verification",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                              # List all services and run interactive mode
  %(prog)s assemblyai                   # Test AssemblyAI registration
  %(prog)s exaai                        # Test ExaAI registration
  %(prog)s exaai --debug                # Test ExaAI with debug mode
  %(prog)s exaai -c config_webkit.yaml  # Test ExaAI with WebKit browser
  %(prog)s --list                       # List all configured services
  %(prog)s --credentials                # Show stored credentials

E2E Testing:
  This script is designed for end-to-end testing and verification of service
  registration flows. Use continuous_main.py for production automation.

Configuration:
  Edit config.yaml to configure services, domains, and automation settings.
  Use -c to specify alternative configuration files for testing.
        """,
    )

    parser.add_argument(
        "service", nargs="?", help="Service name to test (e.g., assemblyai, exaai)"
    )

    parser.add_argument(
        "--list",
        action="store_true",
        help="List all configured services and their status",
    )

    parser.add_argument(
        "--credentials", action="store_true", help="Show stored credentials summary"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode (save screenshots and email content)",
    )

    parser.add_argument(
        "-c",
        "--config",
        type=str,
        default="config.yaml",
        help="Path to configuration file (default: config.yaml)",
    )

    args = parser.parse_args()

    # Load configuration
    config_path = Path(args.config)
    if not config_path.exists():
        print(f"❌ {args.config} not found. Please create configuration file.")
        return 1

    config = Config.load(config_path)
    storage = ResultsStorage()

    print("\n🤖 RegBot Service Registration - E2E Testing")
    print("=" * 60)

    # Handle list command
    if args.list:
        await list_services(config)
        return 0

    # Handle credentials command
    if args.credentials:
        await show_credentials(storage)
        return 0

    # Handle specific service test
    if args.service:
        success = await test_service(args.service, config, storage, args.debug)

        # Show final credentials summary
        await show_credentials(storage)

        return 0 if success else 1

    # Interactive mode - list services and allow selection
    await list_services(config)

    print("💡 Usage Tips:")
    print("  - For e2e testing: python main.py <service_name> [--debug]")
    print("  - For continuous automation: python continuous_main.py <service_name>")
    print("  - For monitoring: python monitor.py --realtime")
    print()

    try:
        service_name = input(
            "Enter service name to test (or press Enter to exit): "
        ).strip()
        if service_name:
            debug_input = input("Enable debug mode? (y/n): ").lower().strip()
            debug_mode = debug_input == "y"
            success = await test_service(service_name, config, storage, debug_mode)
            await show_credentials(storage)
            return 0 if success else 1
    except KeyboardInterrupt:
        print("\nExiting...")

    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
