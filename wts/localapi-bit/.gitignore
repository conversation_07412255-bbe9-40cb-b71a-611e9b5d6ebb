# Python-generated files
__pycache__/
*.py[oc]
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv/
venv/
env/
ENV/
env.bak/
venv.bak/

# Python testing
.coverage
.pytest_cache/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/
htmlcov/

# Python debugging
.pdb
.pyc

# UV package manager
.uv/
uv.lock

# PyCharm / JetBrains IDEs
.idea/
*.swp
*.swo
*~

# VS Code
.vscode/
*.code-workspace
.history/

# Cursor IDE
.cursor/
cursor.log

# Claude Code
.claude/
claude.log
.claude-chat/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Logs
*.log
logs/
*.log.*

# Configuration files with secrets
config.yaml
.env
.env.local
.env.production
.env.staging
.env.development
secrets.yaml
credentials.yaml

# Cloudflare Workers
email-interceptor/wrangler.toml
.wrangler/
worker-configuration.d.ts

# API keys and credentials
api-keys.yaml
*-credentials.json
*.key
*.pem
*.p12
*.crt
*.csr

# Data directories
data/
results/
screenshots/
emails/
webhook_emails/
downloads/

# Temporary files
temp/
tmp/
*.tmp
*.temp
*.swp
*.swo

# Backup files
*.bak
*.backup
*.orig

# Cache directories
.cache/
cache/
.npm/
node_modules/
.yarn/

# Browser profiles and user data
browser-profiles/
user-data-dir/
.chromium/
.firefox/

# Service-specific generated files
*.api-key
registration-results.yaml
automation-state.json

# Test artifacts
test-results/
test-output/
playwright-report/
.playwright/

# Documentation build
docs/_build/
site/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pipenv
Pipfile.lock

# Poetry
poetry.lock

# PDM
pdm.lock
.pdm.toml

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/
