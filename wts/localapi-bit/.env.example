# Environment variables for regbot
# Copy to .env and fill in your values

# Application settings
DEBUG=true
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# Cloudflare Worker settings
WORKER_AUTH_TOKEN=your-secret-token-here
WEBHOOK_BASE_URL=http://localhost:8000

# Email domain for registration
REGISTRATION_DOMAIN=yourdomain.com

# Storage paths
DATA_DIR=./data
KEYS_DIR=./data/keys
CONFIGS_DIR=./data/configs
LOGS_DIR=./data/logs

# Browser automation settings
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000
MAX_RETRIES=3
RETRY_DELAY=5

# Service-specific settings
ASSEMBLYAI_SIGNUP_URL=https://www.assemblyai.com/signup
OPENAI_SIGNUP_URL=https://platform.openai.com/signup