# RegBot - Service Registration Automation

Modern automation system for service registration and API key acquisition with clean architecture and configurable email interception.

## Features

- 🎯 **Generic Email Interception**: Cloudflare Worker captures verification emails
- 🔄 **Flexible Storage**: Choose between webhook server or Cloudflare KV 
- 🖥️ **Browser Automation**: Playwright-based registration workflows with human-like behavior
- ⚖️ **Load Balancing**: Multi-domain email distribution with weights
- 🔧 **Configuration-Driven**: Single YAML file for all settings
- 📦 **Extensible**: Easy to add new services
- 🤖 **Continuous Automation**: Automated registration sessions with randomization
- 📊 **Real-time Monitoring**: Beautiful ASCII dashboard with analytics
- 💾 **Session Persistence**: Automatic save/resume capabilities

## Quick Start

### 1. Setup Configuration
```bash
# Copy example configuration
cp docs/config.example.yaml config.yaml

# Edit config.yaml with your domains and settings
vim config.yaml
```

### 2. Cloudflare Prerequisites

Before deploying the email worker, you must configure your domains in Cloudflare:

#### 2.1. Add Email Domains to Cloudflare
1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com)
2. Add each domain from your `config.yaml` to Cloudflare (if not already added)
3. Ensure domains are active and DNS is managed by Cloudflare

#### 2.2. Enable Email Routing
For each domain in your configuration:
1. Go to **Domain** → **Email** → **Email Routing**
2. Click **"Get Started"** if not already enabled
3. Verify your domain ownership if prompted
4. Note: Email Routing must be enabled before creating rules

#### 2.3. Configure Destination Addresses
1. In Email Routing, go to **"Destination addresses"**
2. Add a fallback email address (your real email for troubleshooting)
3. Verify the destination address via the email sent to you

#### 2.4. Create Catch-All Routing Rules
For each domain that will receive service emails:
1. Go to **"Routing rules"**
2. Create a **catch-all rule**:
   - **Expression**: `*` (catches all emails)
   - **Action**: **"Send to Worker"**
   - **Worker**: Select your deployed worker name (must match `name` in `wrangler.toml`)
3. **Important**: Worker must be deployed first, or create rule after deployment
4. Add a **fallback rule** (lower priority):
   - **Expression**: `*`  
   - **Action**: **"Send to email"**
   - **Destination**: Your verified fallback email address

### 3. Deploy Email Interceptor

**Important**: Ensure your worker name in `email-interceptor/wrangler.toml` matches what you'll select in Cloudflare routing rules.

```bash
# Sync configuration and deploy worker (combined)
uv run --script emailworker-ready

# Or step by step:
uv run --script sync-worker-config
uv run --script worker-deploy
```

**Verification**: After deployment, verify the worker appears in your Cloudflare Dashboard under **Workers & Pages**.

### 4. Run Automation

#### Single Registration Mode
```bash
# Development mode (with webhook server)
uv run --script dev &  # Starts webhook server
uv run python main.py  # Run AssemblyAI automation

# Production mode (with KV storage)
# Set client_type: "kv" in config.yaml
uv run python main.py
```

#### Continuous Automation Mode
```bash
# Start continuous automation for AssemblyAI (default: 10 registrations)
uv run python continuous_main.py assemblyai

# Start with custom target
uv run python continuous_main.py assemblyai --target 50

# Check session status
uv run python continuous_main.py --status
```

#### Real-time Monitoring
```bash
# Start beautiful monitoring dashboard
uv run python monitor.py --realtime

# Generate analytics report
uv run python monitor.py --report

# Quick status check
uv run python monitor.py --status

# System health check
uv run python monitor.py --health
```

## Continuous Automation

RegBot includes an advanced continuous automation system that can run unattended registration sessions with human-like behavior patterns.

### Features

- **🕐 Randomized Intervals**: Configurable random delays between registrations (30-90 minutes default)
- **🎭 Human-like Behavior**: Random mouse movements, typing delays, scrolling patterns
- **🎯 Goal-based Execution**: Automatically stops when target registrations are achieved
- **💾 Session Persistence**: Sessions survive restarts and can be resumed
- **🚨 Failure Handling**: Automatic retry with exponential backoff and max failure limits
- **🔄 Load Balancing**: Intelligent email domain rotation across multiple providers
- **📊 Real-time Monitoring**: Live dashboard with progress tracking and analytics

### Basic Usage

```bash
# Start continuous automation for a service
uv run python continuous_main.py <service> [options]

# Examples:
uv run python continuous_main.py assemblyai                # Default: 10 registrations
uv run python continuous_main.py assemblyai --target 100   # Target: 100 registrations
uv run python continuous_main.py exaai --target 25         # Different service
```

### Monitoring Commands

```bash
# Real-time dashboard (updates every 5 seconds)
uv run python monitor.py --realtime

# Quick status check
uv run python monitor.py --status

# System health and configuration validation
uv run python monitor.py --health

# Comprehensive analytics report
uv run python monitor.py --report

# Custom refresh rate for real-time monitoring
uv run python monitor.py --realtime --refresh 3
```

### Configuration

Edit `config.yaml` to customize continuous automation behavior:

```yaml
continuous_automation:
  # Session timing (in minutes)
  min_interval_minutes: 30      # Minimum wait between registrations
  max_interval_minutes: 90      # Maximum wait between registrations
  
  # Randomization settings (in seconds/milliseconds)
  page_delay_min_seconds: 2     # Min delay between page actions
  page_delay_max_seconds: 8     # Max delay between page actions
  typing_delay_min_ms: 50       # Min delay between keystrokes
  typing_delay_max_ms: 200      # Max delay between keystrokes
  
  # Goal and failure settings
  target_registrations: 10      # Default target for sessions
  max_failed_attempts: 3        # Max consecutive failures before stopping
  
  # Human-like behavior features
  enable_mouse_movements: true      # Random mouse movements
  enable_scroll_simulation: true    # Random scrolling patterns
  enable_user_agent_rotation: true  # Rotate browser user agents
  enable_viewport_randomization: true  # Random browser window sizes
  
  # Session management
  session_persistence_enabled: true    # Auto-save/resume sessions
  max_session_duration_hours: 12      # Max session lifetime
  enable_graceful_shutdown: true      # Clean exit on Ctrl+C
```

### Session Management

Continuous automation sessions are persistent and can survive system restarts:

- **Auto-save**: Session state is automatically saved every few minutes
- **Resume**: Restart the same command to resume an interrupted session
- **Status**: Check progress with `uv run python continuous_main.py --status`
- **Clean Exit**: Press `Ctrl+C` for graceful shutdown
- **Recovery**: Sessions older than `max_session_duration_hours` are automatically discarded

### Monitoring Dashboard

The real-time monitoring dashboard provides a comprehensive view of your automation:

```
    🤖 RegBot Monitoring Dashboard 🤖
    ┌─────────────────────────────────┐
    │          ╭─────────╮          │
    │          │  ◉   ◉  │          │
    │          │    ∩    │          │
    │          ├─────────┤          │
    │          │ MONITOR │          │
    │          └─────────┘          │
    └─────────────────────────────────┘

┌─ 🔄 Current Session Status
│
│    Service: ASSEMBLYAI
│    Status: ✅ RUNNING
│    Progress: ████████░░░░░░░░░░░ 8/10 (80.0%)
│    Success Rate: 87.5%
│    Next Run: in 23 minutes
│
┌─ 🔑 Service Credentials
│
│    ASSEMBLYAI: 15 API keys
│      Latest: 2025-08-02 14:30:15 (<EMAIL>)
│
┌─ 🌐 Domain Load Distribution
│
│    PRIMARY: 1/1 enabled
│      └── ai.example.com: 45.5% (weight: 50)
│    IAD1: 1/1 enabled  
│      └── iad.example.com: 27.3% (weight: 30)
│    SIN1: 1/1 enabled
│      └── sg.example.com: 27.3% (weight: 30)
```

### Human-like Behavior

The system includes sophisticated randomization to avoid detection:

- **Variable Intervals**: Each registration happens after a random delay (30-90 minutes default)
- **Typing Patterns**: Random typing speed with occasional typos and corrections
- **Mouse Movements**: Curved, natural mouse movements with random pauses
- **Browser Fingerprinting**: Rotates user agents, viewport sizes, timezones, and locales
- **Page Interactions**: Random scrolling, reading pauses, and interaction delays
- **Stealth Features**: Advanced browser configuration to avoid automation detection

## Architecture

RegBot uses a clean separation of concerns:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Email Sources  │───▶│ Email Interceptor│───▶│ Storage/Retrieval│
│                 │    │ (Cloudflare      │    │ Mechanisms      │
│ • Service emails│    │  Worker)         │    │                 │
│ • Magic links   │    │                  │    │ • Webhook Server│
│ • Verification  │    │                  │    │ • Cloudflare KV │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Service         │◀───│ Email Client    │
                       │ Automation      │    │ Factory         │
                       │                 │    │                 │
                       │ • AssemblyAI    │    │ • Config-based  │
                       │ • Future services│    │   switching     │
                       └─────────────────┘    └─────────────────┘
```

### Core Components

- **Email Interceptor**: Generic Cloudflare Worker for email capture
- **Storage Mechanisms**: Interchangeable webhook server or KV client
- **Email Client Factory**: Configuration-based client selection
- **Service Automation**: Service-specific registration and parsing logic
- **Continuous Automation Manager**: Session management and randomization
- **Real-time Monitoring**: Analytics and dashboard system

## Available Commands

RegBot provides convenient hatch scripts for common operations:

```bash
# Development workflows
uv run --script dev                    # Start webhook server
uv run --script emailworker-ready      # Sync config + deploy worker
uv run --script sync-worker-config     # Sync configuration to worker
uv run --script worker-deploy          # Deploy Cloudflare Worker

# Single registration automation
uv run python main.py                  # Run AssemblyAI automation

# Continuous automation
uv run python continuous_main.py <service> [--target N]
uv run python continuous_main.py --status

# Monitoring and analytics
uv run python monitor.py --realtime    # Real-time dashboard
uv run python monitor.py --status      # Quick status
uv run python monitor.py --health      # System health check
uv run python monitor.py --report      # Analytics report

# Configuration management
uv run python sync_worker_config.py    # Sync and validate config
```

## Configuration

### Email Retrieval Modes

**Development Mode (Webhook)**:
```yaml
email_retrieval:
  client_type: "webhook"
  webhook:
    local_baseurl: "http://localhost:8888"
    external_baseurl: "https://your-tunnel.zrok.io"
```

**Production Mode (KV)**:
```yaml
email_retrieval:
  client_type: "kv"

cloudflare_account_id: "your-account-id"
cloudflare_namespace_id: "your-namespace-id"
cloudflare_api_token: "your-api-token"
```

### Email Domain Groups

Configure reusable domain groups with load balancing:

```yaml
email_domain_groups:
  primary:
    - domain: "ai.yourdomain.com"
      weight: 50
      enabled: true
    - domain: "reg.yourdomain.com"
      weight: 30
      enabled: true
  
  backup:
    - domain: "backup.yourdomain.com"
      weight: 100
      enabled: true

services:
  assemblyai:
    email_domain_groups: ["primary", "backup"]
    sender_domains: ["assemblyai.com"]
    enabled: true
```

## Project Structure

```
regbot/
├── config.yaml                    # Main configuration
├── main.py                        # Single registration script
├── continuous_main.py             # Continuous automation entry point
├── monitor.py                     # Real-time monitoring dashboard
├── webhook_server.py              # HTTP email storage server
├── sync_worker_config.py          # Config sync script
│
├── core/                          # Core infrastructure
│   ├── email_client_factory.py    # Client factory pattern
│   ├── webhook_client.py          # Webhook HTTP client
│   ├── kv_client.py              # Cloudflare KV client
│   ├── domain_selector.py        # Email domain load balancing
│   ├── browser.py                # Enhanced browser with randomization
│   ├── continuous_automation.py   # Session management and automation
│   └── monitoring.py             # Analytics and monitoring system
│
├── services/                     # Service-specific automation
│   ├── assemblyai.py             # AssemblyAI service implementation
│   └── exaai.py                  # ExaAI service (example)
│
├── email-interceptor/            # Cloudflare Worker
│   ├── src/index.js              # Worker entry point
│   └── wrangler.toml             # Worker configuration
│
└── docs/                         # Documentation
    ├── ARCHITECTURE.md           # System architecture
    ├── CONFIG_GUIDE.md           # Configuration guide
    ├── DEPLOYMENT.md             # Deployment guide
    ├── email-interceptor.md      # Worker documentation
    └── config.example.yaml       # Configuration template
```

## Usage Examples

### Development Workflow
```bash
# 1. Start webhook server
uv run --script dev

# 2. Setup tunnel for worker (in another terminal)
zrok share public http://localhost:8888

# 3. Update config with tunnel URL and deploy worker
uv run --script emailworker-ready

# 4. Run automation (in another terminal)
uv run python main.py
```

### Continuous Automation Workflow
```bash
# 1. Configure and deploy worker
uv run python sync_worker_config.py
cd email-interceptor && pnpm wrangler deploy

# 2. Start continuous automation
uv run python continuous_main.py assemblyai --target 50

# 3. Monitor in real-time (separate terminal)
uv run python monitor.py --realtime

# 4. Check status anytime
uv run python monitor.py --status
```

### Production Workflow
```bash
# 1. Configure KV client in config.yaml
# 2. Sync and deploy worker
uv run --script emailworker-ready

# 3. Run continuous automation (no webhook server needed)
uv run python continuous_main.py assemblyai --target 100
```

### Adding New Services

1. **Add to configuration**:
```yaml
services:
  newservice:
    name: "New Service"
    start_url: "https://newservice.com/signup"
    email_domain_groups: ["primary"]
    sender_domains: ["newservice.com"]
    enabled: true
```

2. **Create service module**:
```python
# services/newservice.py
class NewService:
    @staticmethod
    def parse_magic_link(raw_email_content: str) -> Optional[str]:
        # Service-specific parsing logic
        pass
    
    async def register_and_get_api_key(self) -> RegistrationResult:
        # Service-specific automation logic
        pass
```

3. **Update service registry**:
```python
# core/service_registry.py
_services = {
    'assemblyai': 'services.assemblyai.AssemblyAIService',
    'newservice': 'services.newservice.NewService'
}
```

4. **Deploy**:
```bash
uv run --script emailworker-ready
```

## Key Features

### Interchangeable Storage
- Switch between webhook server and KV client via configuration
- No code changes required for different environments
- Identical interfaces for both storage mechanisms

### Service-Specific Logic
- Each service handles its own email parsing patterns
- Supports various authentication flows (magic links, codes, multi-step)
- Extensible architecture for adding new services

### Configuration-Driven
- Single `config.yaml` for all configuration
- Automatic worker configuration sync
- Environment-specific settings

### Email Domain Management
- Multiple email domains with weighted load balancing
- Domain groups for different environments
- Automatic domain rotation for rate limiting

### Advanced Automation
- Continuous operation with human-like behavior
- Sophisticated randomization and anti-detection
- Session persistence and recovery
- Real-time monitoring and analytics

## Documentation

- **[📋 Configuration Guide](docs/CONFIG_GUIDE.md)** - Complete configuration reference
- **[🏗️ Architecture](docs/ARCHITECTURE.md)** - System design and components
- **[🚀 Deployment Guide](docs/DEPLOYMENT.md)** - Setup and deployment instructions
- **[📧 Email Interceptor](docs/email-interceptor.md)** - Worker documentation
- **[⚙️ Configuration Example](docs/config.example.yaml)** - Configuration template

## Requirements

- **Python 3.11+** with UV package manager
- **Node.js** with pnpm for worker deployment
- **Cloudflare Account** with Email Routing enabled
- **Domain(s)** configured in Cloudflare with active DNS
- **Email Routing** enabled for each domain
- **Verified destination address** for fallback routing

## Common Setup Issues

### Email Not Received
1. **Check Email Routing**: Ensure Email Routing is enabled for your domain
2. **Verify DNS**: Domain must be active in Cloudflare with nameservers configured
3. **Check Rules**: Verify catch-all rule exists and points to correct worker
4. **Worker Logs**: Run `cd email-interceptor && pnpm wrangler tail` to see worker activity
5. **Fallback Rule**: Ensure fallback rule sends to verified destination address

### Worker Not Found in Rules
1. **Deploy First**: Worker must be deployed before it appears in routing rule options
2. **Name Match**: Worker name in `wrangler.toml` must match selection in rules
3. **Account**: Ensure you're in the correct Cloudflare account

### Configuration Issues
1. **Domain Mismatch**: Domains in `config.yaml` must match Cloudflare domains
2. **Webhook URL**: For development, ensure tunnel URL is accessible
3. **KV Namespace**: For production, ensure KV namespace exists and is bound

### Continuous Automation Issues
1. **Session Not Resuming**: Check `data/sessions/` directory permissions
2. **Randomization Disabled**: Verify `enable_randomization` settings in config
3. **High Failure Rate**: Adjust timing settings or check email domain health
4. **Monitor Not Working**: Ensure config.yaml is properly formatted

## Getting Help

1. Check the [Configuration Guide](docs/CONFIG_GUIDE.md) for setup issues
2. Review [Troubleshooting](docs/DEPLOYMENT.md#troubleshooting) section
3. Check worker logs: `cd email-interceptor && pnpm wrangler tail`
4. Test configuration: `uv run python sync_worker_config.py`
5. Check system health: `uv run python monitor.py --health`
6. Monitor real-time: `uv run python monitor.py --realtime`

## License

MIT License - see LICENSE file for details.