# RegBot Architecture

RegBot is a service registration automation system designed with a clean separation of concerns between email interception, storage/retrieval, and automation logic.

## High-Level Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Email Sources  │───▶│ Email Interceptor│───▶│ Storage/Retrieval│
│                 │    │ (<PERSON>flare      │    │ Mechanisms      │
│ • Service emails│    │  Worker)         │    │                 │
│ • Magic links   │    │                  │    │ • Webhook Server│
│ • Verification  │    │                  │    │ • Cloudflare KV │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Service         │◀───│ Email Client    │
                       │ Automation      │    │ Factory         │
                       │                 │    │                 │
                       │ • AssemblyAI    │    │ • Config-based  │
                       │ • Future services│    │   switching     │
                       └─────────────────┘    └─────────────────┘
```

## Core Components

### 1. Email Interceptor (Cloudflare Worker)

**Location**: `email-interceptor/`
**Purpose**: Generic email capture and forwarding

**Key Features**:
- Service-agnostic email interception
- Environment variable-based service detection
- Configurable forwarding to webhook or KV storage
- No hardcoded parsing logic

**Flow**:
1. Receives emails via Cloudflare Email Routing
2. Detects service based on sender domain patterns
3. Forwards raw email content to configured storage mechanism

### 2. Storage/Retrieval Mechanisms

#### Webhook Server (`webhook_server.py`)
- **Purpose**: HTTP-based email storage and retrieval
- **Use Case**: Local development, environments where HTTP servers can run
- **Interface**: REST API with endpoints for storing/retrieving emails

#### Cloudflare KV Client (`core/kv_client.py`)
- **Purpose**: Direct Cloudflare KV access for email retrieval
- **Use Case**: Serverless environments, production deployments
- **Interface**: Same methods as webhook client for interchangeability

#### Email Client Factory (`core/email_client_factory.py`)
- **Purpose**: Configuration-based client selection
- **Configuration**: `config.yaml` → `email_retrieval.client_type`
- **Clients**: Automatically instantiates webhook or KV client

### 3. Service Automation

**Location**: `services/`
**Purpose**: Service-specific registration and API key extraction

**Current Implementation**:
- `assemblyai.py`: AssemblyAI registration automation
- Email parsing logic resides here (not in storage mechanisms)
- Browser automation using Playwright
- Magic link parsing and navigation

## Project Structure

```
regbot/
├── config.yaml                    # Main configuration
├── pyproject.toml                 # Python project config
├── sync_worker_config.py          # Config sync script
├── webhook_server.py              # HTTP email storage server
├── run_automation.py              # Main automation script
│
├── core/                          # Core infrastructure
│   ├── browser.py                 # Playwright browser management
│   ├── config.py                  # Configuration loading
│   ├── email_client_factory.py    # Client factory pattern
│   ├── webhook_client.py          # Webhook HTTP client
│   ├── kv_client.py              # Cloudflare KV client
│   ├── domain_selector.py        # Email domain load balancing
│   ├── profile.py                # Profile generation
│   └── models.py                 # Data models
│
├── services/                     # Service-specific automation
│   └── assemblyai.py             # AssemblyAI service implementation
│
├── email-interceptor/            # Cloudflare Worker
│   ├── src/index.js              # Worker entry point
│   ├── wrangler.toml             # Worker configuration
│   └── package.json              # Worker dependencies
│
└── docs/                         # Documentation
    ├── ARCHITECTURE.md           # This file
    ├── CONFIG_GUIDE.md           # Configuration guide
    ├── email-interceptor.md      # Worker documentation
    ├── DEPLOYMENT.md             # Deployment guide
    └── config.example.yaml       # Configuration template
```

## Key Design Principles

### 1. **Separation of Concerns**
- **Email Interceptor**: Only captures and forwards emails
- **Storage Mechanisms**: Only store and retrieve emails
- **Service Automation**: Contains all parsing and business logic

### 2. **Interchangeable Components**
- Webhook server and KV client have identical interfaces
- Switch between them via configuration
- No code changes required for different environments

### 3. **Service-Specific Logic**
- Each service handles its own email parsing patterns
- Supports various authentication flows (magic links, codes, multi-step)
- Extensible architecture for adding new services

### 4. **Configuration-Driven**
- Single `config.yaml` for all configuration
- Environment-specific settings
- Automatic worker configuration sync

## Data Flow

### Registration Flow
1. **Profile Generation**: Create email and user profile
2. **Service Navigation**: Browser automation to service signup page
3. **Email Submission**: Submit email address for verification
4. **Email Interception**: Worker captures verification email
5. **Email Storage**: Store in webhook server or KV
6. **Email Retrieval**: Service automation fetches email
7. **Email Parsing**: Service-specific magic link extraction
8. **Link Navigation**: Browser follows magic link
9. **API Key Extraction**: Extract and store API credentials

### Configuration Sync Flow
1. **Config Update**: Modify `config.yaml`
2. **Sync Script**: Run `sync_worker_config.py`
3. **Worker Update**: Generate `wrangler.toml` with environment variables
4. **Worker Deploy**: Deploy updated worker configuration

## Scalability Considerations

### Adding New Services
1. Create new service module in `services/`
2. Implement service-specific parsing methods
3. Add service configuration to `config.yaml`
4. Update sync script for new sender domains

### Multi-Environment Support
- **Development**: Use webhook server for easy debugging
- **Production**: Use KV client for serverless deployment
- **Hybrid**: Mix environments based on needs

### Email Domain Management
- Multiple email domains with weighted load balancing
- Domain groups for different environments
- Automatic domain rotation for rate limiting

## Security Considerations

### Configuration Security
- `config.yaml` excluded from git (contains sensitive data)
- `config.example.yaml` as template
- Environment-specific credential management

### Email Security
- Raw email content stored temporarily
- Automatic cleanup mechanisms
- No persistent storage of sensitive data

### API Key Security
- Local YAML storage for development
- Configurable storage backends
- No hardcoded credentials in code