# RegBot Configuration Example
# Copy this to config.yaml and customize for your setup

# Define reusable email domain groups
email_domain_groups:
  primary:
    - domain: "ai.yourdomain.com"
      weight: 50
      enabled: true
    - domain: "reg.yourdomain.com"
      weight: 30
      enabled: true
  
  backup:
    - domain: "backup.yourdomain.com"
      weight: 100
      enabled: false  # Enable when domain is configured
  
  development:
    - domain: "dev.yourdomain.com"
      weight: 100
      enabled: false  # For testing

# Email retrieval configuration
email_retrieval:
  # Client type: "webhook" or "kv"
  # webhook: Use webhook server for email retrieval (requires running webhook server)
  # kv: Use Cloudflare KV direct access (requires KV credentials below)
  client_type: "webhook"
  
  # Webhook configuration (used when client_type is "webhook")
  webhook:
    local_baseurl: "http://localhost:8888"  # Used by automation scripts
    external_baseurl: "https://your-tunnel.zrok.io"  # External tunnel URL for Cloudflare worker

# Email interceptor worker configuration
email_interceptor:
  worker_name: "regbot-emailparser"
  store_raw_email: true  # Store raw email content for debugging
  forward_unknown: true  # Forward emails from unknown services
  forward_email: "<EMAIL>"  # Backup email address

# Cloudflare KV settings (required only if using KV client)
cloudflare_account_id: ""  # Your Cloudflare account ID
cloudflare_namespace_id: ""  # KV namespace ID for email storage
cloudflare_api_token: ""  # API token with KV read permissions

# Global browser settings (can be overridden per service)
browser_timeout: 30000  # 30 seconds default timeout
browser_engines:  # Global default browser engines (can be overridden per service)
  - chromium
  - firefox
  - webkit

# Data storage path
data_path: "data"  # Base path for all data storage (results, screenshots, etc.)

# Service configurations
services:
  assemblyai:
    name: "AssemblyAI"
    start_url: "https://www.assemblyai.com/dashboard/login"
    # Use email domains from groups
    email_domain_groups: ["primary"]  # Will use domains from the specified groups
    # Email parsing configuration for interceptor
    sender_domains: ["assemblyai.com"]  # Matches assemblyai.com and all subdomains (*.assemblyai.com)
    # Service-specific browser settings (must define headless per service)
    headless: false  # Required: define headless mode per service
    browser_engines:  # Override global browser_engines for this service
      - chromium
      - firefox
    wait_times:  # Service-specific timing configuration
      page_load: 3000
      after_login: 5000
      verification_check: 10000
    enabled: true

  exaai:
    name: "ExaAI"
    start_url: "https://dashboard.exa.ai/login"
    email_domain_groups: ["primary"]
    sender_domains: ["exa.ai"]
    # Required: define headless mode per service
    headless: false
    wait_times:
      page_load: 3000
      after_signup: 5000
    enabled: false  # Enable when ready to use

  firecrawl:
    name: "Firecrawl"
    start_url: "https://www.firecrawl.dev/signin/signup"
    email_domain_groups: ["primary"]
    sender_domains: ["firecrawl.dev", "service.firecrawl.dev"]
    # Service-specific browser settings with BitBrowser integration
    headless: false  # Required: define headless mode per service
    browser_engines:  # Service-specific engine configuration
      - chromium
      - bit:
          browser_id: "your_bitbrowser_id_here"
          credentials:
            enabled: true
            token: 1234567890
          api_url: "http://127.0.0.1:54345"
    wait_times:
      page_load: 3000
      after_signup: 5000
      verification_check: 10000
    enabled: true

  # BitBrowser-only service example
  someservice:
    name: "Some Service"
    start_url: "https://example.com/signup"
    email_domain_groups: ["primary"]
    sender_domains: ["example.com"]
    # Uses only BitBrowser with specific configuration
    headless: true  # Required: define headless mode per service
    browser_engines:
      - bit:
          browser_id: "another_browser_id"
          credentials:
            enabled: false  # Disable credentials for this browser
          api_url: "http://127.0.0.1:54345"
    enabled: false

# Continuous automation settings (optional)
continuous_automation:
  # Interval between registration attempts (in minutes)
  min_interval_minutes: 30
  max_interval_minutes: 90
  
  # Goal settings
  target_registrations: 10
  max_failed_attempts: 3
  
  # Session persistence - saves progress to resume later if interrupted
  session_persistence_enabled: true

# Setup Instructions:
# 1. Copy this file: cp docs/config.example.yaml config.yaml
# 2. Configure your email domains in Cloudflare Email Routing
# 3. Update email domain groups with your actual domains
# 4. Set external_baseurl to your tunnel URL (zrok, ngrok, etc.)
# 5. Choose client_type based on your deployment environment
# 6. For BitBrowser: Update browser_id with your actual BitBrowser profile IDs
# 7. Enable services by setting enabled: true
# 8. Run configuration sync: uv run python sync_worker_config.py
# 9. Deploy worker: cd email-interceptor && pnpm wrangler deploy

# Configuration Notes:
# - email_domain_groups: Define once, reuse across services
# - sender_domains: Used by worker for service detection
# - headless: REQUIRED per service (no global default)
# - browser_engines: Support simple strings (chromium, firefox, webkit) or complex configs (bit: {...})
# - BitBrowser integration: Use 'bit' engine with browser_id, credentials, and api_url
# - Service overrides: browser_engines, wait_times can override global settings
# - All email parsing logic is in services/*.py files, not in config