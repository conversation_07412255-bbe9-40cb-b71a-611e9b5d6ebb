# RegBot Configuration Guide

Complete guide to configuring RegBot for service registration automation.

## Quick Start

1. **Copy the example configuration:**
   ```bash
   cp docs/config.example.yaml config.yaml
   ```

2. **Edit config.yaml with your settings**

3. **Run configuration sync:**
   ```bash
   uv run python sync_worker_config.py
   ```

## Configuration Structure

### Email Retrieval Configuration

Choose between webhook server and Cloudflare KV for email retrieval:

```yaml
email_retrieval:
  # Client type: "webhook" or "kv"
  client_type: "webhook"
  
  # Webhook configuration (for webhook client)
  webhook:
    local_baseurl: "http://localhost:8888"
    external_baseurl: "https://your-tunnel.zrok.io"
```

**Client Types:**
- **`webhook`**: Use local webhook server (development, debugging)
- **`kv`**: Use Cloudflare KV direct access (production, serverless)

### Email Domain Groups

Define reusable groups of email domains with load balancing:

```yaml
email_domain_groups:
  primary:
    - domain: "ai.yourdomain.com"
      weight: 50
      enabled: true
    - domain: "reg.yourdomain.com"
      weight: 30
      enabled: true
  
  backup:
    - domain: "backup.yourdomain.com"
      weight: 100
      enabled: true
```

**Domain Properties:**
- **`domain`**: Email domain for receiving verification emails
- **`weight`**: Load balancing weight (higher = more likely to be selected)
- **`enabled`**: Whether this domain is active

### Service Configuration

Configure each service with domain groups and settings:

```yaml
services:
  assemblyai:
    name: "AssemblyAI"
    start_url: "https://www.assemblyai.com/dashboard/login"
    email_domain_groups: ["primary", "backup"]
    sender_domains: ["assemblyai.com"]
    enabled: true
```

**Service Properties:**
- **`name`**: Human-readable service name
- **`start_url`**: URL to begin registration process
- **`email_domain_groups`**: Which domain groups this service can use
- **`sender_domains`**: Email domains this service sends from (for worker detection)
- **`enabled`**: Whether this service is active

### Email Interceptor Configuration

Configure the Cloudflare Worker behavior:

```yaml
email_interceptor:
  worker_name: "regbot-emailparser"
  store_raw_email: true
  forward_unknown: true
  forward_email: "<EMAIL>"
```

### Cloudflare KV Settings

Required only if using KV client:

```yaml
cloudflare_account_id: "your-account-id"
cloudflare_namespace_id: "your-namespace-id"
cloudflare_api_token: "your-api-token"
```

### Browser Settings

Configure browser automation behavior:

```yaml
browser_headless: false  # Set true for production
browser_timeout: 30000   # 30 seconds
```

### Data Storage

Configure where results are stored:

```yaml
data_path: "data"  # Base path for screenshots, results, etc.
```

## Configuration Examples

### Development Setup
```yaml
email_retrieval:
  client_type: "webhook"  # Easy debugging
  webhook:
    local_baseurl: "http://localhost:8888"

browser_headless: false  # See browser actions
```

### Production Setup
```yaml
email_retrieval:
  client_type: "kv"  # Serverless friendly

browser_headless: true  # No GUI needed
```

### Multi-Domain Load Balancing
```yaml
email_domain_groups:
  high_capacity:
    - domain: "reg1.fastdomain.com"
      weight: 40
      enabled: true
    - domain: "reg2.fastdomain.com"
      weight: 40
      enabled: true
    - domain: "reg3.fastdomain.com"
      weight: 20
      enabled: true

services:
  assemblyai:
    email_domain_groups: ["high_capacity"]
```

### Service-Specific Domains
```yaml
email_domain_groups:
  assemblyai_only:
    - domain: "ai.dedicated.com"
      weight: 100
      enabled: true
  
  shared:
    - domain: "general.shared.com"
      weight: 100
      enabled: true

services:
  assemblyai:
    email_domain_groups: ["assemblyai_only", "shared"]  # Dedicated first, shared fallback
```

## Configuration Workflow

### 1. Initial Setup
1. Copy `docs/config.example.yaml` to `config.yaml`
2. Configure email domains (must have Email Routing enabled)
3. Set webhook or KV configuration
4. Configure services you want to automate

### 2. Worker Configuration Sync
```bash
# Sync config to worker
uv run python sync_worker_config.py

# Deploy worker
cd email-interceptor
pnpm wrangler deploy
```

### 3. Testing Configuration
```bash
# Test domain selection
uv run python -c "
from core.config import Config
config = Config.load()
domains = config.get_domains_for_service('assemblyai')
print([d.domain for d in domains])
"

# Test email client
uv run python -c "
from core.email_client_factory import EmailClientFactory
client = EmailClientFactory.create_client()
print(f'Using: {type(client).__name__}')
"
```

## Security Best Practices

### Configuration Security
- **Never commit `config.yaml`** (contains sensitive data)
- Use `config.example.yaml` as template
- Store API tokens in environment variables when possible

### Email Domain Security
- Use dedicated domains for automation
- Enable Cloudflare Email Routing security features
- Monitor email traffic for abuse

### API Key Security
- Store API keys in secure location
- Implement key rotation policies
- Use least-privilege access patterns

## Troubleshooting

### Common Issues

**Worker not receiving emails:**
1. Check Email Routing configuration in Cloudflare
2. Verify sender domains in config match actual senders
3. Check worker logs for errors

**Email client connection issues:**
1. Verify webhook server is running (for webhook client)
2. Check KV credentials (for KV client)
3. Test client configuration with factory

**Domain selection issues:**
1. Ensure domains are enabled
2. Check domain group assignments
3. Verify email routing for selected domains

### Debug Commands
```bash
# Check worker configuration
cat email-interceptor/wrangler.toml

# Test configuration loading
uv run python -c "from core.config import Config; print(Config.load())"

# Test email client factory
uv run python -c "from core.email_client_factory import EmailClientFactory; EmailClientFactory.create_client()"
```

## Advanced Configuration

### Custom Service Implementation
1. Create new service module in `services/`
2. Add service configuration to `config.yaml`
3. Update `sync_worker_config.py` if needed
4. Implement service-specific parsing logic

### Multi-Environment Support
Use different config files for different environments:
```bash
# Development
cp config.dev.yaml config.yaml

# Production  
cp config.prod.yaml config.yaml
```

### Email Parsing Customization
Email parsing logic is implemented in each service module (`services/`), not in configuration. This allows for:
- Complex parsing patterns
- Multi-step authentication flows
- Service-specific handling of different email types