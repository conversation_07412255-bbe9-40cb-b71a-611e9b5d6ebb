#!/usr/bin/env python3
"""Setup script for RegBot automation system."""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, description):
    """Run command with error handling."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(
            cmd, shell=True, check=True, capture_output=True, text=True
        )
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False


def main():
    """Main setup function."""
    print("🚀 Setting up RegBot automation environment...")
    print("=" * 60)

    # Check if we're in the right directory
    if not Path("pyproject.toml").exists():
        print("❌ Please run this script from the RegBot project root directory")
        sys.exit(1)

    # Install Python dependencies
    if not run_command("uv sync", "Installing Python dependencies"):
        sys.exit(1)

    # Install Playwright browsers
    if not run_command(
        "uv run playwright install", "Installing Playwright browsers (all engines)"
    ):
        print("⚠️  Falling back to Chromium only...")
        if not run_command(
            "uv run playwright install chromium", "Installing Chromium browser"
        ):
            sys.exit(1)

    # Check for config file
    config_path = Path("config.yaml")
    if not config_path.exists():
        print("⚠️  config.yaml not found - you'll need to create one")
        print("   Example configuration will be generated on first run")
    else:
        print("✅ config.yaml found")

    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("   1. Configure config.yaml with your email domains and services")
    print("   2. Start webhook server: uv run uvicorn webhook_server:app --port 8888")
    print("   3. Test a service: uv run python main.py exaai --debug")
    print("\n🔧 Available commands:")
    print("   • uv run python main.py --list          # List all services")
    print("   • uv run python main.py exaai           # Test ExaAI registration")
    print("   • uv run python main.py exaai --debug   # Test with screenshots")
    print("\n🌐 Browser engines installed:")
    print("   • Chromium (primary)")
    print("   • Firefox (fallback)")
    print("   • WebKit (Safari-like, if supported)")


if __name__ == "__main__":
    main()
