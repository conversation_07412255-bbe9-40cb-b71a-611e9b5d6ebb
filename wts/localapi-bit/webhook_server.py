"""Webhook server to receive and store emails from Cloudflare Email Worker."""

from datetime import datetime
from typing import Dict
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import logging
from urllib.parse import unquote

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Store emails in memory (could be Redis in production)
emails_store: Dict[str, Dict] = {}

app = FastAPI(title="RegBot Webhook Server")


class EmailData(BaseModel):
    """Email data from Cloudflare Worker."""

    from_address: str = ""
    to_address: str = ""
    subject: str = ""
    timestamp: str = ""
    service: str = ""
    sender_domain: str = ""
    email_domain: str = ""
    headers: Dict[str, str] = {}
    raw: str = ""


@app.post("/webhook/email")
async def receive_email(email_data: EmailData):
    """Receive and store email data from Cloudflare Worker - no parsing."""
    try:
        # Extract service and email user
        to_user = (
            email_data.to_address.split("@")[0] if email_data.to_address else "unknown"
        )

        # Store the complete email data by service and email user
        key = f"{email_data.service}_{to_user}"
        emails_store[key] = {
            "from_address": email_data.from_address,
            "to_address": email_data.to_address,
            "subject": email_data.subject,
            "timestamp": email_data.timestamp,
            "service": email_data.service,
            "sender_domain": email_data.sender_domain,
            "email_domain": email_data.email_domain,
            "headers": email_data.headers,
            "raw": email_data.raw,  # Store raw email content for service automation to parse
        }

        logger.info(
            f"Stored email for {email_data.service}/{to_user} from {email_data.from_address}"
        )

        return {
            "status": "success",
            "service": email_data.service,
            "email_user": to_user,
            "stored": True,
        }

    except Exception as e:
        logger.error(f"Error processing email webhook: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/email-data/{service}/{email}")
async def get_email_data(service: str, email: str):
    """Get complete email data for a specific service and email address."""
    # URL decode the email parameter
    decoded_email = unquote(email)
    email_user = decoded_email.split("@")[0] if "@" in decoded_email else decoded_email
    key = f"{service}_{email_user}"

    if key in emails_store:
        return emails_store[key]
    else:
        raise HTTPException(
            status_code=404, detail=f"No email found for {service}/{decoded_email}"
        )


@app.delete("/emails/{service}/{email}")
async def clear_emails(service: str, email: str):
    """Clear stored emails for a specific service and email address."""
    # URL decode the email parameter
    decoded_email = unquote(email)
    email_user = decoded_email.split("@")[0] if "@" in decoded_email else decoded_email
    key = f"{service}_{email_user}"

    if key in emails_store:
        del emails_store[key]
        logger.info(f"Cleared emails for {service}/{decoded_email}")
        return {
            "status": "success",
            "message": f"Emails cleared for {service}/{decoded_email}",
        }
    else:
        raise HTTPException(
            status_code=404, detail=f"No emails found for {service}/{decoded_email}"
        )


@app.get("/emails")
async def list_all_emails():
    """List all stored emails."""
    return {
        "total": len(emails_store),
        "emails": [
            {
                "key": key,
                "service": data["service"],
                "to_address": data["to_address"],
                "from_address": data["from_address"],
                "subject": data["subject"],
                "timestamp": data["timestamp"],
            }
            for key, data in emails_store.items()
        ],
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


def run_server(host: str = "0.0.0.0", port: int = 8888):
    """Run the webhook server."""
    logger.info(f"Starting webhook server on {host}:{port}")
    logger.info(f"Webhook URL: http://{host}:{port}/webhook/email")
    uvicorn.run(app, host=host, port=port)


if __name__ == "__main__":
    run_server()
