"""Monitor script for RegBot continuous automation with colorized ASCII output."""

import asyncio
import argparse
import sys
from pathlib import Path
from datetime import datetime

from core.config import Config
from core.monitoring import MonitoringAnalytics


class Colors:
    """ANSI color codes for terminal output."""

    RESET = "\033[0m"
    BOLD = "\033[1m"
    DIM = "\033[2m"
    UNDERLINE = "\033[4m"

    # Colors
    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"

    # Bright colors
    BRIGHT_BLACK = "\033[90m"
    BRIGHT_RED = "\033[91m"
    BRIGHT_GREEN = "\033[92m"
    BRIGHT_YELLOW = "\033[93m"
    BRIGHT_BLUE = "\033[94m"
    BRIGHT_MAGENTA = "\033[95m"
    BRIGHT_CYAN = "\033[96m"
    BRIGHT_WHITE = "\033[97m"

    # Background colors
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"


def colorize(text: str, color: str = Colors.RESET, bold: bool = False) -> str:
    """Colorize text with ANSI escape codes."""
    prefix = Colors.BOLD if bold else ""
    return f"{prefix}{color}{text}{Colors.RESET}"


def print_header(title: str, width: int = 80):
    """Print a colorized header with ASCII art."""
    border = "═" * width
    print(colorize(f"╔{border}╗", Colors.BRIGHT_CYAN, bold=True))
    print(colorize(f"║{title.center(width)}║", Colors.BRIGHT_CYAN, bold=True))
    print(colorize(f"╚{border}╝", Colors.BRIGHT_CYAN, bold=True))


def print_section(title: str, icon: str = ""):
    """Print a section header with icon."""
    full_title = f"{icon} {title}" if icon else title
    print(colorize(f"\n┌─ {full_title}", Colors.BRIGHT_YELLOW, bold=True))
    print(colorize("│", Colors.BRIGHT_YELLOW))


def print_item(label: str, value: str, indent: int = 1, color: str = Colors.WHITE):
    """Print an indented item with label and value."""
    spacing = "    " * indent
    print(
        f"{colorize('│', Colors.BRIGHT_YELLOW)}{spacing}{colorize(label + ':', Colors.BRIGHT_WHITE)} {colorize(value, color)}"
    )


def print_status_icon(status: str) -> str:
    """Get colored status icon."""
    if status.lower() in ["healthy", "completed", "running", "success"]:
        return colorize("✅", Colors.BRIGHT_GREEN)
    elif status.lower() in ["warning", "paused"]:
        return colorize("⚠️ ", Colors.BRIGHT_YELLOW)
    elif status.lower() in ["critical", "failed", "error"]:
        return colorize("❌", Colors.BRIGHT_RED)
    else:
        return colorize("ℹ️ ", Colors.BRIGHT_BLUE)


def print_progress_bar(current: int, target: int, width: int = 40) -> str:
    """Create a colored progress bar."""
    if target == 0:
        return colorize("[no target set]", Colors.DIM)

    percentage = min(current / target, 1.0)
    filled = int(width * percentage)
    empty = width - filled

    bar = "█" * filled + "░" * empty
    color = Colors.BRIGHT_GREEN if percentage >= 1.0 else Colors.BRIGHT_CYAN
    return f"{colorize(bar, color)} {colorize(f'{current}/{target}', Colors.BRIGHT_WHITE)} ({colorize(f'{percentage * 100:.1f}%', color)})"


def print_ascii_robot():
    """Print ASCII art robot."""
    robot = [
        "    🤖 RegBot Monitoring Dashboard 🤖",
        "    ┌─────────────────────────────────┐",
        "    │          ╭─────────╮          │",
        "    │          │  ◉   ◉  │          │",
        "    │          │    ∩    │          │",
        "    │          ├─────────┤          │",
        "    │          │ MONITOR │          │",
        "    │          └─────────┘          │",
        "    └─────────────────────────────────┘",
    ]

    for line in robot:
        print(colorize(line, Colors.BRIGHT_CYAN, bold=True))


async def display_realtime_dashboard(
    monitoring: MonitoringAnalytics, refresh_seconds: int = 5
):
    """Display enhanced real-time monitoring dashboard."""
    try:
        while True:
            # Clear screen
            print("\033[2J\033[H")

            # ASCII robot header
            print_ascii_robot()

            # Current time
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(
                f"\n{colorize('Last Updated:', Colors.BRIGHT_WHITE)} {colorize(current_time, Colors.BRIGHT_CYAN)}"
            )

            # Current session status
            print_section("Current Session Status", "🔄")
            session_status = monitoring.get_current_session_status()

            if session_status:
                status_icon = print_status_icon(session_status["status"])
                print_item(
                    "Service",
                    colorize(
                        session_status["service"].upper(),
                        Colors.BRIGHT_MAGENTA,
                        bold=True,
                    ),
                )
                print_item(
                    "Status",
                    f"{status_icon} {colorize(session_status['status'].upper(), Colors.BRIGHT_WHITE, bold=True)}",
                )

                # Progress bar
                progress_parts = session_status["progress"].split("/")
                if len(progress_parts) == 2:
                    current = int(progress_parts[0])
                    target = int(progress_parts[1])
                    progress_bar = print_progress_bar(current, target)
                    print_item("Progress", progress_bar)
                else:
                    print_item(
                        "Progress",
                        colorize(session_status["progress"], Colors.BRIGHT_CYAN),
                    )

                print_item(
                    "Success Rate",
                    colorize(
                        session_status["success_rate"], Colors.BRIGHT_GREEN, bold=True
                    ),
                )
                print_item(
                    "Duration", colorize(session_status["duration"], Colors.BRIGHT_BLUE)
                )
                print_item(
                    "Total Attempts",
                    colorize(
                        str(session_status["total_attempts"]), Colors.BRIGHT_WHITE
                    ),
                )

                if session_status.get("next_run"):
                    try:
                        next_run = datetime.fromisoformat(
                            session_status["next_run"].replace("Z", "+00:00")
                        )
                        time_until = next_run - datetime.now()
                        if time_until.total_seconds() > 0:
                            time_str = str(time_until).split(".")[0]
                            print_item(
                                "Next Run",
                                f"in {colorize(time_str, Colors.BRIGHT_YELLOW, bold=True)}",
                            )
                        else:
                            print_item(
                                "Next Run",
                                colorize("DUE NOW", Colors.BRIGHT_RED, bold=True),
                            )
                    except:
                        print_item(
                            "Next Run",
                            colorize(session_status["next_run"], Colors.BRIGHT_YELLOW),
                        )

                if session_status.get("consecutive_failures", 0) > 0:
                    failures = colorize(
                        str(session_status["consecutive_failures"]),
                        Colors.BRIGHT_RED,
                        bold=True,
                    )
                    print_item("⚠️  Consecutive Failures", failures)

                if session_status.get("last_error"):
                    print_item(
                        "Last Error",
                        colorize(
                            session_status["last_error"][:60] + "...", Colors.BRIGHT_RED
                        ),
                    )
            else:
                print_item("Status", colorize("No active session", Colors.DIM))

            # Service credentials
            print_section("Service Credentials", "🔑")
            cred_stats = monitoring.get_service_credentials_stats()

            if cred_stats:
                for service, stats in cred_stats.items():
                    service_name = colorize(
                        service.upper(), Colors.BRIGHT_MAGENTA, bold=True
                    )
                    count = colorize(
                        str(stats["total_credentials"]), Colors.BRIGHT_GREEN, bold=True
                    )
                    print_item(service_name, f"{count} API keys")

                    if stats["latest_created"]:
                        print_item(
                            "Latest",
                            f"{colorize(stats['latest_created'], Colors.BRIGHT_BLUE)} ({colorize(stats['latest_email'], Colors.BRIGHT_CYAN)})",
                            indent=2,
                        )
            else:
                print_item("Status", colorize("No credentials stored", Colors.DIM))

            # Domain load distribution
            print_section("Domain Load Distribution", "🌐")
            domain_stats = monitoring.calculate_domain_load_distribution()

            for group_name, stats in domain_stats.items():
                group_color = (
                    Colors.BRIGHT_MAGENTA
                    if stats["enabled_domains"] > 0
                    else Colors.DIM
                )
                status_info = (
                    f"{stats['enabled_domains']}/{stats['total_domains']} enabled"
                )
                print_item(
                    colorize(group_name.upper(), group_color, bold=True),
                    colorize(status_info, Colors.BRIGHT_WHITE),
                )

                for domain, load_info in stats["load_distribution"].items():
                    percentage = load_info["percentage"]
                    weight = load_info["weight"]

                    # Color code based on load percentage
                    if percentage > 40:
                        percent_color = Colors.BRIGHT_RED
                    elif percentage > 25:
                        percent_color = Colors.BRIGHT_YELLOW
                    else:
                        percent_color = Colors.BRIGHT_GREEN

                    domain_info = f"{colorize(f'{percentage}%', percent_color, bold=True)} (weight: {colorize(str(weight), Colors.BRIGHT_BLUE)})"
                    print_item(f"└── {domain}", domain_info, indent=2)

            # System health
            print_section("System Health", "🏥")
            health = monitoring.get_system_health()

            status_icon = print_status_icon(health["status"])
            status_text = health["status"].upper()
            status_color = (
                Colors.BRIGHT_GREEN
                if health["status"] == "healthy"
                else (
                    Colors.BRIGHT_YELLOW
                    if health["status"] == "warning"
                    else Colors.BRIGHT_RED
                )
            )

            print_item(
                "Overall Status",
                f"{status_icon} {colorize(status_text, status_color, bold=True)}",
            )

            if health["issues"]:
                print_item(
                    "Issues Found",
                    colorize(str(len(health["issues"])), Colors.BRIGHT_RED, bold=True),
                )
                for i, issue in enumerate(health["issues"][:3]):  # Show max 3 issues
                    print_item(
                        f"└── Issue {i + 1}",
                        colorize(issue, Colors.BRIGHT_RED),
                        indent=2,
                    )
                if len(health["issues"]) > 3:
                    print_item(
                        "└── ...",
                        colorize(f"and {len(health['issues']) - 3} more", Colors.DIM),
                        indent=2,
                    )

            if health["recommendations"]:
                print_item(
                    "Recommendations",
                    colorize(
                        str(len(health["recommendations"])),
                        Colors.BRIGHT_YELLOW,
                        bold=True,
                    ),
                )
                for i, rec in enumerate(
                    health["recommendations"][:2]
                ):  # Show max 2 recommendations
                    print_item(
                        f"└── Tip {i + 1}",
                        colorize(rec, Colors.BRIGHT_YELLOW),
                        indent=2,
                    )

            # Footer
            print(
                colorize(
                    "\n└─────────────────────────────────────────────────────────────────────────────────┘",
                    Colors.BRIGHT_YELLOW,
                )
            )

            refresh_text = (
                f"Refreshing in {refresh_seconds} seconds... Press Ctrl+C to exit"
            )
            print(f"\n{colorize(refresh_text, Colors.DIM)}")

            # Wait for next refresh
            await asyncio.sleep(refresh_seconds)

    except KeyboardInterrupt:
        print(f"\n{colorize('Monitoring stopped by user', Colors.BRIGHT_YELLOW)}")
    except Exception as e:
        print(f"\n{colorize(f'Error in monitoring: {e}', Colors.BRIGHT_RED)}")


def print_analytics_report(monitoring: MonitoringAnalytics):
    """Print colorized analytics report."""
    print_header("📊 RegBot Analytics Report", 60)

    analytics = monitoring.get_historical_analytics()
    session_status = monitoring.get_current_session_status()
    cred_stats = monitoring.get_service_credentials_stats()

    # Overall statistics
    print_section("Overall Statistics", "📈")
    print_item(
        "Total Sessions",
        colorize(str(analytics["total_sessions"]), Colors.BRIGHT_CYAN, bold=True),
    )
    print_item(
        "Total Registrations",
        colorize(str(analytics["total_registrations"]), Colors.BRIGHT_GREEN, bold=True),
    )
    print_item(
        "Total Attempts",
        colorize(str(analytics["total_attempts"]), Colors.BRIGHT_BLUE, bold=True),
    )

    if analytics["total_attempts"] > 0:
        overall_rate = (
            analytics["total_registrations"] / analytics["total_attempts"]
        ) * 100
        rate_color = (
            Colors.BRIGHT_GREEN
            if overall_rate > 70
            else (Colors.BRIGHT_YELLOW if overall_rate > 40 else Colors.BRIGHT_RED)
        )
        print_item(
            "Overall Success Rate",
            colorize(f"{overall_rate:.1f}%", rate_color, bold=True),
        )

    # Current session
    if session_status:
        print_section("Current Session", "🔄")
        status_icon = print_status_icon(session_status["status"])
        print_item(
            "Service",
            colorize(
                session_status["service"].upper(), Colors.BRIGHT_MAGENTA, bold=True
            ),
        )
        print_item(
            "Status",
            f"{status_icon} {colorize(session_status['status'].upper(), Colors.BRIGHT_WHITE, bold=True)}",
        )
        print_item(
            "Progress",
            colorize(session_status["progress"], Colors.BRIGHT_CYAN, bold=True),
        )
        print_item(
            "Success Rate",
            colorize(session_status["success_rate"], Colors.BRIGHT_GREEN, bold=True),
        )
        print_item("Duration", colorize(session_status["duration"], Colors.BRIGHT_BLUE))

    # Service statistics
    if analytics["services"]:
        print_section("Service Statistics", "🎯")
        for service, stats in analytics["services"].items():
            print_item(colorize(service.upper(), Colors.BRIGHT_MAGENTA, bold=True), "")
            print_item(
                "Sessions",
                colorize(str(stats["total_sessions"]), Colors.BRIGHT_CYAN),
                indent=2,
            )
            print_item(
                "Registrations",
                colorize(str(stats["total_registrations"]), Colors.BRIGHT_GREEN),
                indent=2,
            )

            rate = stats.get("success_rate", 0)
            rate_color = (
                Colors.BRIGHT_GREEN
                if rate > 70
                else (Colors.BRIGHT_YELLOW if rate > 40 else Colors.BRIGHT_RED)
            )
            print_item(
                "Success Rate",
                colorize(f"{rate:.1f}%", rate_color, bold=True),
                indent=2,
            )

    # Stored credentials
    if cred_stats:
        print_section("Stored Credentials", "🔑")
        for service, stats in cred_stats.items():
            service_name = colorize(service.upper(), Colors.BRIGHT_MAGENTA, bold=True)
            count = colorize(
                str(stats["total_credentials"]), Colors.BRIGHT_GREEN, bold=True
            )
            print_item(service_name, f"{count} API keys")

    print(colorize("\n" + "═" * 60, Colors.BRIGHT_CYAN))


async def main():
    """Main monitoring function."""
    parser = argparse.ArgumentParser(
        description="RegBot Monitoring and Analytics with colorized ASCII output",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""
{colorize("Examples:", Colors.BRIGHT_YELLOW, bold=True)}
  {colorize("python monitor.py --realtime", Colors.BRIGHT_CYAN)}                    # Start real-time monitoring dashboard
  {colorize("python monitor.py --report", Colors.BRIGHT_CYAN)}                     # Generate analytics report
  {colorize("python monitor.py --status", Colors.BRIGHT_CYAN)}                     # Show current status only
  {colorize("python monitor.py --health", Colors.BRIGHT_CYAN)}                     # Show system health check

{colorize("Monitor Commands:", Colors.BRIGHT_YELLOW, bold=True)}
  {colorize("--realtime", Colors.BRIGHT_GREEN)}: Display live updating dashboard with colors and ASCII art
  {colorize("--report", Colors.BRIGHT_GREEN)}:   Generate comprehensive analytics report
  {colorize("--status", Colors.BRIGHT_GREEN)}:   Show current session status
  {colorize("--health", Colors.BRIGHT_GREEN)}:   Show system health and configuration issues
        """,
    )

    parser.add_argument(
        "--realtime", action="store_true", help="Start real-time monitoring dashboard"
    )

    parser.add_argument(
        "--report", action="store_true", help="Generate analytics report"
    )

    parser.add_argument(
        "--status", action="store_true", help="Show current session status"
    )

    parser.add_argument(
        "--health", action="store_true", help="Show system health check"
    )

    parser.add_argument(
        "--refresh",
        type=int,
        default=5,
        help="Refresh interval in seconds for real-time mode (default: 5)",
    )

    args = parser.parse_args()

    # Load configuration
    config_path = Path("config.yaml")
    if not config_path.exists():
        print(
            colorize(
                "❌ config.yaml not found. Please create configuration file.",
                Colors.BRIGHT_RED,
                bold=True,
            )
        )
        return 1

    config = Config.load(config_path)
    monitoring = MonitoringAnalytics(config)

    # Handle different commands
    if args.realtime:
        print(
            colorize(
                "Starting real-time monitoring dashboard...",
                Colors.BRIGHT_GREEN,
                bold=True,
            )
        )
        print(colorize("Press Ctrl+C to exit", Colors.BRIGHT_YELLOW))
        await display_realtime_dashboard(monitoring, args.refresh)
        return 0

    elif args.report:
        print_analytics_report(monitoring)
        return 0

    elif args.status:
        print_header("📊 Current Session Status", 50)
        session_status = monitoring.get_current_session_status()

        if session_status:
            status_icon = print_status_icon(session_status["status"])
            print_item(
                "Service",
                colorize(
                    session_status["service"].upper(), Colors.BRIGHT_MAGENTA, bold=True
                ),
            )
            print_item(
                "Status",
                f"{status_icon} {colorize(session_status['status'].upper(), Colors.BRIGHT_WHITE, bold=True)}",
            )
            print_item(
                "Progress",
                colorize(session_status["progress"], Colors.BRIGHT_CYAN, bold=True),
            )
            print_item(
                "Success Rate",
                colorize(
                    session_status["success_rate"], Colors.BRIGHT_GREEN, bold=True
                ),
            )
            print_item(
                "Total Attempts",
                colorize(str(session_status["total_attempts"]), Colors.BRIGHT_BLUE),
            )
            print_item(
                "Duration", colorize(session_status["duration"], Colors.BRIGHT_BLUE)
            )

            if session_status.get("next_run"):
                print_item(
                    "Next Run",
                    colorize(session_status["next_run"], Colors.BRIGHT_YELLOW),
                )

            if session_status.get("last_error"):
                print_item(
                    "Last Error",
                    colorize(session_status["last_error"], Colors.BRIGHT_RED),
                )
        else:
            print_item("Status", colorize("No active session found", Colors.DIM))

        return 0

    elif args.health:
        print_header("🏥 System Health Check", 50)
        health = monitoring.get_system_health()
        domain_stats = monitoring.calculate_domain_load_distribution()

        status_icon = print_status_icon(health["status"])
        status_color = (
            Colors.BRIGHT_GREEN
            if health["status"] == "healthy"
            else (
                Colors.BRIGHT_YELLOW
                if health["status"] == "warning"
                else Colors.BRIGHT_RED
            )
        )
        print_item(
            "Overall Status",
            f"{status_icon} {colorize(health['status'].upper(), status_color, bold=True)}",
        )

        if health["issues"]:
            print_section("Issues Found", "❌")
            for i, issue in enumerate(health["issues"], 1):
                print_item(f"Issue {i}", colorize(issue, Colors.BRIGHT_RED))

        if health["recommendations"]:
            print_section("Recommendations", "💡")
            for i, rec in enumerate(health["recommendations"], 1):
                print_item(f"Tip {i}", colorize(rec, Colors.BRIGHT_YELLOW))

        print_section("Domain Configuration", "🌐")
        for group_name, stats in domain_stats.items():
            group_color = (
                Colors.BRIGHT_MAGENTA if stats["enabled_domains"] > 0 else Colors.DIM
            )
            status_info = f"{stats['enabled_domains']}/{stats['total_domains']} enabled"
            print_item(
                colorize(group_name.upper(), group_color, bold=True),
                colorize(status_info, Colors.BRIGHT_WHITE),
            )

            for domain, load_info in stats["load_distribution"].items():
                percentage = load_info["percentage"]
                percent_color = (
                    Colors.BRIGHT_GREEN if percentage < 40 else Colors.BRIGHT_YELLOW
                )
                print_item(
                    f"└── {domain}",
                    f"{colorize(f'{percentage}%', percent_color)} load",
                    indent=2,
                )

        return 0

    else:
        parser.print_help()
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print(f"\n{colorize('Monitoring stopped by user', Colors.BRIGHT_YELLOW)}")
        sys.exit(0)
    except Exception as e:
        print(f"{colorize(f'Error: {e}', Colors.BRIGHT_RED, bold=True)}")
        sys.exit(1)
