{"name": "regbot-email-interceptor", "version": "1.0.0", "description": "Email interceptor for regbot service registrations", "main": "src/index.js", "scripts": {"dev": "pnpm wrangler dev", "deploy": "pnpm wrangler deploy", "deploy:dev": "pnpm wrangler deploy --env development", "deploy:prod": "pnpm wrangler deploy --env production", "tail": "pnpm wrangler tail", "tail:dev": "pnpm wrangler tail --env development", "tail:prod": "pnpm wrangler tail --env production", "kv:list": "pnpm wrangler kv:key list --namespace-id EMAIL_KV", "test:email": "node scripts/test-email.js"}, "devDependencies": {"wrangler": "^4.27.0"}}