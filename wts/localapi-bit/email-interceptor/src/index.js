// Generic email interceptor worker
// Captures emails from configured services and forwards to webhook/KV for parsing

// Helper function to check if email domain is configured
function isConfiguredDomain(toDomain, env) {
  // Check environment variable domains
  const configuredDomains = env.CONFIGURED_DOMAINS ? 
    env.CONFIGURED_DOMAINS.split(',').map(d => d.trim()) : [];
  
  return configuredDomains.includes(toDomain);
}

// Helper function to detect service from sender domain using environment variables
function detectService(senderDomain, env) {
  // Check each service's sender domains from environment variables
  // Format: ASSEMBLYAI_SENDER_DOMAINS="assemblyai.com,mail.assemblyai.com"
  for (const [key, value] of Object.entries(env)) {
    if (key.endsWith('_SENDER_DOMAINS') && value) {
      const serviceName = key.replace('_SENDER_DOMAINS', '').toLowerCase();
      const domains = value.split(',').map(d => d.trim());
      
      for (const domain of domains) {
        // Check for exact match or subdomain match
        if (senderDomain === domain || senderDomain.endsWith('.' + domain)) {
          return serviceName;
        }
      }
    }
  }
  return null;
}

export default {
  async email(message, env) {
    const fromAddress = message.from || "";
    const senderDomain = fromAddress.split("@")[1]?.toLowerCase();
    const toAddress = message.to || "";
    const toDomain = toAddress.split("@")[1]?.toLowerCase();

    // Check if this is one of our configured email domains
    if (!isConfiguredDomain(toDomain, env)) {
      message.setReject("550 5.7.1 Invalid recipient domain");
      return;
    }

    // Detect service from sender
    const service = detectService(senderDomain, env);
    
    if (service) {
      try {
        // Extract email content
        const rawBody = await new Response(message.raw).text();
        
        // Parse email headers
        const headers = {};
        message.headers.forEach((value, key) => {
          headers[key] = value;
        });
        
        // Prepare generic email data (no service-specific parsing)
        const emailData = {
          from_address: fromAddress,
          to_address: toAddress,
          subject: headers.subject || "",
          timestamp: new Date().toISOString(),
          service: service,
          sender_domain: senderDomain,
          email_domain: toDomain,
          headers: headers,
          raw: env.STORE_RAW_EMAIL === "true" ? rawBody : ""  // Controlled by environment variable
        };
        
        // Send to webhook if configured (webhook server will do the parsing)
        if (env.WEBHOOK_URL) {
          try {
            const webhookResponse = await fetch(env.WEBHOOK_URL, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(emailData)
            });
            
            if (!webhookResponse.ok) {
              console.error(`Webhook failed: ${webhookResponse.status}`);
            } else {
              console.log(`Webhook success: ${service} email forwarded`);
            }
          } catch (webhookError) {
            console.error("Webhook error:", webhookError);
          }
        }
        
        // Store in KV as backup/alternative (for future KV-only architecture)
        if (env.EMAIL_KV) {
          const key = `${service}_${Date.now()}_${toAddress.split("@")[0]}`;
          await env.EMAIL_KV.put(key, JSON.stringify(emailData), {
            expirationTtl: 3600 // Expire after 1 hour
          });
          
          // Store latest email for easy retrieval
          await env.EMAIL_KV.put(`latest_${service}_email`, JSON.stringify(emailData), {
            expirationTtl: 600 // 10 minutes
          });
        }
        
        // Log for debugging
        console.log(`Processed ${service} email: ${fromAddress} -> ${toAddress}`);
        
        // Forward to backup email if configured
        if (env.FORWARD_EMAIL) {
          await message.forward(env.FORWARD_EMAIL);
        }
        
      } catch (error) {
        console.error(`Error processing ${service} email:`, error);
        // Still forward the email on error
        if (env.FORWARD_EMAIL) {
          await message.forward(env.FORWARD_EMAIL);
        }
      }
    } else {
      // Unknown service - log and optionally forward
      console.log(`Unknown sender: ${senderDomain}`);
      
      if (env.FORWARD_UNKNOWN && env.FORWARD_EMAIL) {
        await message.forward(env.FORWARD_EMAIL);
      } else {
        message.setReject("550 5.7.1 Sender not configured for this service");
      }
    }
  }
};