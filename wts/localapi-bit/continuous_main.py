"""Main entry point for continuous automation."""

import asyncio
import signal
import sys
import logging
import argparse
from pathlib import Path
from typing import Optional

from core.config import Config
from core.continuous_automation import (
    ContinuousAutomationManager,
    ContinuousAutomationConfig,
    SessionStatus,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("continuous_automation.log"),
    ],
)
logger = logging.getLogger(__name__)


class ContinuousAutomationCLI:
    """Command-line interface for continuous automation."""

    def __init__(self):
        self.manager: Optional[ContinuousAutomationManager] = None
        self.shutdown_requested = False

    def setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""

        def signal_handler(signum, frame):
            logger.info("Received shutdown signal, initiating graceful shutdown...")
            self.shutdown_requested = True
            if self.manager:
                self.manager.request_shutdown()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def start_continuous_automation(
        self, service_name: str, target_registrations: int = None
    ):
        """Start continuous automation for a service."""
        try:
            # Load configuration
            config_path = Path("config.yaml")
            if not config_path.exists():
                logger.error("config.yaml not found. Please create configuration file.")
                return 1

            config = Config.load(config_path)

            # Validate service
            if service_name not in config.services:
                logger.error(f"Service '{service_name}' not found in configuration")
                logger.info(f"Available services: {', '.join(config.services.keys())}")
                return 1

            service_config = config.services[service_name]
            if not service_config.enabled:
                logger.error(f"Service '{service_name}' is disabled in configuration")
                return 1

            # Create automation manager
            automation_config = config.continuous_automation
            self.manager = ContinuousAutomationManager(config, automation_config)

            # Set up signal handlers for graceful shutdown
            self.setup_signal_handlers()

            # Display startup information
            print(f"\n🤖 RegBot Continuous Automation")
            print(f"{'=' * 50}")
            print(f"Service: {service_config.name}")
            print(
                f"Target: {target_registrations or automation_config.target_registrations} registrations"
            )
            print(
                f"Interval: {automation_config.min_interval_minutes}-{automation_config.max_interval_minutes} minutes"
            )
            print(f"Max failures: {automation_config.max_failed_attempts}")
            print(
                f"Session persistence: {'enabled' if automation_config.session_persistence_enabled else 'disabled'}"
            )
            print(f"{'=' * 50}\n")

            # Check for existing session
            existing_session = self.manager.load_session()
            if existing_session:
                print(f"📂 Found existing session: {existing_session.session_id}")
                print(f"   Status: {existing_session.status}")
                print(
                    f"   Progress: {existing_session.successful_registrations}/{existing_session.target_registrations}"
                )
                print(f"   Total attempts: {existing_session.total_attempts}")
                if existing_session.last_error:
                    print(f"   Last error: {existing_session.last_error}")
                print()

                # Ask user if they want to resume
                try:
                    resume = input("Resume existing session? (y/n): ").lower().strip()
                    if resume != "y":
                        print("Creating new session...")
                        # Clear existing session by creating new one
                        existing_session = None
                except KeyboardInterrupt:
                    print("\nShutdown requested by user")
                    return 0

            # Start automation
            print("🚀 Starting continuous automation...")
            print("Press Ctrl+C to stop gracefully\n")

            session = await self.manager.run_continuous_automation(
                service_name, target_registrations
            )

            # Display final results
            await self._display_final_results(session)

            return 0 if session.status == SessionStatus.COMPLETED else 1

        except KeyboardInterrupt:
            logger.info("Shutdown requested by user")
            return 0
        except Exception as e:
            logger.error(f"Critical error in continuous automation: {e}")
            return 1

    async def show_status(self):
        """Show status of current session."""
        try:
            config_path = Path("config.yaml")
            config = Config.load(config_path)
            automation_config = config.continuous_automation

            manager = ContinuousAutomationManager(config, automation_config)
            session = manager.load_session()

            if not session:
                print("❌ No active session found")
                return 1

            summary = manager.get_session_summary()

            print(f"\n📊 Session Status")
            print(f"{'=' * 40}")
            print(f"Session ID: {summary['session_id']}")
            print(f"Service: {summary['service']}")
            print(f"Status: {summary['status']}")
            print(f"Progress: {summary['progress']}")
            print(f"Success rate: {summary['success_rate']}")
            print(f"Total attempts: {summary['total_attempts']}")
            print(f"Failed attempts: {summary['failed_attempts']}")
            print(f"Consecutive failures: {summary['consecutive_failures']}")
            print(f"Duration: {summary['duration']}")

            if summary["next_run"]:
                print(f"Next run: {summary['next_run']}")

            if summary["last_error"]:
                print(f"Last error: {summary['last_error']}")

            print(f"{'=' * 40}\n")

            return 0

        except Exception as e:
            logger.error(f"Error showing status: {e}")
            return 1

    async def _display_final_results(self, session):
        """Display final results of automation session."""
        print(f"\n🏁 Automation Complete")
        print(f"{'=' * 50}")
        print(f"Final Status: {session.status}")
        print(f"Successful registrations: {session.successful_registrations}")
        print(f"Total attempts: {session.total_attempts}")
        print(f"Failed attempts: {session.failed_attempts}")

        if session.total_attempts > 0:
            success_rate = (
                session.successful_registrations / session.total_attempts
            ) * 100
            print(f"Success rate: {success_rate:.1f}%")

        duration = (
            session.completed_at - session.created_at if session.completed_at else None
        )
        if duration:
            print(f"Total duration: {str(duration).split('.')[0]}")

        if session.status == SessionStatus.COMPLETED:
            print("🎉 Goal achieved successfully!")
        elif session.status == SessionStatus.FAILED:
            print("❌ Automation failed")
            if session.last_error:
                print(f"Final error: {session.last_error}")
        elif session.status == SessionStatus.PAUSED:
            print("⏸️ Session paused (can be resumed)")

        print(f"{'=' * 50}\n")


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="RegBot Continuous Automation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s assemblyai                    # Start continuous automation for AssemblyAI
  %(prog)s exaai                         # Start continuous automation for ExaAI
  %(prog)s assemblyai --target 20        # Target 20 registrations
  %(prog)s --status                      # Show current session status
  %(prog)s --help                        # Show this help message

Configuration:
  Edit config.yaml to configure services, domains, and automation settings.
        """,
    )

    parser.add_argument(
        "service", nargs="?", help="Service name to automate (e.g., assemblyai, exaai)"
    )

    parser.add_argument(
        "--target", type=int, help="Target number of successful registrations"
    )

    parser.add_argument(
        "--status", action="store_true", help="Show status of current session"
    )

    args = parser.parse_args()

    cli = ContinuousAutomationCLI()

    # Handle status command
    if args.status:
        return await cli.show_status()

    # Validate service argument
    if not args.service:
        parser.print_help()
        return 1

    # Start continuous automation
    return await cli.start_continuous_automation(args.service, args.target)


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
