email_domain_groups:
  primary:
    - domain: "ai.whatisinitfor.me"
      weight: 50
      enabled: true
    - domain: "sg.164136.xyz"
      weight: 30
      enabled: true
    - domain: "iad.164136.xyz"
      weight: 20
      enabled: true

# Email retrieval configuration
email_retrieval:
  client_type: "webhook"
  webhook:
    local_baseurl: "http://localhost:8888"

# Global browser settings
browser_engines:
  - chromium
  - firefox
  - bit:
      browser_id: "08b818f4e0a84f0a86a08fa404f41662"
      credentials:
        enabled: true
        token: 2329381312
      api_url: "http://127.0.0.1:54345"

services:
  firecrawl:
    name: "Firecrawl"
    start_url: "https://www.firecrawl.dev"
    email_domain_groups: ["primary"]
    sender_domains: ["firecrawl.dev", "service.firecrawl.dev"]
    enabled: true
    headless: true
    browser_engines:
      - bit:
          browser_id: "08b818f4e0a84f0a86a08fa404f41662"
          credentials:
            enabled: false
          api_url: "http://127.0.0.1:54345"
      - chromium
      - firefox
    wait_times:
      page_load: 3000
      after_signup: 5000
      verification_check: 10000