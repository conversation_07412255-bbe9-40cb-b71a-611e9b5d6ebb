[project]
name = "regbot"
version = "0.1.0"
description = "Service registration automation system"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
dependencies = [
    "fastapi>=0.116.1",
    "uvicorn[standard]>=0.35.0",
    "playwright>=1.54.0",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.1.0",
    "pyyaml>=6.0.2",
    "httpx>=0.28.1",
    "tenacity>=8.2.3",
    "structlog>=23.2.0",
    "python-multipart>=0.0.6",
    "aiofiles>=24.1.0",
    "faker>=37.5.3",
    "toml>=0.10.2",
    "aiohttp>=3.12.15",
    "hatch>=1.14.1",
    "pjstealth",
    "setuptools>=80.9.0",
    "playwright-stealth>=2.0.0",
    "requests>=2.32.4",
]
readme = "README.md"
requires-python = ">= 3.13"

[project.scripts]
regbot = "main:main"

[tool.hatch.envs.default.scripts]
# Main automation tasks
run-assemblyai = "python main.py assemblyai"
run-exaai = "python main.py exaai"
run-webhook = "uvicorn webhook_server:app --host 0.0.0.0 --port 8888 --reload"

# Setup tasks
install-browsers = "playwright install"
install-browsers-full = "playwright install chromium firefox webkit"
setup-env = ["install-browsers"]

# Configuration sync
sync-worker-config = "python sync_worker_config.py"

# Email worker tasks
worker-deploy = "cd email-interceptor && pnpm wrangler deploy"

# Development workflows
dev = ["run-webhook"]
emailworker-ready = ["sync-worker-config", "worker-deploy"]
test-flow = ["run-webhook"]
full-setup = ["sync-worker-config", "install-browsers", "worker-deploy"]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "black>=23.11.0",
    "ruff>=0.1.6",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["core", "services"]

[tool.ruff]
target-version = "py312"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.black]
target-version = ['py312']
include = '\.pyi?$'
line-length = 88

[tool.mypy]
python_version = "3.12"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.uv.sources]
pjstealth = { git = "https://github.com/winner-hue/pjstealth.git" }
