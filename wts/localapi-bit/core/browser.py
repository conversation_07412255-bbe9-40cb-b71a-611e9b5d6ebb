"""Browser management with <PERSON><PERSON> and lightweight profile randomization."""

import asyncio
import logging
import random
from typing import Op<PERSON>, List, Tuple
from playwright.async_api import async_<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>erContex<PERSON>, Page
from .randomizer import Randomizer, create_randomizer, RandomizationProfile

logger = logging.getLogger(__name__)


class BrowserManager:
    """Manage browser instances with Playwright and human-like behavior."""

    def __init__(
        self,
        headless: bool = False,
        timeout: int = 30000,
        enable_randomization: bool = True,
        user_agent: Optional[str] = None,
        viewport: Optional[Tuple[int, int]] = None,
        permissions: Optional[List[str]] = None,
        browser_engines: Optional[List[str]] = None,
        randomizer: Optional[Randomizer] = None,
    ):
        self.headless = headless
        self.timeout = timeout
        self.enable_randomization = enable_randomization
        self.custom_user_agent = user_agent
        self.custom_viewport = viewport
        self.permissions = permissions

        # Initialize randomizer - use provided one or create default
        if randomizer:
            self.randomizer = randomizer
        else:
            # Create appropriate randomizer based on settings
            profile = (
                RandomizationProfile.NORMAL
                if enable_randomization
                else RandomizationProfile.TESTING
            )
            self.randomizer = create_randomizer(profile, enabled=enable_randomization)

        # Store browser engines for fallback logic - NO defaults
        self.browser_engines = browser_engines or []

        # Handle browser engine selection from list
        if not self.browser_engines:
            raise ValueError(
                "No browser engines configured. At least one browser engine must be specified."
            )

        parsed_engines = self._parse_browser_engines(self.browser_engines)
        available_engines = [engine["name"] for engine in parsed_engines]

        if enable_randomization:
            selected_engine_info = self.randomizer.choice(parsed_engines)
            self.browser_engine = selected_engine_info["name"]
            self.engine_config = selected_engine_info.get("options", {})
        else:
            selected_engine_info = parsed_engines[
                0
            ]  # Use first engine when not randomizing
            self.browser_engine = selected_engine_info["name"]
            self.engine_config = selected_engine_info.get("options", {})

        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self._bitbrowser_manager = None  # For BitBrowser cleanup

        # User agent pool for randomization - expanded with more versions
        self.user_agents = [
            # Chrome versions (different releases)
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            # Safari versions
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15",
            # Firefox versions
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0",
            # Edge versions
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
        ]

        # Viewport sizes for randomization
        self.viewport_sizes = [
            (1920, 1080),
            (1366, 768),
            (1440, 900),
            (1536, 864),
            (1280, 720),
            (1600, 900),
            (1680, 1050),
            (1280, 800),
            (1024, 768),
        ]

    def _parse_browser_engines(self, engines_config):
        """Parse browser engines configuration to normalize format."""
        parsed_engines = []
        for engine in engines_config:
            if isinstance(engine, str):
                parsed_engines.append({"name": engine, "type": "simple"})
            elif isinstance(engine, dict):
                # Handle nested engine configurations like bit: {...}
                if len(engine) == 1 and isinstance(list(engine.values())[0], dict):
                    engine_name = list(engine.keys())[0]
                    engine_config = list(engine.values())[0]
                    parsed_engines.append(
                        {
                            "name": engine_name,
                            "type": "configured",
                            "options": engine_config,
                        }
                    )
                else:
                    # Handle direct dict format
                    parsed_engines.append(
                        {
                            "name": engine.get("name", "unknown"),
                            "type": "configured",
                            "options": engine,
                        }
                    )
        return parsed_engines

    def _get_random_user_agent(self) -> str:
        """Get random user agent."""
        if self.custom_user_agent:
            return self.custom_user_agent
        if self.enable_randomization:
            return self.randomizer.choice(self.user_agents)
        return self.user_agents[0]  # Default to first one

    def _get_random_viewport(self) -> dict:
        """Get random viewport size."""
        if self.custom_viewport:
            width, height = self.custom_viewport
            return {"width": width, "height": height}

        if self.enable_randomization:
            width, height = self.randomizer.choice(self.viewport_sizes)
            # Add small random variation (±20 pixels)
            width += self.randomizer.randint(-20, 20)
            height += self.randomizer.randint(-20, 20)
            return {"width": max(800, width), "height": max(600, height)}

        return {"width": 1280, "height": 720}

    def _get_context_options(self) -> dict:
        """Get context options for browser context creation."""
        context_options = {
            "viewport": self._get_random_viewport(),
            "user_agent": self._get_random_user_agent(),
            "java_script_enabled": True,
            "accept_downloads": True,
            "ignore_https_errors": True,
            "extra_http_headers": {
                "Accept-Language": "en-US,en;q=0.9",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "max-age=0",
                "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"macOS"',
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "none",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1",
            },
        }

        # Add permissions based on browser engine
        if self.browser_engine in ["chromium", "bit", "fpchrome"]:
            context_options["permissions"] = ["clipboard-read", "clipboard-write"]
        elif self.browser_engine == "webkit":
            context_options["permissions"] = ["clipboard-read"]
        elif self.browser_engine == "firefox":
            context_options["permissions"] = []

        # Add random locale and timezone if randomization enabled
        if self.enable_randomization:
            locales = ["en-US", "en-GB", "en-CA", "en-AU"]
            timezones = [
                "America/New_York",
                "America/Los_Angeles",
                "Europe/London",
                "America/Chicago",
            ]
            context_options.update(
                {
                    "locale": self.randomizer.choice(locales),
                    "timezone_id": self.randomizer.choice(timezones),
                }
            )

        return context_options

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.stop()

    async def start(self):
        """Start browser and create context with automatic fallback on engine failure."""
        self.playwright = await async_playwright().start()

        # Get ONLY the browser engines specified in the service configuration
        # NO global fallbacks - only use what's explicitly configured
        parsed_engines = self._parse_browser_engines(self.browser_engines or [])

        # Try engines in order until one succeeds
        last_error = None
        engines_tried = []

        for engine_info in parsed_engines:
            try:
                engine_name = engine_info["name"]
                engine_config = engine_info.get("options", {})

                logger.info(f"🌐 Trying browser engine: {engine_name}")
                engines_tried.append(engine_name)

                # Set current engine info
                self.browser_engine = engine_name
                self.engine_config = engine_config

                # Try to start this engine
                await self._start_engine()

                logger.info(f"✅ Successfully started browser engine: {engine_name}")
                return  # Success - exit the loop

            except Exception as e:
                last_error = e
                logger.warning(f"⚠️ Engine {engine_name} failed: {e}")

                # Clean up any partial resources
                if self.browser:
                    try:
                        await self.browser.close()
                    except:
                        pass
                    self.browser = None

                if hasattr(self, "_bitbrowser_manager") and self._bitbrowser_manager:
                    try:
                        self._bitbrowser_manager.close_browser()
                    except:
                        pass
                    self._bitbrowser_manager = None

                # Continue to next engine
                continue

        # If we get here, all engines failed
        engines_str = ", ".join(engines_tried)
        raise RuntimeError(
            f"All browser engines failed ({engines_str}). Last error: {last_error}"
        )

    async def _start_engine(self):
        """Start the currently selected browser engine."""
        # Browser launch args for stealth
        launch_args = [
            "--disable-blink-features=AutomationControlled",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--no-first-run",
            "--disable-background-networking",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--enable-features=ClipboardContentAllowedByOrigin",
            "--incognito",  # Back to incognito mode as requested
            "--disable-extensions",
            "--disable-plugins",
            "--no-sandbox",
            "--disable-dev-shm-usage",
        ]
        if self.permissions:
            launch_args.append(
                "--enable-features=ClipboardContentAllowedByOrigin,"
                + ",".join(self.permissions)
            )

        if self.enable_randomization:
            # Add random Chrome features
            launch_args.extend(
                [
                    f"--window-size={self.randomizer.randint(1200, 1920)},{self.randomizer.randint(800, 1080)}",
                    "--disable-extensions-except=" + ",".join([]),  # Empty list
                    "--load-extension=" + ",".join([]),  # Empty list
                ]
            )

        # Launch browser based on selected engine
        if self.browser_engine == "firefox":
            # Firefox-specific args
            firefox_args = [
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
            ]
            self.browser = await self.playwright.firefox.launch(
                headless=self.headless, args=firefox_args
            )
        elif self.browser_engine == "webkit":
            # WebKit/Safari
            self.browser = await self.playwright.webkit.launch(headless=self.headless)
        elif self.browser_engine == "fpchrome":
            # FPChrome - Ungoogled Chromium with fingerprinting and enhanced automation features
            # Use unique temp directory for each session to ensure clean state
            import tempfile
            import uuid

            temp_user_data = f"/tmp/chromium/fpchrome_{uuid.uuid4().hex[:8]}"

            fpchrome_args = [
                # Core fingerprinting arguments
                "--fingerprint=1000",
                '--timezone="America/Los_Angeles"',
                '--fingerprint-platform="windows"',
                '--fingerprint-platform-version="15.2.0"',
                '--fingerprint-brand="Edge"',
                '--lang="en-US"',
                # Enhanced automation hiding flags based on research
                "--disable-blink-features=AutomationControlled",
                "--disable-infobars",
                "--disable-infobar-for-protected-media-identifier",
                "--disable-notifications",
                "--disable-save-password-bubble",
                "--disable-translate-new-ux",
                "--disable-file-system",
                "--disable-extensions",
                "--disable-extensions-file-access-check",
                "--disable-extensions-http-throttling",
                "--no-first-run",
                "--disable-default-apps",
                "--disable-popup-blocking",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--disable-backgrounding-occluded-windows",
                "--disable-client-side-phishing-detection",
                "--disable-sync",
                "--disable-translate",
                "--disable-features=TranslateUI,BlinkGenPropertyTrees,VizDisplayCompositor",
                "--disable-ipc-flooding-protection",
                "--disable-dev-shm-usage",
                "--disable-software-rasterizer",
                "--disable-component-extensions-with-background-pages",
                "--disable-background-networking",
                "--no-default-browser-check",
                "--no-first-run",
                "--remote-debugging-port=0",
                "--no-sandbox",
            ]

            # Combine with existing launch args but filter out conflicting ones
            filtered_launch_args = [
                arg
                for arg in launch_args
                if not any(
                    arg.startswith(prefix)
                    for prefix in [
                        "--disable-blink-features",
                        "--remote-debugging-port",
                        "--disable-gpu",
                    ]
                )
            ]
            all_args = filtered_launch_args + fpchrome_args

            # Use launch_persistent_context with unique temp directory for clean sessions
            # This ensures each run starts with a fresh browser state
            context_options = self._get_context_options()

            # Add excludeSwitches to experimental options to hide automation banner
            if "extra_http_headers" in context_options:
                del context_options[
                    "extra_http_headers"
                ]  # Will be set after context creation

            self.context = await self.playwright.chromium.launch_persistent_context(
                user_data_dir=temp_user_data,
                headless=self.headless,
                args=all_args,
                executable_path="/Applications/Chromium.app/Contents/MacOS/Chromium",
                ignore_default_args=[
                    "--enable-automation",
                    "--enable-blink-features=AutomationControlled",
                ],
                **context_options,
            )

            # Store temp directory for cleanup
            self._temp_user_data = temp_user_data

            # Get the browser from the context
            self.browser = self.context.browser
            self.context.set_default_timeout(self.timeout)

            # Create a new page
            self.page = await self.context.new_page()

            # Enhanced stealth setup using Ungoogled Chromium features
            await self._setup_ungoogled_chromium_features()

            # Set up additional page features
            await self._setup_page_features()
            return  # Skip the normal context creation below
        elif self.browser_engine == "bit":
            # BitBrowser integration
            from .bitbrowser_manager import BitBrowserManager

            # Get BitBrowser configuration
            browser_id = self.engine_config.get("browser_id", "bit")
            api_url = self.engine_config.get("api_url", "http://127.0.0.1:54345")
            credentials = self.engine_config.get("credentials", {})

            bitbrowser_manager = BitBrowserManager(
                browser_id=browser_id, api_url=api_url, credentials=credentials
            )

            # Open BitBrowser and get WebSocket endpoint
            result = bitbrowser_manager.open_browser(headless=self.headless)
            if not result["success"]:
                raise RuntimeError(
                    f"Failed to open BitBrowser: {result.get('error', 'Unknown error')}"
                )

            ws_endpoint = result["ws_endpoint"]
            logger.info(f"🔗 BitBrowser WebSocket endpoint: {ws_endpoint}")

            # Connect to BitBrowser via CDP
            self.browser = await self.playwright.chromium.connect_over_cdp(ws_endpoint)

            # Use existing context from BitBrowser (don't create new one!)
            self.context = self.browser.contexts[0]
            self.context.set_default_timeout(self.timeout)

            # Store BitBrowser manager for cleanup
            self._bitbrowser_manager = bitbrowser_manager

            # Skip context creation for BitBrowser - use existing context and page setup
            self.page = await self.context.new_page()

            # Set exact same headers as working standalone script
            await self.page.set_extra_http_headers(
                {
                    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                }
            )

            # Set up additional page features for BitBrowser
            await self._setup_page_features()
            return  # Skip the normal context creation below
        else:
            # Default to Chromium (for backward compatibility)
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless, args=launch_args
            )

        # Create context with randomized settings and enhanced headers
        context_options = {
            "viewport": self._get_random_viewport(),
            "user_agent": self._get_random_user_agent(),
            "java_script_enabled": True,
            "accept_downloads": True,
            "ignore_https_errors": True,
            "extra_http_headers": {
                "Accept-Language": "en-US,en;q=0.9",  # Critical: Chromium without this gets flagged
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "max-age=0",
                "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',  # Hide HeadlessChrome
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"macOS"',
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "none",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1",
            },
        }

        # Add permissions based on browser engine
        if self.browser_engine in ["chromium", "bit", "fpchrome"]:
            context_options["permissions"] = ["clipboard-read", "clipboard-write"]
        elif self.browser_engine == "webkit":
            # WebKit has more limited clipboard support - only add what's supported
            context_options["permissions"] = ["clipboard-read"]
        elif self.browser_engine == "firefox":
            # Firefox: minimal permissions for basic functionality
            context_options[
                "permissions"
            ] = []  # Empty list, not removing permissions key

        # Add random locale and timezone if randomization enabled
        if self.enable_randomization:
            locales = ["en-US", "en-GB", "en-CA", "en-AU"]
            timezones = [
                "America/New_York",
                "America/Los_Angeles",
                "Europe/London",
                "America/Chicago",
            ]
            context_options.update(
                {
                    "locale": self.randomizer.choice(locales),
                    "timezone_id": self.randomizer.choice(timezones),
                }
            )

        self.context = await self.browser.new_context(**context_options)
        self.context.set_default_timeout(self.timeout)

        # Add lightweight navigator property spoofing (essential for basic fingerprint variation)
        await self.context.add_init_script("""
            () => {
                // Basic automation property cleanup (minimal overhead)
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                    configurable: true
                });

                // Essential language settings (required for service functionality)
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                    configurable: true
                });

                // Basic platform properties (lightweight randomization)
                Object.defineProperty(navigator, 'platform', {
                    get: () => 'MacIntel',
                    configurable: true
                });

                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => 8,
                    configurable: true
                });
            }
        """)

        self.page = await self.context.new_page()

        # Legacy method kept for comparison if needed
        # await self._add_advanced_stealth_scripts()

        # Set up additional page features
        await self._setup_page_features()

    async def grant_permissions(self):
        # Grant permissions if provided, must be done after a page has navigated to a URL
        if self.permissions and self.page and "about:blank" not in self.page.url:
            await self.context.grant_permissions(self.permissions, origin=self.page.url)

    async def _add_advanced_stealth_scripts(self):
        """Add comprehensive anti-detection scripts."""
        stealth_script = f"""
        // === CRITICAL CDP DETECTION PREVENTION ===
        // Remove Playwright-specific global variables
        delete window.__playwright__binding__;
        delete window.__pwInitScripts;
        delete window.__playwright_evaluation_script__;
        
        // Prevent CDP stack trace detection
        const originalError = Error;
        Error = class {{
            constructor(...args) {{
                const error = new originalError(...args);
                if (error.stack) {{
                    error.stack = error.stack.replace(/\\s+at .*chrome-extension.*\\n/g, '');
                    error.stack = error.stack.replace(/\\s+at .*moz-extension.*\\n/g, '');
                }}
                return error;
            }}
        }};
        Error.prototype = originalError.prototype;
        
        // === WEBDRIVER DETECTION REMOVAL ===
        Object.defineProperty(navigator, 'webdriver', {{
            get: () => undefined,
            configurable: true
        }});
        
        // Remove all automation indicators
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_JSON;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Function;
        delete window.$cdc_asdjflasutopfhvcZLmcfl_;
        
        // === REALISTIC NAVIGATOR PROPERTIES ===
        const realisticPlugins = [
            {{ name: 'Chrome PDF Plugin', description: 'Portable Document Format', filename: 'internal-pdf-viewer' }},
            {{ name: 'Chromium PDF Plugin', description: 'Portable Document Format', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai' }},
            {{ name: 'Microsoft Edge PDF Plugin', description: 'Portable Document Format', filename: 'edge-pdf' }},
            {{ name: 'WebKit built-in PDF', description: 'Portable Document Format', filename: 'webkit-pdf' }}
        ];
        
        Object.defineProperty(navigator, 'plugins', {{
            get: () => realisticPlugins,
            configurable: true
        }});
        
        Object.defineProperty(navigator, 'languages', {{
            get: () => ['en-US', 'en'],
            configurable: true
        }});
        
        Object.defineProperty(navigator, 'language', {{
            get: () => 'en-US',
            configurable: true
        }});
        
        Object.defineProperty(navigator, 'hardwareConcurrency', {{
            get: () => {random.randint(4, 16)},
            configurable: true
        }});
        
        Object.defineProperty(navigator, 'deviceMemory', {{
            get: () => {random.choice([4, 8, 16])},
            configurable: true
        }});
        
        Object.defineProperty(navigator, 'platform', {{
            get: () => 'MacIntel',
            configurable: true
        }});
        
        // === REALISTIC SCREEN PROPERTIES ===
        const screenWidth = {random.randint(1280, 1920)};
        const screenHeight = {random.randint(800, 1080)};
        const colorDepth = {random.choice([24, 32])};
        
        Object.defineProperty(screen, 'width', {{
            get: () => screenWidth,
            configurable: true
        }});
        
        Object.defineProperty(screen, 'height', {{
            get: () => screenHeight,
            configurable: true
        }});
        
        Object.defineProperty(screen, 'colorDepth', {{
            get: () => colorDepth,
            configurable: true
        }});
        
        Object.defineProperty(screen, 'pixelDepth', {{
            get: () => colorDepth,
            configurable: true
        }});
        
        Object.defineProperty(screen, 'availWidth', {{
            get: () => screenWidth,
            configurable: true
        }});
        
        Object.defineProperty(screen, 'availHeight', {{
            get: () => screenHeight - 50,  // Account for taskbar
            configurable: true
        }});
        
        // === CHROME OBJECT SPOOFING ===
        if (!window.chrome) {{
            window.chrome = {{}};
        }}
        
        window.chrome.runtime = {{
            OnInstalledReason: {{
                CHROME_UPDATE: "chrome_update",
                INSTALL: "install",
                SHARED_MODULE_UPDATE: "shared_module_update",
                UPDATE: "update"
            }},
            OnRestartRequiredReason: {{
                APP_UPDATE: "app_update",
                OS_UPDATE: "os_update",
                PERIODIC: "periodic"
            }},
            PlatformArch: {{
                ARM: "arm",
                ARM64: "arm64",
                MIPS: "mips",
                MIPS64: "mips64",
                X86_32: "x86-32",
                X86_64: "x86-64"
            }},
            PlatformNaclArch: {{
                ARM: "arm",
                MIPS: "mips",
                MIPS64: "mips64",
                X86_32: "x86-32",
                X86_64: "x86-64"
            }},
            PlatformOs: {{
                ANDROID: "android",
                CROS: "cros",
                LINUX: "linux",
                MAC: "mac",
                OPENBSD: "openbsd",
                WIN: "win"
            }},
            RequestUpdateCheckStatus: {{
                NO_UPDATE: "no_update",
                THROTTLED: "throttled",
                UPDATE_AVAILABLE: "update_available"
            }}
        }};
        
        // Add loadTimes (Chrome-specific)
        window.chrome.loadTimes = function() {{
            return {{
                commitLoadTime: performance.timing.responseStart / 1000,
                connectionInfo: 'http/1.1',
                finishDocumentLoadTime: performance.timing.domContentLoadedEventEnd / 1000,
                finishLoadTime: performance.timing.loadEventEnd / 1000,
                firstPaintAfterLoadTime: 0,
                firstPaintTime: performance.timing.responseStart / 1000,
                navigationType: 'Other',
                npnNegotiatedProtocol: 'unknown',
                requestTime: performance.timing.navigationStart / 1000,
                startLoadTime: performance.timing.navigationStart / 1000,
                wasAlternateProtocolAvailable: false,
                wasFetchedViaSpdy: false,
                wasNpnNegotiated: false
            }};
        }};
        
        // Add csi (Chrome-specific)
        window.chrome.csi = function() {{
            return {{
                pageT: performance.timing.loadEventEnd - performance.timing.navigationStart,
                startE: performance.timing.navigationStart,
                tran: 15
            }};
        }};
        
        // === CANVAS FINGERPRINT RANDOMIZATION ===
        const getImageData = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function(...args) {{
            const result = getImageData.apply(this, args);
            // Add slight randomization to canvas data to avoid consistent fingerprints
            const noise = Math.random() * 0.0001;
            return result + noise;
        }};
        
        // === WEBGL FINGERPRINT RANDOMIZATION ===
        const getContext = HTMLCanvasElement.prototype.getContext;
        HTMLCanvasElement.prototype.getContext = function(contextType, ...args) {{
            const context = getContext.apply(this, [contextType, ...args]);
            if (contextType === 'webgl' || contextType === 'experimental-webgl') {{
                const getParameter = context.getParameter;
                context.getParameter = function(parameter) {{
                    // Spoof WebGL parameters to avoid detection
                    if (parameter === context.RENDERER) {{
                        return 'Intel Iris OpenGL Engine';
                    }}
                    if (parameter === context.VENDOR) {{
                        return 'Intel Inc.';
                    }}
                    if (parameter === context.VERSION) {{
                        return 'OpenGL ES 2.0 (WebGL 1.0)';
                    }}
                    if (parameter === context.SHADING_LANGUAGE_VERSION) {{
                        return 'OpenGL ES GLSL ES 1.0 (WebGL)';
                    }}
                    return getParameter.apply(this, arguments);
                }};
            }}
            return context;
        }};
        
        // === TIMING ATTACKS MITIGATION ===
        const originalPerformanceNow = performance.now;
        performance.now = function() {{
            // Add small random jitter to prevent timing-based detection
            return originalPerformanceNow.apply(this, arguments) + (Math.random() - 0.5) * 0.1;
        }};
        
        // === MEDIA DEVICES SPOOFING ===
        if (navigator.mediaDevices) {{
            const originalEnumerateDevices = navigator.mediaDevices.enumerateDevices;
            navigator.mediaDevices.enumerateDevices = function() {{
                return Promise.resolve([
                    {{ deviceId: 'default', groupId: 'group1', kind: 'audioinput', label: 'Default - Built-in Microphone' }},
                    {{ deviceId: 'communications', groupId: 'group1', kind: 'audioinput', label: 'Communications - Built-in Microphone' }},
                    {{ deviceId: 'default', groupId: 'group2', kind: 'audiooutput', label: 'Default - Built-in Output' }}
                ]);
            }};
        }}
        
        // === NOTIFICATION PERMISSION SPOOFING ===
        if (navigator.permissions) {{
            const originalPermissionsQuery = navigator.permissions.query;
            navigator.permissions.query = function(parameters) {{
                if (parameters.name === 'notifications') {{
                    return Promise.resolve({{ state: 'default' }});
                }}
                return originalPermissionsQuery.apply(this, arguments);
            }};
        }}
        
        // === BATTERY API SPOOFING ===
        if (navigator.getBattery) {{
            const originalGetBattery = navigator.getBattery;
            navigator.getBattery = function() {{
                return Promise.resolve({{
                    charging: true,
                    chargingTime: Infinity,
                    dischargingTime: Infinity,
                    level: {random.uniform(0.8, 1.0):.2f}
                }});
            }};
        }}
        
        // === PREVENT HEADLESS DETECTION ===
        Object.defineProperty(navigator, 'maxTouchPoints', {{
            get: () => 1,
            configurable: true
        }});
        
        // === MOUSE EVENT SIMULATION ===
        let mouseX = {random.randint(100, 800)};
        let mouseY = {random.randint(100, 600)};
        
        // Simulate realistic mouse movements
        setInterval(() => {{
            mouseX += (Math.random() - 0.5) * 5;
            mouseY += (Math.random() - 0.5) * 5;
            mouseX = Math.max(0, Math.min(window.innerWidth || 1280, mouseX));
            mouseY = Math.max(0, Math.min(window.innerHeight || 720, mouseY));
            
            // Store mouse position for potential queries
            window._simulatedMouseX = mouseX;
            window._simulatedMouseY = mouseY;
        }}, {random.randint(200, 800)});
        
        // === PREVENT CONSOLE DEBUG DETECTION ===
        const devtools = {{
            open: false,
            orientation: null
        }};
        
        const threshold = 160;
        
        setInterval(() => {{
            if (window.outerHeight - window.innerHeight > threshold || 
                window.outerWidth - window.innerWidth > threshold) {{
                if (!devtools.open) {{
                    devtools.open = true;
                    devtools.orientation = window.outerWidth - window.innerWidth > threshold ? 'vertical' : 'horizontal';
                }}
            }} else {{
                if (devtools.open) {{
                    devtools.open = false;
                    devtools.orientation = null;
                }}
            }}
        }}, 500);
        
        // Hide devtools detection from Firecrawl
        Object.defineProperty(window, 'devtools', {{
            get: () => ({{ open: false, orientation: null }}),
            configurable: false
        }});
        
        console.log('🛡️ Advanced anti-detection measures loaded (CDP-resistant)');
        """

        await self.context.add_init_script(stealth_script)

    async def _setup_ungoogled_chromium_features(self):
        """Set up enhanced features specific to Ungoogled Chromium."""
        if not self.page:
            return

        # Inject scripts to leverage Ungoogled Chromium's automation features
        await self.context.add_init_script("""
            () => {
                // Leverage Ungoogled Chromium's fakeShadowRoot feature
                if (typeof window.fakeShadowRoot !== 'undefined') {
                    console.log('🛡️ Ungoogled Chromium fakeShadowRoot available');
                }
                
                // Enhanced webdriver property hiding
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    configurable: true
                });
                
                // Remove automation traces
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
                delete window.$cdc_asdjflasutopfhvcZLmcfl_;
                
                // Enhanced CDP detection avoidance
                const originalError = Error;
                Error = class extends originalError {
                    constructor(...args) {
                        super(...args);
                        if (this.stack) {
                            this.stack = this.stack.replace(/\\s+at .*chrome-extension.*\\n/g, '');
                            this.stack = this.stack.replace(/\\s+at .*puppeteer.*\\n/g, '');
                            this.stack = this.stack.replace(/\\s+at .*playwright.*\\n/g, '');
                        }
                    }
                };
                
                // Override console methods to hide automation traces
                const originalLog = console.log;
                console.log = function(...args) {
                    const message = args.join(' ');
                    if (!message.includes('DevTools') && !message.includes('automation')) {
                        originalLog.apply(console, args);
                    }
                };
                
                console.log('🛡️ Enhanced Ungoogled Chromium stealth features loaded');
            }
        """)

    async def _setup_page_features(self):
        """Set up additional page features for human-like behavior."""
        if not self.page:
            return

        # Block images and videos to speed up loading (optional)
        # await self.page.route("**/*.{png,jpg,jpeg,gif,webp,svg,mp4,avi,mov}", lambda route: route.abort())

        # Set up event listeners for human-like behavior
        if self.enable_randomization:
            # Generate random values in Python and inject them into JS
            random_offset_range = self.randomizer.uniform(-2, 2)
            random_delay_base = self.randomizer.uniform(50, 150)
            await self.page.evaluate(f"""
                // Add random mouse movements on page interactions
                document.addEventListener('click', (e) => {{
                    // Small random delay after clicks
                    setTimeout(() => {{
                        const x = e.clientX + {random_offset_range};
                        const y = e.clientY + {random_offset_range};
                    }}, {random_delay_base});
                }});
            """)

    async def stop(self):
        """Stop browser and cleanup."""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

        # Clean up temporary user data directory for fpchrome
        if hasattr(self, "_temp_user_data") and self._temp_user_data:
            try:
                import shutil

                shutil.rmtree(self._temp_user_data, ignore_errors=True)
                logger.info(
                    f"🧹 Cleaned up fpchrome temp directory: {self._temp_user_data}"
                )
            except Exception as e:
                logger.warning(
                    f"⚠️ Could not clean up temp directory {self._temp_user_data}: {e}"
                )

        # Clean up BitBrowser if it was used
        if hasattr(self, "_bitbrowser_manager") and self._bitbrowser_manager:
            try:
                await self._bitbrowser_manager.ensure_browser_closed()
            except Exception as e:
                logger.warning(f"Error closing BitBrowser: {e}")
            finally:
                self._bitbrowser_manager = None

    async def goto(self, url: str, wait_until: str = "networkidle"):
        """Navigate to URL with random delays."""
        if not self.page:
            raise RuntimeError("Browser not started")

        # Add random pre-navigation delay
        if self.enable_randomization:
            await self.randomizer.delay(0.5, 2.0)

        await self.page.goto(url, wait_until=wait_until)
        await self.grant_permissions()

        # Add random post-navigation delay
        if self.enable_randomization:
            await self.randomizer.delay(1.0, 3.0)
            await self._simulate_reading_behavior()

    async def wait_for_selector(self, selector: str, timeout: Optional[int] = None):
        """Wait for element to appear."""
        if not self.page:
            raise RuntimeError("Browser not started")
        return await self.page.wait_for_selector(selector, timeout=timeout)

    async def fill(self, selector: str, value: str, human_like: bool = True):
        """Fill input field with human-like typing."""
        if not self.page:
            raise RuntimeError("Browser not started")

        element = await self.page.wait_for_selector(selector)
        await element.click()  # Focus the element

        if human_like and self.enable_randomization:
            await self.randomizer.human_type(element, value)
        else:
            await element.fill(value)

    async def click(self, selector: str, human_like: bool = True):
        """Click element with human-like behavior."""
        if not self.page:
            raise RuntimeError("Browser not started")

        element = await self.page.wait_for_selector(selector)

        if human_like and self.enable_randomization:
            await self.randomizer.human_click(self.page, element)
        else:
            await element.click()

    async def get_text(self, selector: str) -> str:
        """Get text content of element."""
        if not self.page:
            raise RuntimeError("Browser not started")
        element = await self.page.wait_for_selector(selector)
        return await element.text_content() or ""

    async def screenshot(self, path: str = "screenshot.png"):
        """Take screenshot."""
        if not self.page:
            raise RuntimeError("Browser not started")
        await self.page.screenshot(path=path)

    async def wait(self, ms: int):
        """Wait for specified milliseconds."""
        await asyncio.sleep(ms / 1000)

    async def random_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0):
        """Add random delay between actions."""
        if self.enable_randomization:
            await self.randomizer.delay(min_seconds, max_seconds)

    async def random_short_delay(self):
        """Add a short random delay (0.5-1.5 seconds)."""
        await self.randomizer.short_delay()

    async def random_medium_delay(self):
        """Add a medium random delay (3-5 seconds)."""
        await self.randomizer.medium_delay()

    async def random_long_delay(self):
        """Add a long random delay (10-20 seconds)."""
        await self.randomizer.long_delay()

    async def random_page_load_delay(self):
        """Add random delay for page loading simulation."""
        await self.randomizer.page_load_delay()

    async def random_user_action_delay(self):
        """Add random delay between user actions."""
        await self.randomizer.user_action_delay()

    async def random_scroll(self):
        """Perform random scrolling to simulate reading."""
        if not self.page or not self.enable_randomization:
            return

        try:
            await self.randomizer.random_scroll(self.page)
        except Exception:
            pass  # Ignore scroll errors

    async def _simulate_reading_behavior(self):
        """Simulate human reading behavior on page load."""
        if not self.enable_randomization:
            return

        try:
            await self.randomizer.simulate_reading_behavior(self.page)
        except Exception:
            pass  # Ignore any errors during reading simulation
