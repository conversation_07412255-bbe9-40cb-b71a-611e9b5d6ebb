"""Email domain selector with weighted load balancing."""

from typing import List, Dict, Optional
from pydantic import BaseModel


class EmailDomain(BaseModel):
    """Email domain configuration."""

    domain: str
    weight: int = 100  # Default weight
    enabled: bool = True


class DomainSelector:
    """Select email domains based on weights for load balancing."""

    def __init__(self, domains: List[EmailDomain], randomizer=None):
        self.domains = [d for d in domains if d.enabled]
        self.randomizer = randomizer  # Optional randomizer injection
        self._calculate_cumulative_weights()

    def _calculate_cumulative_weights(self):
        """Calculate cumulative weights for weighted random selection."""
        self.total_weight = sum(d.weight for d in self.domains)
        self.cumulative_weights = []
        cumsum = 0
        for domain in self.domains:
            cumsum += domain.weight
            self.cumulative_weights.append((cumsum, domain))

    def select_domain(self) -> Optional[str]:
        """Select a domain based on weighted random selection."""
        if not self.domains:
            return None

        if len(self.domains) == 1:
            return self.domains[0].domain

        # Weighted random selection
        if self.randomizer:
            rand = self.randomizer.randint(1, self.total_weight)
        else:
            # Fallback to Python's random for backwards compatibility
            import random

            rand = random.randint(1, self.total_weight)
        for cumsum, domain in self.cumulative_weights:
            if rand <= cumsum:
                return domain.domain

        # Fallback (shouldn't reach here)
        return self.domains[0].domain

    def get_all_domains(self) -> List[str]:
        """Get all configured domains."""
        return [d.domain for d in self.domains]

    def get_domain_stats(self) -> Dict[str, float]:
        """Get domain selection probabilities."""
        if not self.total_weight:
            return {}

        return {d.domain: (d.weight / self.total_weight) * 100 for d in self.domains}
