"""Email parsing service for extracting magic links and other data from service emails."""

from typing import Optional, Dict, Any
from pathlib import Path
import yaml
import logging
from core.service_registry import ServiceRegistry

logger = logging.getLogger(__name__)


class EmailParser:
    """Service-specific email parser that delegates to service modules."""

    def __init__(self, config_path: Optional[Path] = None):
        """Initialize parser with service configurations."""
        self.config_path = config_path or Path("config.yaml")
        self.services_config = self._load_services_config()

    def _load_services_config(self) -> Dict[str, Any]:
        """Load services configuration from config file."""
        if not self.config_path.exists():
            logger.warning(f"Config file not found: {self.config_path}")
            return {}

        try:
            with open(self.config_path, "r") as f:
                config = yaml.safe_load(f)
                return config.get("services", {})
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return {}

    def detect_service_from_email(
        self, from_address: str, sender_domain: str
    ) -> Optional[str]:
        """Detect service based on sender domain using configured sender_domains."""
        for service_name, service_config in self.services_config.items():
            if not service_config.get("enabled", True):
                continue

            sender_domains = service_config.get("sender_domains", [])
            for domain in sender_domains:
                # Check for exact match or subdomain match
                if sender_domain == domain or sender_domain.endswith("." + domain):
                    logger.info(
                        f"Detected service '{service_name}' from sender domain '{sender_domain}'"
                    )
                    return service_name

        return None

    def parse_magic_link(self, service: str, raw_email_content: str) -> Optional[str]:
        """Parse magic link from email content using service-specific logic."""
        return ServiceRegistry.parse_magic_link(service, raw_email_content)

    def parse_email_data(self, email_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse email data and extract magic link using service-specific logic."""
        service = email_data.get("service")
        if not service:
            return email_data

        return ServiceRegistry.parse_email_data(service, email_data)
