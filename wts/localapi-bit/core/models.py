"""Data models for regbot automation."""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class EmailMessage(BaseModel):
    """Email message model."""

    from_address: str
    to_address: str
    subject: str
    body: str
    html_body: Optional[str] = None
    received_at: datetime = Field(default_factory=datetime.now)
    service: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class RegistrationResult(BaseModel):
    """Result of a service registration."""

    service: str
    success: bool
    api_key: Optional[str] = None
    username: Optional[str] = None
    email: Optional[str] = None
    password: Optional[str] = None
    search_successful: Optional[bool] = None  # Track search success for ExaAI
    error_message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ServiceCredentials(BaseModel):
    """Stored service credentials."""

    service: str
    api_key: str
    username: Optional[str] = None
    email: Optional[str] = None
    password: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(default_factory=dict)
