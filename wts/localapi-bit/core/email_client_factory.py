"""Factory for creating email retrieval clients based on configuration."""

from typing import Union, Optional
from pathlib import Path
import yaml
import logging

from .webhook_client import WebhookClient
from .kv_client import CloudflareKVClient

logger = logging.getLogger(__name__)


class EmailClientFactory:
    """Factory for creating email retrieval clients based on configuration."""

    @staticmethod
    def create_client(
        config_path: Optional[Path] = None,
    ) -> Union[WebhookClient, CloudflareKVClient]:
        """Create appropriate email client based on configuration.

        Args:
            config_path: Path to config.yaml file

        Returns:
            WebhookClient or CloudflareKVClient based on config

        Raises:
            ValueError: If client_type is not supported or configuration is invalid
        """
        config_path = config_path or Path("config.yaml")

        try:
            config = EmailClientFactory._load_config(config_path)

            # Get email retrieval configuration
            email_retrieval = config.get("email_retrieval", {})
            client_type = email_retrieval.get("client_type", "webhook").lower()

            if client_type == "webhook":
                return EmailClientFactory._create_webhook_client(config)
            elif client_type == "kv":
                return EmailClientFactory._create_kv_client(config_path)
            else:
                raise ValueError(
                    f"Unsupported client_type: {client_type}. Must be 'webhook' or 'kv'"
                )

        except Exception as e:
            logger.error(f"Error creating email client: {e}")
            # Fallback to webhook client
            logger.info("Falling back to default webhook client")
            return WebhookClient()

    @staticmethod
    def _load_config(config_path: Path) -> dict:
        """Load configuration from file."""
        if not config_path.exists():
            raise FileNotFoundError(f"Config file not found: {config_path}")

        with open(config_path, "r") as f:
            return yaml.safe_load(f) or {}

    @staticmethod
    def _create_webhook_client(config: dict) -> WebhookClient:
        """Create webhook client from configuration."""
        email_retrieval = config.get("email_retrieval", {})
        webhook_config = email_retrieval.get("webhook", {})

        # Use local baseurl for automation scripts
        webhook_url = webhook_config.get("local_baseurl", "http://localhost:8888")

        logger.info(f"Creating webhook client with URL: {webhook_url}")
        return WebhookClient(webhook_url=webhook_url)

    @staticmethod
    def _create_kv_client(config_path: Path) -> CloudflareKVClient:
        """Create KV client from configuration."""
        logger.info("Creating Cloudflare KV client")
        return CloudflareKVClient(config_path=config_path)
