"""Storage handler for API keys and credentials."""

from pathlib import Path
from typing import Dict, List, Optional
import yaml
from datetime import datetime
from .models import ServiceCredentials


class Storage:
    """Handle storage of API keys and credentials."""

    def __init__(self, storage_path: Path):
        self.storage_path = storage_path
        self.storage_path.parent.mkdir(parents=True, exist_ok=True)
        self._data: Dict[str, ServiceCredentials] = {}
        self.load()

    def load(self):
        """Load credentials from YAML file."""
        if self.storage_path.exists():
            with open(self.storage_path, "r") as f:
                data = yaml.safe_load(f) or {}
                for service, creds in data.items():
                    self._data[service] = ServiceCredentials(**creds)

    def save(self):
        """Save credentials to YAML file."""
        data = {}
        for service, creds in self._data.items():
            data[service] = creds.model_dump(mode="json")

        with open(self.storage_path, "w") as f:
            yaml.dump(data, f, default_flow_style=False)

    def add_credential(self, credential: ServiceCredentials):
        """Add or update a credential."""
        self._data[credential.service] = credential
        self.save()

    def get_credential(self, service: str) -> Optional[ServiceCredentials]:
        """Get credential for a service."""
        return self._data.get(service)

    def list_services(self) -> List[str]:
        """List all services with stored credentials."""
        return list(self._data.keys())

    def remove_credential(self, service: str) -> bool:
        """Remove a credential."""
        if service in self._data:
            del self._data[service]
            self.save()
            return True
        return False
