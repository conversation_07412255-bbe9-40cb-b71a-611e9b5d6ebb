"""Real-time monitoring and analytics for RegBot continuous automation."""

import asyncio
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging

from core.config import Config
from core.continuous_automation import ContinuousAutomationManager, SessionStatus
from core.results_storage import ResultsStorage

logger = logging.getLogger(__name__)


class MonitoringAnalytics:
    """Real-time monitoring and analytics for continuous automation."""

    def __init__(self, config: Config):
        self.config = config
        self.storage = ResultsStorage()
        self.session_dir = Path(config.data_path) / "sessions"
        self.analytics_file = self.session_dir / "analytics.json"
        self.session_dir.mkdir(parents=True, exist_ok=True)

    def get_current_session_status(self) -> Optional[Dict[str, Any]]:
        """Get current session status if any."""
        try:
            manager = ContinuousAutomationManager(
                self.config, self.config.continuous_automation
            )
            session = manager.load_session()

            if not session:
                return None

            return manager.get_session_summary()
        except Exception as e:
            logger.error(f"Error getting session status: {e}")
            return None

    def get_historical_analytics(self) -> Dict[str, Any]:
        """Get historical analytics data."""
        if not self.analytics_file.exists():
            return self._initialize_analytics()

        try:
            with open(self.analytics_file, "r") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading analytics: {e}")
            return self._initialize_analytics()

    def _initialize_analytics(self) -> Dict[str, Any]:
        """Initialize analytics data structure."""
        return {
            "created_at": datetime.now().isoformat(),
            "total_sessions": 0,
            "total_registrations": 0,
            "total_attempts": 0,
            "services": {},
            "daily_stats": {},
            "hourly_stats": {},
            "domain_usage": {},
            "success_rates": {"overall": 0.0, "by_service": {}, "by_domain_group": {}},
        }

    def update_analytics(self, session_data: Dict[str, Any]):
        """Update analytics with session data."""
        analytics = self.get_historical_analytics()

        # Update totals
        analytics["total_sessions"] += 1

        # Update service-specific stats
        service = session_data.get("service", "unknown")
        if service not in analytics["services"]:
            analytics["services"][service] = {
                "total_sessions": 0,
                "total_registrations": 0,
                "total_attempts": 0,
                "success_rate": 0.0,
                "last_session": None,
            }

        service_stats = analytics["services"][service]
        service_stats["total_sessions"] += 1
        service_stats["last_session"] = datetime.now().isoformat()

        # Update daily stats
        today = datetime.now().strftime("%Y-%m-%d")
        if today not in analytics["daily_stats"]:
            analytics["daily_stats"][today] = {
                "sessions": 0,
                "registrations": 0,
                "attempts": 0,
            }

        analytics["daily_stats"][today]["sessions"] += 1

        # Save analytics
        self._save_analytics(analytics)

    def _save_analytics(self, analytics: Dict[str, Any]):
        """Save analytics to file."""
        try:
            with open(self.analytics_file, "w") as f:
                json.dump(analytics, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving analytics: {e}")

    def get_service_credentials_stats(self) -> Dict[str, Any]:
        """Get statistics about stored service credentials."""
        summary = self.storage.get_service_summary()
        stats = {}

        for service, count in summary.items():
            latest = self.storage.get_latest_credential(service)
            stats[service] = {
                "total_credentials": count,
                "latest_created": latest.get("created_at", "Unknown")
                if latest
                else None,
                "latest_email": latest.get("email", "Unknown") if latest else None,
            }

        return stats

    def calculate_domain_load_distribution(self) -> Dict[str, Any]:
        """Calculate load distribution across domain groups."""
        domain_stats = {}

        for group_name, domains in self.config.email_domain_groups.items():
            enabled_domains = [d for d in domains if d.enabled]
            total_weight = sum(d.weight for d in enabled_domains)

            group_stats = {
                "total_domains": len(domains),
                "enabled_domains": len(enabled_domains),
                "total_weight": total_weight,
                "load_distribution": {},
            }

            for domain in enabled_domains:
                if total_weight > 0:
                    percentage = (domain.weight / total_weight) * 100
                    group_stats["load_distribution"][domain.domain] = {
                        "weight": domain.weight,
                        "percentage": round(percentage, 2),
                    }

            domain_stats[group_name] = group_stats

        return domain_stats

    def get_system_health(self) -> Dict[str, Any]:
        """Get system health metrics."""
        health = {"status": "healthy", "issues": [], "recommendations": []}

        # Check domain configuration
        enabled_domains = sum(
            len([d for d in domains if d.enabled])
            for domains in self.config.email_domain_groups.values()
        )

        if enabled_domains == 0:
            health["status"] = "critical"
            health["issues"].append("No email domains are enabled")
            health["recommendations"].append(
                "Enable at least one email domain in config.yaml"
            )
        elif enabled_domains < 2:
            health["status"] = "warning"
            health["issues"].append("Only one email domain enabled - no redundancy")
            health["recommendations"].append(
                "Enable additional email domains for better load distribution"
            )

        # Check service configuration
        enabled_services = [
            name for name, svc in self.config.services.items() if svc.enabled
        ]

        if not enabled_services:
            health["status"] = "critical"
            health["issues"].append("No services are enabled")
            health["recommendations"].append(
                "Enable at least one service in config.yaml"
            )

        # Check webhook configuration
        if hasattr(self.config, "email_retrieval"):
            webhook_config = getattr(self.config.email_retrieval, "webhook", None)
            if webhook_config and not getattr(webhook_config, "external_baseurl", None):
                health["status"] = "warning"
                health["issues"].append("No external webhook URL configured")
                health["recommendations"].append(
                    "Configure external_baseurl for email interception"
                )

        return health

    async def display_realtime_monitor(self, refresh_seconds: int = 5):
        """Display real-time monitoring dashboard."""
        try:
            while True:
                # Clear screen (works on most terminals)
                print("\033[2J\033[H")

                # Header
                print(f"🤖 RegBot Real-Time Monitor")
                print(f"{'=' * 60}")
                print(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print()

                # Current session status
                session_status = self.get_current_session_status()
                if session_status:
                    print("📊 Current Session:")
                    print(f"   Service: {session_status['service']}")
                    print(f"   Status: {session_status['status']}")
                    print(f"   Progress: {session_status['progress']}")
                    print(f"   Success Rate: {session_status['success_rate']}")
                    print(f"   Duration: {session_status['duration']}")

                    if session_status["next_run"]:
                        next_run = datetime.fromisoformat(
                            session_status["next_run"].replace("Z", "+00:00")
                        )
                        time_until = next_run - datetime.now()
                        if time_until.total_seconds() > 0:
                            print(f"   Next Run: in {str(time_until).split('.')[0]}")
                        else:
                            print(f"   Next Run: due now")

                    if session_status["consecutive_failures"] > 0:
                        print(
                            f"   ⚠️  Consecutive Failures: {session_status['consecutive_failures']}"
                        )
                else:
                    print("📊 Current Session: None active")

                print()

                # Service credentials
                cred_stats = self.get_service_credentials_stats()
                if cred_stats:
                    print("🔑 Service Credentials:")
                    for service, stats in cred_stats.items():
                        print(f"   {service}: {stats['total_credentials']} API keys")
                        if stats["latest_created"]:
                            print(
                                f"      Latest: {stats['latest_created']} ({stats['latest_email']})"
                            )
                else:
                    print("🔑 Service Credentials: None stored")

                print()

                # Domain load distribution
                domain_stats = self.calculate_domain_load_distribution()
                print("🌐 Domain Load Distribution:")
                for group_name, stats in domain_stats.items():
                    print(
                        f"   {group_name}: {stats['enabled_domains']}/{stats['total_domains']} enabled"
                    )
                    for domain, load_info in stats["load_distribution"].items():
                        print(
                            f"      {domain}: {load_info['percentage']}% (weight: {load_info['weight']})"
                        )

                print()

                # System health
                health = self.get_system_health()
                status_icon = (
                    "✅"
                    if health["status"] == "healthy"
                    else ("⚠️" if health["status"] == "warning" else "❌")
                )
                print(f"🏥 System Health: {status_icon} {health['status'].upper()}")

                if health["issues"]:
                    print("   Issues:")
                    for issue in health["issues"]:
                        print(f"      - {issue}")

                if health["recommendations"]:
                    print("   Recommendations:")
                    for rec in health["recommendations"]:
                        print(f"      - {rec}")

                print()
                print(
                    f"Refreshing in {refresh_seconds} seconds... (Press Ctrl+C to exit)"
                )

                # Wait for next refresh
                await asyncio.sleep(refresh_seconds)

        except KeyboardInterrupt:
            print("\nMonitoring stopped by user")
        except Exception as e:
            print(f"Error in monitoring: {e}")

    def generate_analytics_report(self) -> str:
        """Generate a comprehensive analytics report."""
        analytics = self.get_historical_analytics()
        session_status = self.get_current_session_status()
        cred_stats = self.get_service_credentials_stats()
        domain_stats = self.calculate_domain_load_distribution()
        health = self.get_system_health()

        report = []
        report.append("📊 RegBot Analytics Report")
        report.append("=" * 50)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # Overall statistics
        report.append("📈 Overall Statistics:")
        report.append(f"   Total Sessions: {analytics['total_sessions']}")
        report.append(f"   Total Registrations: {analytics['total_registrations']}")
        report.append(f"   Total Attempts: {analytics['total_attempts']}")

        if analytics["total_attempts"] > 0:
            overall_rate = (
                analytics["total_registrations"] / analytics["total_attempts"]
            ) * 100
            report.append(f"   Overall Success Rate: {overall_rate:.1f}%")

        report.append("")

        # Current session
        if session_status:
            report.append("🔄 Current Session:")
            report.append(f"   Service: {session_status['service']}")
            report.append(f"   Status: {session_status['status']}")
            report.append(f"   Progress: {session_status['progress']}")
            report.append(f"   Success Rate: {session_status['success_rate']}")
            report.append(f"   Duration: {session_status['duration']}")
        else:
            report.append("🔄 Current Session: None active")

        report.append("")

        # Service statistics
        if analytics["services"]:
            report.append("🎯 Service Statistics:")
            for service, stats in analytics["services"].items():
                report.append(f"   {service}:")
                report.append(f"      Sessions: {stats['total_sessions']}")
                report.append(f"      Registrations: {stats['total_registrations']}")
                report.append(f"      Success Rate: {stats['success_rate']:.1f}%")

        report.append("")

        # Stored credentials
        if cred_stats:
            report.append("🔑 Stored Credentials:")
            for service, stats in cred_stats.items():
                report.append(f"   {service}: {stats['total_credentials']} API keys")

        report.append("")

        # Domain configuration
        report.append("🌐 Domain Configuration:")
        for group_name, stats in domain_stats.items():
            report.append(f"   {group_name}:")
            report.append(
                f"      Domains: {stats['enabled_domains']}/{stats['total_domains']} enabled"
            )
            report.append(f"      Total Weight: {stats['total_weight']}")

        report.append("")

        # System health
        status_icon = (
            "✅"
            if health["status"] == "healthy"
            else ("⚠️" if health["status"] == "warning" else "❌")
        )
        report.append(f"🏥 System Health: {status_icon} {health['status'].upper()}")

        if health["issues"]:
            report.append("   Issues:")
            for issue in health["issues"]:
                report.append(f"      - {issue}")

        if health["recommendations"]:
            report.append("   Recommendations:")
            for rec in health["recommendations"]:
                report.append(f"      - {rec}")

        return "\n".join(report)
