"""Cloudflare KV client for direct access to raw email content (alternative to webhook server)."""

import httpx
import json
from typing import Optional, Dict, Any
from pathlib import Path
import yaml
import logging

logger = logging.getLogger(__name__)


class CloudflareKVClient:
    """Client for retrieving raw email content from Cloudflare KV.

    This provides an alternative to the webhook server for environments where
    running a webhook server is not feasible.
    """

    def __init__(self, config_path: Optional[Path] = None):
        """Initialize KV client with Cloudflare credentials."""
        self.config_path = config_path or Path("config.yaml")
        self.config = self._load_config()

        self.account_id = self.config.get("cloudflare_account_id")
        self.namespace_id = self.config.get("cloudflare_namespace_id")
        self.api_token = self.config.get("cloudflare_api_token")

        if not all([self.account_id, self.namespace_id, self.api_token]):
            logger.warning("Cloudflare KV credentials not fully configured")

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from config file."""
        if not self.config_path.exists():
            logger.warning(f"Config file not found: {self.config_path}")
            return {}

        try:
            with open(self.config_path, "r") as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return {}

    async def get_latest_email_content(self, service: str, email: str) -> Optional[str]:
        """Get raw email content for a specific email address from KV."""
        email_data = await self.get_latest_email_data(service, email)
        if email_data:
            return email_data.get("raw", "")
        return None

    async def get_latest_email_data(
        self, service: str, email: str
    ) -> Optional[Dict[str, Any]]:
        """Get complete email data for a specific email address from KV."""
        if not self._is_configured():
            logger.error("KV client not properly configured")
            return None

        try:
            # Try latest email key first
            email_user = email.split("@")[0]
            key = f"latest_{service}_email"

            email_data = await self._get_kv_value(key)
            if email_data and email_data.get("to_address", "").startswith(email_user):
                logger.info(
                    f"Retrieved latest email data for {service}/{email} from KV"
                )
                return email_data

            # If not found, try to find by email pattern
            # This is a fallback - in practice, the latest key should work
            logger.warning(f"No matching email found in KV for {service}/{email}")
            return None

        except Exception as e:
            logger.error(f"Error retrieving email from KV: {e}")
            return None

    async def _get_kv_value(self, key: str) -> Optional[Dict[str, Any]]:
        """Get value from KV by key."""
        try:
            url = f"https://api.cloudflare.com/client/v4/accounts/{self.account_id}/storage/kv/namespaces/{self.namespace_id}/values/{key}"

            headers = {
                "Authorization": f"Bearer {self.api_token}",
                "Content-Type": "application/json",
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)

                if response.status_code == 200:
                    return response.json()
                else:
                    return None

        except Exception as e:
            logger.error(f"Error getting KV value for {key}: {e}")
            return None

    def _is_configured(self) -> bool:
        """Check if KV client is properly configured."""
        return bool(self.account_id and self.namespace_id and self.api_token)
