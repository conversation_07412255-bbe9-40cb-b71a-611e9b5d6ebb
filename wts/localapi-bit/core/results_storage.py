"""Improved storage handler for API keys and credentials with service-specific files."""

from pathlib import Path
from typing import Dict, List, Optional, Any
import yaml
from datetime import datetime
import fcntl
import time
from .models import ServiceCredentials


class ResultsStorage:
    """Handle storage of API keys and credentials with improved structure and file locking."""

    def __init__(self, base_path: Path = None):
        if base_path is None:
            base_path = Path("data/results")

        self.base_path = base_path
        self.base_path.mkdir(parents=True, exist_ok=True)

    def _get_service_file(self, service: str) -> Path:
        """Get the YAML file path for a specific service."""
        return self.base_path / f"{service}.yaml"

    def _load_service_data(self, service: str) -> Dict[str, List[Dict[str, Any]]]:
        """Load existing data for a service with file locking."""
        service_file = self._get_service_file(service)
        if service_file.exists():
            # Retry logic for file locking
            for attempt in range(5):
                try:
                    with open(service_file, "r") as f:
                        fcntl.flock(
                            f.fileno(), fcntl.LOCK_SH
                        )  # Shared lock for reading
                        data = yaml.safe_load(f) or {service: []}
                        fcntl.flock(f.fileno(), fcntl.LOCK_UN)  # Unlock
                        return data
                except (IOError, OSError) as e:
                    if attempt < 4:
                        time.sleep(0.1 * (attempt + 1))  # Exponential backoff
                        continue
                    else:
                        print(
                            f"Warning: Could not lock file {service_file} for reading: {e}"
                        )
                        # Fallback: read without lock
                        with open(service_file, "r") as f:
                            return yaml.safe_load(f) or {service: []}
        return {service: []}

    def add_credential(self, credential: ServiceCredentials):
        """Add a new credential to the service file with atomic write and file locking."""
        service = credential.service
        service_file = self._get_service_file(service)

        # Retry logic for file locking
        for attempt in range(5):
            try:
                # Load existing data into memory
                data = self._load_service_data(service)

                # Create simplified credential entry
                new_entry = {
                    "api_key": credential.api_key,
                    "created_at": credential.created_at.isoformat(),
                    "account": credential.email,
                }

                # Add password if available
                if hasattr(credential, "password") and credential.password:
                    new_entry["password"] = credential.password

                # Add service-specific fields
                if service == "assemblyai":
                    # For AssemblyAI, we only need api_key, created_at, and account
                    pass
                else:
                    # For other services, we might need username or other fields
                    if credential.username:
                        new_entry["username"] = credential.username

                # Add to the in-memory data structure
                if service not in data:
                    data[service] = []
                data[service].append(new_entry)

                # Atomic write with exclusive lock
                temp_file = service_file.with_suffix(".tmp")
                with open(temp_file, "w") as f:
                    fcntl.flock(f.fileno(), fcntl.LOCK_EX)  # Exclusive lock for writing
                    yaml.dump(data, f, default_flow_style=False, sort_keys=False)
                    f.flush()
                    fcntl.flock(f.fileno(), fcntl.LOCK_UN)  # Unlock

                # Atomic move
                temp_file.replace(service_file)
                print(f"✅ Saved {service} credentials to {service_file}")
                return

            except (IOError, OSError) as e:
                if attempt < 4:
                    time.sleep(0.1 * (attempt + 1))  # Exponential backoff
                    continue
                else:
                    print(
                        f"Error: Could not save credential after {attempt + 1} attempts: {e}"
                    )
                    # Fallback: write without lock (risky but better than losing data)
                    with open(service_file, "w") as f:
                        yaml.dump(data, f, default_flow_style=False, sort_keys=False)
                    print(f"⚠️ Saved {service} credentials without file lock (fallback)")
                    return

    def get_latest_credential(self, service: str) -> Optional[Dict[str, Any]]:
        """Get the most recent credential for a service."""
        data = self._load_service_data(service)
        service_data = data.get(service, [])
        if service_data:
            return service_data[-1]  # Return the last (most recent) entry
        return None

    def get_all_credentials(self, service: str) -> List[Dict[str, Any]]:
        """Get all credentials for a service."""
        data = self._load_service_data(service)
        return data.get(service, [])

    def list_services(self) -> List[str]:
        """List all services with stored credentials."""
        services = []
        for yaml_file in self.base_path.glob("*.yaml"):
            services.append(yaml_file.stem)  # filename without .yaml extension
        return services

    def get_service_summary(self) -> Dict[str, int]:
        """Get a summary of stored credentials per service."""
        summary = {}
        for service in self.list_services():
            credentials = self.get_all_credentials(service)
            summary[service] = len(credentials)
        return summary

    def _get_partial_file(self, service: str) -> Path:
        """Get the partial credentials YAML file path for a specific service."""
        return self.base_path / f"{service}_partial.yaml"

    def _load_partial_data(self, service: str) -> Dict[str, List[Dict[str, Any]]]:
        """Load existing partial data for a service with file locking."""
        partial_file = self._get_partial_file(service)
        if partial_file.exists():
            # Retry logic for file locking
            for attempt in range(5):
                try:
                    with open(partial_file, "r") as f:
                        fcntl.flock(
                            f.fileno(), fcntl.LOCK_SH
                        )  # Shared lock for reading
                        data = yaml.safe_load(f) or {f"{service}_partial": []}
                        fcntl.flock(f.fileno(), fcntl.LOCK_UN)  # Unlock
                        return data
                except (IOError, OSError) as e:
                    if attempt < 4:
                        time.sleep(0.1 * (attempt + 1))  # Exponential backoff
                        continue
                    else:
                        print(
                            f"Warning: Could not lock partial file {partial_file} for reading: {e}"
                        )
                        # Fallback: read without lock
                        with open(partial_file, "r") as f:
                            return yaml.safe_load(f) or {f"{service}_partial": []}
        return {f"{service}_partial": []}

    def add_partial_credential(self, credential: ServiceCredentials):
        """Add a partial credential (without API key) to the service partial file with atomic write and file locking."""
        service = credential.service
        partial_file = self._get_partial_file(service)
        partial_key = f"{service}_partial"

        # Retry logic for file locking
        for attempt in range(5):
            try:
                # Load existing data into memory
                data = self._load_partial_data(service)

                # Create simplified partial credential entry (no API key)
                new_entry = {
                    "email": credential.email,
                    "password": credential.password
                    if hasattr(credential, "password") and credential.password
                    else "",
                    "username": credential.username
                    if credential.username
                    else credential.email.split("@")[0],
                    "created_at": credential.created_at.isoformat(),
                    "status": "partial_pending_api_key",
                }

                # Add to the in-memory data structure
                if partial_key not in data:
                    data[partial_key] = []
                data[partial_key].append(new_entry)

                # Atomic write with exclusive lock
                temp_file = partial_file.with_suffix(".tmp")
                with open(temp_file, "w") as f:
                    fcntl.flock(f.fileno(), fcntl.LOCK_EX)  # Exclusive lock for writing
                    yaml.dump(data, f, default_flow_style=False, sort_keys=False)
                    f.flush()
                    fcntl.flock(f.fileno(), fcntl.LOCK_UN)  # Unlock

                # Atomic move
                temp_file.replace(partial_file)
                print(f"✅ Saved {service} partial credentials to {partial_file}")
                return

            except (IOError, OSError) as e:
                if attempt < 4:
                    time.sleep(0.1 * (attempt + 1))  # Exponential backoff
                    continue
                else:
                    print(
                        f"Error: Could not save partial credential after {attempt + 1} attempts: {e}"
                    )
                    # Fallback: write without lock (risky but better than losing data)
                    with open(partial_file, "w") as f:
                        yaml.dump(data, f, default_flow_style=False, sort_keys=False)
                    print(
                        f"⚠️ Saved {service} partial credentials without file lock (fallback)"
                    )
                    return

    def get_latest_partial_credential(self, service: str) -> Optional[Dict[str, Any]]:
        """Get the most recent partial credential for a service."""
        data = self._load_partial_data(service)
        partial_key = f"{service}_partial"
        service_data = data.get(partial_key, [])
        if service_data:
            return service_data[-1]  # Return the last (most recent) entry
        return None

    def remove_partial_credential(self, service: str, email: str):
        """Remove a specific partial credential by email from the partial file."""
        partial_file = self._get_partial_file(service)
        partial_key = f"{service}_partial"

        if not partial_file.exists():
            print(f"No partial credentials file exists for {service}")
            return

        # Retry logic for file locking
        for attempt in range(5):
            try:
                # Load existing data into memory
                data = self._load_partial_data(service)

                if partial_key not in data or not data[partial_key]:
                    print(f"No partial credentials found for {service}")
                    return

                # Filter out the matching email
                original_count = len(data[partial_key])
                data[partial_key] = [
                    cred for cred in data[partial_key] if cred.get("email") != email
                ]
                removed_count = original_count - len(data[partial_key])

                if removed_count == 0:
                    print(f"No partial credential found for email {email} in {service}")
                    return

                # If no partial credentials left, delete the file
                if not data[partial_key]:
                    partial_file.unlink(missing_ok=True)
                    print(
                        f"✅ Removed last partial credential for {email} and deleted {partial_file}"
                    )
                    return

                # Otherwise, write the updated data back
                # Atomic write with exclusive lock
                temp_file = partial_file.with_suffix(".tmp")
                with open(temp_file, "w") as f:
                    fcntl.flock(f.fileno(), fcntl.LOCK_EX)  # Exclusive lock for writing
                    yaml.dump(data, f, default_flow_style=False, sort_keys=False)
                    f.flush()
                    fcntl.flock(f.fileno(), fcntl.LOCK_UN)  # Unlock

                # Atomic move
                temp_file.replace(partial_file)
                print(f"✅ Removed partial credential for {email} from {partial_file}")
                return

            except (IOError, OSError) as e:
                if attempt < 4:
                    time.sleep(0.1 * (attempt + 1))  # Exponential backoff
                    continue
                else:
                    print(
                        f"Error: Could not remove partial credential after {attempt + 1} attempts: {e}"
                    )
                    return
