"""Enhanced result saving system for regbot - designed for flexibility and future serverless migration."""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass, field
import logging

logger = logging.getLogger(__name__)


class RegistrationState(Enum):
    """Workflow states that can trigger saving."""

    INITIATED = "initiated"
    EMAIL_SENT = "email_sent"
    EMAIL_VERIFIED = "email_verified"
    API_KEY_EXTRACTED = "api_key_extracted"
    WIZARD_STEP_COMPLETED = "wizard_step_completed"  # For Firecrawl multi-step
    WORKFLOW_COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class SaveStrategy:
    """Configuration for when and how a service should save results."""

    # Which states trigger saves
    save_on_states: List[RegistrationState] = field(default_factory=list)

    # Required fields for each state
    required_fields: Dict[RegistrationState, List[str]] = field(default_factory=dict)

    # Optional fields for each state
    optional_fields: Dict[RegistrationState, List[str]] = field(default_factory=dict)

    # How to handle existing data: "merge", "overwrite", "append"
    merge_strategy: str = "merge"

    # Enable partial saves for complex workflows
    enable_partial_saves: bool = False

    # Custom field transformations per state
    field_transformers: Dict[RegistrationState, Dict[str, callable]] = field(
        default_factory=dict
    )


class StorageBackend(ABC):
    """Abstract storage backend - supports YAML now, Cloudflare D1 later."""

    @abstractmethod
    async def save_credential(
        self,
        service: str,
        credential_data: Dict[str, Any],
        strategy: str = "merge",
        state: RegistrationState = None,
    ) -> bool:
        """Save credential data with specified merge strategy."""
        pass

    @abstractmethod
    async def get_credential(
        self, service: str, identifier: str = None
    ) -> Optional[Dict[str, Any]]:
        """Get specific credential by identifier (email, username, etc)."""
        pass

    @abstractmethod
    async def list_credentials(self, service: str) -> List[Dict[str, Any]]:
        """List all credentials for a service."""
        pass

    @abstractmethod
    async def delete_credential(self, service: str, identifier: str) -> bool:
        """Delete a specific credential."""
        pass


class YAMLStorageBackend(StorageBackend):
    """Current YAML-based storage implementation."""

    def __init__(self, data_path: str = "data"):
        self.data_path = data_path
        # Import existing ResultsStorage logic here

    async def save_credential(
        self,
        service: str,
        credential_data: Dict[str, Any],
        strategy: str = "merge",
        state: RegistrationState = None,
    ) -> bool:
        # Use existing YAML storage with enhanced merge logic
        pass


class CloudflareD1Backend(StorageBackend):
    """Future serverless storage backend."""

    def __init__(self, account_id: str, database_id: str, api_token: str):
        self.account_id = account_id
        self.database_id = database_id
        self.api_token = api_token

    async def save_credential(
        self,
        service: str,
        credential_data: Dict[str, Any],
        strategy: str = "merge",
        state: RegistrationState = None,
    ) -> bool:
        # D1 SQL operations
        pass


@dataclass
class RegistrationProgress:
    """Tracks progress through registration workflow."""

    service: str
    state: RegistrationState = RegistrationState.INITIATED
    profile: Dict[str, Any] = field(default_factory=dict)
    partial_results: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    state_timestamps: Dict[RegistrationState, datetime] = field(default_factory=dict)
    error_details: Optional[str] = None


class ResultManager:
    """Manages result saving for a specific service registration workflow."""

    def __init__(
        self,
        service_name: str,
        storage_backend: StorageBackend,
        save_strategy: SaveStrategy,
    ):
        self.service_name = service_name
        self.storage = storage_backend
        self.strategy = save_strategy
        self.progress = RegistrationProgress(service=service_name)

    async def update_progress(
        self, new_state: RegistrationState, data: Dict[str, Any]
    ) -> bool:
        """Update workflow progress and save if configured to do so."""

        # Update internal state
        old_state = self.progress.state
        self.progress.state = new_state
        self.progress.state_timestamps[new_state] = datetime.now()

        # Merge new data
        if "profile" in data:
            self.progress.profile.update(data["profile"])
        if "results" in data:
            self.progress.partial_results.update(data["results"])
        if "metadata" in data:
            self.progress.metadata.update(data["metadata"])

        logger.info(
            f"🔄 {self.service_name} state: {old_state.value} -> {new_state.value}"
        )

        # Check if this state requires saving
        if await self._should_save(new_state):
            return await self._save_current_state(new_state, data)

        return True

    async def _should_save(self, state: RegistrationState) -> bool:
        """Check if current state requires saving."""
        return state in self.strategy.save_on_states

    async def _save_current_state(
        self, state: RegistrationState, new_data: Dict[str, Any]
    ) -> bool:
        """Save current progress state."""

        try:
            # Prepare save data according to strategy
            save_data = await self._prepare_save_data(state, new_data)

            # Validate required fields
            if not await self._validate_required_fields(state, save_data):
                logger.warning(
                    f"⚠️ Missing required fields for {self.service_name} state {state.value}"
                )
                if not self.strategy.enable_partial_saves:
                    return False

            # Save to storage backend
            success = await self.storage.save_credential(
                service=self.service_name,
                credential_data=save_data,
                strategy=self.strategy.merge_strategy,
                state=state,
            )

            if success:
                logger.info(f"💾 {self.service_name} saved at state {state.value}")
            else:
                logger.error(
                    f"❌ {self.service_name} save failed at state {state.value}"
                )

            return success

        except Exception as e:
            logger.error(
                f"❌ Error saving {self.service_name} at state {state.value}: {e}"
            )
            return False

    async def _prepare_save_data(
        self, state: RegistrationState, new_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Prepare data for saving based on state and strategy."""

        save_data = {
            "service": self.service_name,
            "state": state.value,
            "created_at": datetime.now(),
            "state_timestamps": {
                k.value: v for k, v in self.progress.state_timestamps.items()
            },
        }

        # Add profile data
        save_data.update(self.progress.profile)

        # Add partial results
        save_data.update(self.progress.partial_results)

        # Add new data
        save_data.update(new_data)

        # Apply field transformers if any
        if state in self.strategy.field_transformers:
            for field, transformer in self.strategy.field_transformers[state].items():
                if field in save_data:
                    save_data[field] = transformer(save_data[field])

        # Filter to only allowed fields for this state
        allowed_fields = set()
        if state in self.strategy.required_fields:
            allowed_fields.update(self.strategy.required_fields[state])
        if state in self.strategy.optional_fields:
            allowed_fields.update(self.strategy.optional_fields[state])

        # Always include core fields
        allowed_fields.update(["service", "state", "created_at", "state_timestamps"])

        if allowed_fields:
            filtered_data = {k: v for k, v in save_data.items() if k in allowed_fields}
            return filtered_data

        return save_data

    async def _validate_required_fields(
        self, state: RegistrationState, data: Dict[str, Any]
    ) -> bool:
        """Validate that all required fields are present."""

        if state not in self.strategy.required_fields:
            return True

        required = set(self.strategy.required_fields[state])
        present = set(data.keys())
        missing = required - present

        if missing:
            logger.warning(f"⚠️ Missing required fields for {state.value}: {missing}")
            return False

        return True

    async def mark_failed(self, error: str) -> bool:
        """Mark the workflow as failed."""
        self.progress.error_details = error
        return await self.update_progress(RegistrationState.FAILED, {"error": error})

    async def get_current_progress(self) -> RegistrationProgress:
        """Get current workflow progress."""
        return self.progress


# Service-specific saving strategies
ASSEMBLYAI_SAVE_STRATEGY = SaveStrategy(
    save_on_states=[
        RegistrationState.API_KEY_EXTRACTED,  # Save when we get the API key
        RegistrationState.WORKFLOW_COMPLETED,  # Final comprehensive save
    ],
    required_fields={
        RegistrationState.API_KEY_EXTRACTED: ["api_key", "email"],
        RegistrationState.WORKFLOW_COMPLETED: ["api_key", "email", "username"],
    },
    optional_fields={
        RegistrationState.API_KEY_EXTRACTED: ["username"],
        RegistrationState.WORKFLOW_COMPLETED: ["password", "profile_data"],
    },
    merge_strategy="merge",
    enable_partial_saves=True,
)

FIRECRAWL_SAVE_STRATEGY = SaveStrategy(
    save_on_states=[
        RegistrationState.EMAIL_VERIFIED,  # Save after email verification
        RegistrationState.WIZARD_STEP_COMPLETED,  # Save after each wizard step
        RegistrationState.API_KEY_EXTRACTED,  # Save when API key found
        RegistrationState.WORKFLOW_COMPLETED,  # Final save
    ],
    required_fields={
        RegistrationState.EMAIL_VERIFIED: ["email"],
        RegistrationState.WIZARD_STEP_COMPLETED: ["email", "wizard_progress"],
        RegistrationState.API_KEY_EXTRACTED: ["api_key", "email"],
        RegistrationState.WORKFLOW_COMPLETED: [
            "api_key",
            "email",
            "username",
            "password",
        ],
    },
    optional_fields={
        RegistrationState.EMAIL_VERIFIED: ["verification_token"],
        RegistrationState.WIZARD_STEP_COMPLETED: ["step_data"],
        RegistrationState.API_KEY_EXTRACTED: ["username", "password"],
        RegistrationState.WORKFLOW_COMPLETED: ["profile_data", "wizard_data"],
    },
    merge_strategy="merge",
    enable_partial_saves=True,  # Critical for multi-step wizard
)

EXAAI_SAVE_STRATEGY = SaveStrategy(
    save_on_states=[
        RegistrationState.WORKFLOW_COMPLETED  # ExaAI is simpler, save only at end
    ],
    required_fields={RegistrationState.WORKFLOW_COMPLETED: ["api_key", "email"]},
    optional_fields={
        RegistrationState.WORKFLOW_COMPLETED: ["username", "search_successful"]
    },
    merge_strategy="overwrite",  # Replace entirely for simple workflows
    enable_partial_saves=False,
)


class ResultManagerFactory:
    """Factory for creating service-specific result managers."""

    STRATEGIES = {
        "assemblyai": ASSEMBLYAI_SAVE_STRATEGY,
        "firecrawl": FIRECRAWL_SAVE_STRATEGY,
        "exaai": EXAAI_SAVE_STRATEGY,
    }

    @classmethod
    def create_manager(
        cls, service_name: str, storage_backend: StorageBackend
    ) -> ResultManager:
        """Create a result manager for the specified service."""

        strategy = cls.STRATEGIES.get(service_name)
        if not strategy:
            # Default strategy for unknown services
            strategy = SaveStrategy(
                save_on_states=[RegistrationState.WORKFLOW_COMPLETED],
                required_fields={
                    RegistrationState.WORKFLOW_COMPLETED: ["api_key", "email"]
                },
                merge_strategy="merge",
                enable_partial_saves=False,
            )
            logger.warning(
                f"⚠️ Using default save strategy for unknown service: {service_name}"
            )

        return ResultManager(service_name, storage_backend, strategy)


# Usage example in services:
"""
class AssemblyAIService:
    def __init__(self, config, full_config):
        # ... existing init ...
        storage_backend = YAMLStorageBackend(full_config.data_path)
        self.result_manager = ResultManagerFactory.create_manager("assemblyai", storage_backend)
        
    async def register_and_get_api_key(self):
        # Start workflow
        await self.result_manager.update_progress(RegistrationState.INITIATED, {
            "profile": {"email": self.profile["email"], "username": self.profile["username"]}
        })
        
        # ... email submission ...
        await self.result_manager.update_progress(RegistrationState.EMAIL_SENT, {
            "metadata": {"email_submitted_at": datetime.now()}
        })
        
        # ... magic link processing ...
        await self.result_manager.update_progress(RegistrationState.EMAIL_VERIFIED, {
            "metadata": {"magic_link_processed_at": datetime.now()}
        })
        
        # ... API key extraction ... 
        await self.result_manager.update_progress(RegistrationState.API_KEY_EXTRACTED, {
            "results": {"api_key": api_key}
        })
        
        # ... final completion ...
        await self.result_manager.update_progress(RegistrationState.WORKFLOW_COMPLETED, {
            "metadata": {"completed_at": datetime.now()}
        })
"""
