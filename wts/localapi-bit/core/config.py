"""Configuration management for regbot automation."""

from typing import Dict, Optional, List, Any, Union
from pathlib import Path
from pydantic import BaseModel, Field
import yaml
from .randomizer import RandomizerConfig


class BitBrowserConfig(BaseModel):
    """BitBrowser-specific configuration."""

    browser_id: str = "bit"
    credentials: Dict[str, Any] = Field(default_factory=dict)
    api_url: str = "http://127.0.0.1:54345"


class EmailDomainConfig(BaseModel):
    """Email domain configuration."""

    domain: str
    weight: int = 100
    enabled: bool = True


class WebhookConfig(BaseModel):
    """Webhook configuration for local and external URLs."""

    local_baseurl: str = "http://localhost:8888"
    external_baseurl: Optional[str] = None


class EmailInterceptorEnvironment(BaseModel):
    """Environment-specific configuration for email interceptor."""

    webhook_url: str
    forward_email: str = ""
    store_raw_email: bool = True


class EmailInterceptorConfig(BaseModel):
    """Email interceptor worker configuration."""

    worker_name: str = "regbot-emailparser"
    store_raw_email: bool = True
    forward_unknown: bool = True
    forward_email: str = ""
    environments: Dict[str, EmailInterceptorEnvironment] = Field(default_factory=dict)


class ContinuousAutomationConfig(BaseModel):
    """Configuration for continuous automation."""

    # Session interval settings (in minutes)
    min_interval_minutes: int = 30
    max_interval_minutes: int = 90

    # Randomization settings
    page_delay_min_seconds: int = 2
    page_delay_max_seconds: int = 8
    typing_delay_min_ms: int = 50
    typing_delay_max_ms: int = 200

    # Goal settings
    target_registrations: int = 10
    max_failed_attempts: int = 3

    # Behavior randomization
    enable_mouse_movements: bool = True
    enable_scroll_simulation: bool = True
    enable_user_agent_rotation: bool = True
    enable_viewport_randomization: bool = True

    # Session management
    session_persistence_enabled: bool = True
    max_session_duration_hours: int = 12
    enable_graceful_shutdown: bool = True


class ServiceConfig(BaseModel):
    """Configuration for a single service."""

    name: str  # Display name for the service
    start_url: str  # URL to start the registration process (e.g., signup page)
    email_domain_groups: List[str] = Field(
        default_factory=list
    )  # References to domain groups for email generation
    sender_domains: List[str] = Field(
        default_factory=list
    )  # Domains that send emails for this service (for email routing)
    email_patterns: List[str] = Field(
        default_factory=list
    )  # Email patterns (illustrative - actual parsing in service code)
    wait_times: Dict[str, int] = Field(
        default_factory=dict
    )  # Wait times in milliseconds (illustrative - actual waits in service code)
    enabled: bool = True  # Whether this service is enabled for registration
    headless: Optional[bool] = (
        None  # Headless mode: None=default(True), True=headless, False=visible browser
    )
    browser_engines: Optional[List[Union[str, Dict[str, Any]]]] = (
        None  # Browser engines: strings ("chromium") or configs ({"bit": {...}})
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict
    )  # Additional service-specific metadata


class Config(BaseModel):
    """Main configuration."""

    # Email domain groups - reusable collections of domains across services
    email_domain_groups: Dict[str, List[EmailDomainConfig]] = Field(
        default_factory=dict
    )

    # Email retrieval configuration - how to get emails (webhook or KV)
    email_retrieval: Optional[Dict[str, Any]] = (
        None  # Contains client_type and webhook/kv config
    )

    # Webhook configuration (legacy support)
    webhook: WebhookConfig = Field(default_factory=WebhookConfig)

    # Email interceptor worker configuration - Cloudflare Worker settings
    email_interceptor: EmailInterceptorConfig = Field(
        default_factory=EmailInterceptorConfig
    )

    # Continuous automation configuration - settings for repeated runs
    continuous_automation: ContinuousAutomationConfig = Field(
        default_factory=ContinuousAutomationConfig
    )

    # Randomization configuration - human-like behavior settings
    randomizer: RandomizerConfig = Field(default_factory=RandomizerConfig)

    # Cloudflare KV settings (required only if using KV client_type)
    cloudflare_worker_url: Optional[str] = None  # Worker URL for email fetching
    cloudflare_account_id: Optional[str] = None  # Cloudflare account ID
    cloudflare_namespace_id: Optional[str] = None  # KV namespace ID for email storage
    cloudflare_api_token: Optional[str] = None  # API token with KV read permissions

    # Browser settings (global defaults, can be overridden per service)
    browser_timeout: int = 30000  # Default timeout in milliseconds (30 seconds)
    browser_engines: List[Union[str, Dict[str, Any]]] = Field(
        default_factory=lambda: ["chromium", "firefox"]
    )  # Default engines: simple strings or complex configs

    # Storage paths - base directory for all data
    data_path: str = (
        "data"  # Base path for all data storage (results, screenshots, debug files)
    )

    # Service configurations - individual service settings
    services: Dict[str, ServiceConfig] = Field(default_factory=dict)

    def get_domains_for_service(self, service_name: str) -> List[EmailDomainConfig]:
        """Get all email domains for a service based on its groups."""
        service = self.services.get(service_name)
        if not service:
            return []

        domains = []
        for group_name in service.email_domain_groups:
            if group_name in self.email_domain_groups:
                domains.extend(self.email_domain_groups[group_name])

        return domains

    def parse_browser_engines(
        self, engines_config: List[Union[str, Dict[str, Any]]]
    ) -> List[Dict[str, Any]]:
        """Parse browser engines configuration to normalize format."""
        parsed_engines = []
        for engine in engines_config:
            if isinstance(engine, str):
                parsed_engines.append({"name": engine, "type": "simple"})
            elif isinstance(engine, dict):
                # Handle nested engine configurations like bit: {...}
                if len(engine) == 1 and isinstance(list(engine.values())[0], dict):
                    engine_name = list(engine.keys())[0]
                    engine_config = list(engine.values())[0]
                    parsed_engines.append(
                        {
                            "name": engine_name,
                            "type": "configured",
                            "options": engine_config,
                        }
                    )
                else:
                    # Handle direct dict format
                    parsed_engines.append(
                        {
                            "name": engine.get("name", "unknown"),
                            "type": "configured",
                            "options": engine,
                        }
                    )
        return parsed_engines

    @property
    def webhook_url(self) -> str:
        """Get the local webhook URL for automation scripts."""
        # Handle new email_retrieval.webhook structure or fallback to old webhook structure
        if (
            hasattr(self, "email_retrieval")
            and self.email_retrieval
            and hasattr(self.email_retrieval, "webhook")
        ):
            return self.email_retrieval.webhook.local_baseurl
        elif hasattr(self, "webhook") and self.webhook:
            return self.webhook.local_baseurl
        else:
            return "http://localhost:8888"  # Default fallback

    @classmethod
    def load(cls, config_path: Optional[Path] = None) -> "Config":
        """Load configuration from file or use defaults."""
        if config_path:
            # Convert string to Path if needed
            if isinstance(config_path, str):
                config_path = Path(config_path)

            if config_path.exists():
                with open(config_path, "r") as f:
                    data = yaml.safe_load(f)

                    # Convert email domain groups to proper objects
                    if "email_domain_groups" in data:
                        for group_name, domains in data["email_domain_groups"].items():
                            data["email_domain_groups"][group_name] = [
                                EmailDomainConfig(**d) if isinstance(d, dict) else d
                                for d in domains
                            ]

                    # Convert webhook config
                    if "webhook" in data:
                        data["webhook"] = WebhookConfig(**data["webhook"])

                    # Convert email interceptor config
                    if "email_interceptor" in data:
                        interceptor_data = data["email_interceptor"]
                        if "environments" in interceptor_data:
                            envs = {}
                            for env_name, env_data in interceptor_data[
                                "environments"
                            ].items():
                                envs[env_name] = EmailInterceptorEnvironment(**env_data)
                            interceptor_data["environments"] = envs
                        data["email_interceptor"] = EmailInterceptorConfig(
                            **interceptor_data
                        )

                    # Convert continuous automation config
                    if "continuous_automation" in data:
                        data["continuous_automation"] = ContinuousAutomationConfig(
                            **data["continuous_automation"]
                        )

                    # Convert services to proper objects
                    if "services" in data:
                        for service_name, service_data in data["services"].items():
                            data["services"][service_name] = ServiceConfig(
                                **service_data
                            )

                    return cls(**data)

        # Default configuration
        return cls(
            email_domain_groups={
                "primary": [
                    EmailDomainConfig(
                        domain="ai.whatisinitfor.me", weight=50, enabled=True
                    )
                ],
                "secondary": [
                    EmailDomainConfig(
                        domain="bot.example.com", weight=30, enabled=False
                    ),
                    EmailDomainConfig(
                        domain="reg.example.com", weight=20, enabled=False
                    ),
                ],
            },
            webhook=WebhookConfig(local_baseurl="http://localhost:8888"),
            services={
                "assemblyai": ServiceConfig(
                    name="AssemblyAI",
                    start_url="https://www.assemblyai.com/dashboard/login",
                    email_domain_groups=["primary"],
                    email_patterns=[
                        "<EMAIL>",
                        "<EMAIL>",
                        "assemblyai.com",
                    ],
                    wait_times={
                        "page_load": 3000,
                        "after_login": 5000,
                        "verification_check": 10000,
                    },
                )
            },
        )

    def save(self, config_path: Path):
        """Save configuration to file."""
        config_path.parent.mkdir(parents=True, exist_ok=True)
        with open(config_path, "w") as f:
            yaml.dump(self.model_dump(), f, default_flow_style=False)
