#!/usr/bin/env python3
"""
Dedicated Firefox clipboard test script.
Tests various clipboard access methods in Firefox to debug the API key extraction issue.
"""

import asyncio
import logging
from pathlib import Path
from core.browser import BrowserManager

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class FirefoxClipboardTester:
    """Test Firefox clipboard functionality in isolation."""

    def __init__(self):
        self.debug_dir = Path("debug") / "firefox_clipboard_test"
        self.debug_dir.mkdir(parents=True, exist_ok=True)

    async def run_clipboard_tests(self):
        """Run comprehensive clipboard tests in Firefox."""
        logger.info("🔥 Starting Firefox clipboard tests...")

        async with BrowserManager(
            headless=False,  # Use visible mode for better debugging
            browser_engines=["firefox"],
            enable_randomization=False,
            permissions=[],  # Firefox doesn't support clipboard-read permission
        ) as browser:
            logger.info(f"🌐 Using browser engine: {browser.browser_engine}")

            # Test 1: Navigate to ExaAI dashboard with known working credentials
            logger.info("📍 Test 1: Navigate to ExaAI and check basic functionality")
            await self.test_navigation_and_basic_access(browser)

            # Test 2: Test clipboard on a simple test page
            logger.info("📍 Test 2: Test clipboard on simple test page")
            await self.test_simple_clipboard_page(browser)

            # Test 3: Test clipboard with ExaAI API keys page
            logger.info("📍 Test 3: Navigate to actual ExaAI API keys page")
            await self.test_exaai_clipboard(browser)

            logger.info("✅ Firefox clipboard tests completed!")

    async def test_navigation_and_basic_access(self, browser: BrowserManager):
        """Test basic Firefox navigation and JavaScript execution."""
        try:
            # Navigate to ExaAI login
            await browser.goto("https://dashboard.exa.ai/login")
            await browser.wait(3000)

            # Take screenshot
            await browser.screenshot(str(self.debug_dir / "01_login_page.png"))

            # Test basic JavaScript execution
            test_result = await browser.page.evaluate(
                "() => { return navigator.userAgent; }"
            )
            logger.info(f"🔍 User Agent: {test_result}")

            # Test clipboard permission check
            clipboard_permission = await browser.page.evaluate("""
                async () => {
                    try {
                        const permission = await navigator.permissions.query({name: 'clipboard-read'});
                        return permission.state;
                    } catch (e) {
                        return 'error: ' + e.message;
                    }
                }
            """)
            logger.info(f"🔍 Clipboard permission state: {clipboard_permission}")

            return True
        except Exception as e:
            logger.error(f"❌ Navigation test failed: {e}")
            return False

    async def test_simple_clipboard_page(self, browser: BrowserManager):
        """Test clipboard on a simple controlled page."""
        try:
            # Create a simple test page with clipboard functionality
            test_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Clipboard Test</title>
            </head>
            <body>
                <h1>Firefox Clipboard Test</h1>
                <div>
                    <p>Test API Key: <span id="api-key">test-api-key-123456789-abcdef-very-long-key-for-testing</span></p>
                    <button id="copy-btn" onclick="copyToClipboard()">Copy API Key</button>
                </div>
                <div>
                    <textarea id="paste-area" placeholder="Paste here to test..."></textarea>
                </div>
                
                <script>
                    // Global clipboard content storage
                    window.lastCopiedContent = '';
                    window.lastAlertMessage = '';
                    
                    // Override alert to capture messages
                    const originalAlert = window.alert;
                    window.alert = function(message) {
                        window.lastAlertMessage = message;
                        console.log('Alert captured:', message);
                        return originalAlert(message);
                    };
                    
                    async function copyToClipboard() {
                        const apiKey = document.getElementById('api-key').textContent;
                        console.log('Attempting to copy:', apiKey);
                        
                        try {
                            // Method 1: Try modern clipboard API
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                await navigator.clipboard.writeText(apiKey);
                                console.log('Copied using clipboard API');
                                window.lastCopiedContent = apiKey;  // Store for retrieval
                                alert('Copied using clipboard API!');
                                return;
                            }
                        } catch (e) {
                            console.log('Clipboard API failed:', e);
                        }
                        
                        try {
                            // Method 2: Try execCommand
                            const textArea = document.createElement('textarea');
                            textArea.value = apiKey;
                            textArea.style.position = 'fixed';
                            textArea.style.left = '-9999px';
                            document.body.appendChild(textArea);
                            textArea.focus();
                            textArea.select();
                            textArea.setSelectionRange(0, 99999);
                            
                            const success = document.execCommand('copy');
                            document.body.removeChild(textArea);
                            
                            if (success) {
                                console.log('Copied using execCommand');
                                window.lastCopiedContent = apiKey;  // Store for retrieval
                                alert('Copied using execCommand!');
                            } else {
                                console.log('execCommand failed');
                                alert('Copy failed!');
                            }
                        } catch (e) {
                            console.log('execCommand failed:', e);
                            alert('Copy failed: ' + e.message);
                        }
                    }
                    
                    // Listen for copy events
                    document.addEventListener('copy', function(e) {
                        console.log('Copy event detected');
                        const selection = window.getSelection().toString();
                        if (selection) {
                            window.lastCopiedContent = selection;
                            console.log('Stored copied content:', selection);
                        }
                        if (e.clipboardData) {
                            const clipboardData = e.clipboardData.getData('text/plain');
                            if (clipboardData) {
                                window.lastCopiedContent = clipboardData;
                                console.log('Stored clipboard data:', clipboardData);
                            }
                        }
                    });
                </script>
            </body>
            </html>
            """

            # Create a data URL with the test page
            data_url = f"data:text/html;charset=utf-8,{test_html}"
            await browser.goto(data_url)
            await browser.wait(2000)

            # Take screenshot
            await browser.screenshot(str(self.debug_dir / "02_test_page.png"))

            # Click the copy button
            copy_button = await browser.page.query_selector("#copy-btn")
            if copy_button:
                logger.info("🔍 About to click copy button...")
                await copy_button.click()
                await browser.wait(1000)

                # Also try calling the function directly
                logger.info("🔍 Calling copyToClipboard() directly...")
                await browser.page.evaluate("copyToClipboard()")
                await browser.wait(2000)

                # Check for any dialogs or alerts
                try:
                    # Check if an alert appeared
                    dialog_result = await browser.page.evaluate(
                        "() => { return window.lastAlertMessage || 'no alert'; }"
                    )
                    logger.info(f"🔍 Dialog result: {dialog_result}")

                    # Also check browser console for any JavaScript errors
                    console_logs = await browser.page.evaluate(
                        "() => { return console.log.toString(); }"
                    )
                    logger.info(f"🔍 Console check complete")
                except Exception as e:
                    logger.info(f"Dialog check failed: {e}")

                # Test all Firefox clipboard methods
                logger.info("🔍 Testing Firefox clipboard methods...")

                # Method 1: Try standard clipboard API
                try:
                    clipboard_content = await browser.page.evaluate(
                        "() => navigator.clipboard.readText()"
                    )
                    if clipboard_content:
                        logger.info(f"✅ Standard clipboard API: {clipboard_content}")
                    else:
                        logger.info("❌ Standard clipboard API: No content")
                except Exception as e:
                    logger.info(f"❌ Standard clipboard API failed: {e}")

                # Method 2: Check global storage
                try:
                    stored_content = await browser.page.evaluate(
                        "() => window.lastCopiedContent"
                    )
                    if stored_content:
                        logger.info(f"✅ Global storage method: {stored_content}")
                    else:
                        logger.info("❌ Global storage method: No content")
                except Exception as e:
                    logger.info(f"❌ Global storage method failed: {e}")

                # Method 3: Try our Firefox-specific methods
                firefox_content = await self.test_firefox_specific_methods(browser.page)
                if firefox_content:
                    logger.info(f"✅ Firefox specific methods: {firefox_content}")
                else:
                    logger.info("❌ Firefox specific methods: No content")

                # Method 4: Debug what actually happened
                await self.debug_clipboard_state(browser.page)

                return True
            else:
                logger.error("❌ Could not find copy button")
                return False

        except Exception as e:
            logger.error(f"❌ Simple clipboard test failed: {e}")
            return False

    async def debug_clipboard_state(self, page):
        """Debug what's happening with clipboard in Firefox."""
        try:
            debug_info = await page.evaluate("""
                () => {
                    const info = {};
                    
                    // Check navigator.clipboard availability
                    info.clipboardAPI = !!navigator.clipboard;
                    info.clipboardReadText = !!(navigator.clipboard && navigator.clipboard.readText);
                    info.clipboardWriteText = !!(navigator.clipboard && navigator.clipboard.writeText);
                    
                    // Check document.execCommand support
                    info.execCommandCopy = document.queryCommandSupported('copy');
                    info.execCommandPaste = document.queryCommandSupported('paste');
                    
                    // Check current selection
                    const selection = window.getSelection();
                    info.hasSelection = !!(selection && selection.toString());
                    info.selectionText = selection ? selection.toString() : '';
                    
                    // Check focused element
                    const activeElement = document.activeElement;
                    info.activeElementTag = activeElement ? activeElement.tagName : 'none';
                    info.activeElementValue = activeElement && activeElement.value ? activeElement.value : '';
                    
                    // Check for any clipboard-related globals
                    info.globalClipboard = window.lastCopiedContent || '';
                    
                    // Check console logs
                    info.timestamp = new Date().toISOString();
                    
                    return info;
                }
            """)

            logger.info("🔍 Firefox clipboard debug info:")
            for key, value in debug_info.items():
                logger.info(f"  {key}: {value}")

        except Exception as e:
            logger.error(f"Debug clipboard state failed: {e}")

    async def test_firefox_specific_methods(self, page):
        """Test our Firefox-specific clipboard methods."""
        try:
            result = await page.evaluate("""
                async () => {
                    const methods = [];
                    
                    // Method 1: Check selection
                    try {
                        const selection = window.getSelection();
                        if (selection && selection.toString()) {
                            methods.push('Selection: ' + selection.toString());
                        }
                    } catch (e) {
                        methods.push('Selection failed: ' + e.message);
                    }
                    
                    // Method 2: Check focused element
                    try {
                        const activeElement = document.activeElement;
                        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
                            if (activeElement.value) {
                                methods.push('Active element: ' + activeElement.value);
                            }
                        }
                    } catch (e) {
                        methods.push('Active element failed: ' + e.message);
                    }
                    
                    // Method 3: Check for long input values
                    try {
                        const inputs = document.querySelectorAll('input[type="text"], input[readonly]');
                        for (const input of inputs) {
                            if (input.value && input.value.length > 30) {
                                methods.push('Long input: ' + input.value);
                                break;
                            }
                        }
                    } catch (e) {
                        methods.push('Long input search failed: ' + e.message);
                    }
                    
                    // Method 4: Check global storage
                    try {
                        if (window.lastCopiedContent) {
                            methods.push('Global storage: ' + window.lastCopiedContent);
                        }
                    } catch (e) {
                        methods.push('Global storage failed: ' + e.message);
                    }
                    
                    return methods;
                }
            """)

            for method_result in result:
                logger.info(f"  🔍 {method_result}")

            # Return the first successful result
            for method_result in result:
                if not method_result.startswith(
                    (
                        "Selection failed",
                        "Active element failed",
                        "Long input search failed",
                        "Global storage failed",
                    )
                ):
                    return (
                        method_result.split(": ", 1)[1]
                        if ": " in method_result
                        else method_result
                    )

            return None

        except Exception as e:
            logger.error(f"Firefox specific methods error: {e}")
            return None

    async def test_exaai_clipboard(self, browser: BrowserManager):
        """Test clipboard on actual ExaAI API keys page."""
        try:
            # Navigate directly to a known working API keys page
            # This assumes we have a valid session - in practice we'd need to login first
            logger.info("🔍 Testing ExaAI clipboard functionality...")

            # For now, just test navigation to the API keys page
            await browser.goto("https://dashboard.exa.ai/api-keys")
            await browser.wait(5000)

            # Take screenshot
            await browser.screenshot(str(self.debug_dir / "03_api_keys_page.png"))

            # Check if we're on the login page (not authenticated)
            current_url = browser.page.url
            if "login" in current_url:
                logger.info(
                    "⚠️ Not authenticated - would need full login flow for real test"
                )
                return False

            # Look for API key table elements
            api_key_elements = await browser.page.query_selector_all(
                'tr:has-text("default")'
            )
            if api_key_elements:
                logger.info(f"✅ Found {len(api_key_elements)} potential API key rows")

                # Try to click on the first one and test clipboard
                for i, element in enumerate(
                    api_key_elements[:1]
                ):  # Test first one only
                    try:
                        await element.click()
                        await browser.wait(2000)

                        # Test clipboard methods
                        clipboard_content = await self.test_firefox_specific_methods(
                            browser.page
                        )
                        if clipboard_content:
                            logger.info(
                                f"✅ ExaAI clipboard test {i + 1}: {clipboard_content[:50]}..."
                            )
                        else:
                            logger.info(f"❌ ExaAI clipboard test {i + 1}: No content")

                    except Exception as e:
                        logger.error(f"❌ ExaAI clipboard test {i + 1} failed: {e}")

                return True
            else:
                logger.info("❌ No API key elements found")
                return False

        except Exception as e:
            logger.error(f"❌ ExaAI clipboard test failed: {e}")
            return False


async def main():
    """Run the Firefox clipboard tests."""
    tester = FirefoxClipboardTester()
    await tester.run_clipboard_tests()


if __name__ == "__main__":
    asyncio.run(main())
