#!/usr/bin/env python3
"""Test browser randomization capabilities."""

import asyncio
import sys
from core.browser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


async def test_browser_randomization():
    """Test different browser engines and randomization."""

    engines = ["chromium", "firefox", "webkit"]

    for engine in engines:
        print(f"\n🧪 Testing {engine.upper()} browser engine...")
        try:
            async with BrowserManager(
                headless=True,
                enable_randomization=True,
                browser_engines=[engine],  # Force specific engine
            ) as browser:
                await browser.goto("https://httpbin.org/headers")
                await asyncio.sleep(2)

                # Get user agent
                user_agent = await browser.page.evaluate("navigator.userAgent")
                viewport = await browser.page.evaluate(
                    "({width: window.innerWidth, height: window.innerHeight})"
                )

                print(f"   ✅ {engine} launched successfully")
                print(f"   📱 User Agent: {user_agent}")
                print(f"   📺 Viewport: {viewport['width']}x{viewport['height']}")
                print(f"   🎯 Selected engine: {browser.browser_engine}")

        except Exception as e:
            print(f"   ❌ {engine} failed: {e}")

    print(f"\n🎲 Testing random engine selection from list...")
    try:
        async with BrowserManager(
            headless=True,
            enable_randomization=True,
            browser_engines=["chromium", "firefox", "webkit"],  # All engines
        ) as browser:
            await browser.goto("https://httpbin.org/headers")
            user_agent = await browser.page.evaluate("navigator.userAgent")
            print(f"   ✅ Random engine selected: {browser.browser_engine}")
            print(f"   📱 Random User Agent: {user_agent}")
    except Exception as e:
        print(f"   ❌ Random selection failed: {e}")

    print(f"\n🎯 Testing engine list fallback (chromium + firefox only)...")
    try:
        async with BrowserManager(
            headless=True,
            enable_randomization=True,
            browser_engines=["chromium", "firefox"],  # Production default
        ) as browser:
            await browser.goto("https://httpbin.org/headers")
            user_agent = await browser.page.evaluate("navigator.userAgent")
            print(f"   ✅ Fallback engine selected: {browser.browser_engine}")
            print(f"   📱 Fallback User Agent: {user_agent}")
    except Exception as e:
        print(f"   ❌ Fallback selection failed: {e}")


if __name__ == "__main__":
    asyncio.run(test_browser_randomization())
