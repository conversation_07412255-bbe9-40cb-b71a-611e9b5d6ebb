version: 2.1

orbs:
  python: circleci/python@2.1.1
  codecov: codecov/codecov@3.2.4

executors:
  python-executor:
    docker:
      - image: cimg/python:3.11
    working_directory: ~/project

jobs:
  setup:
    executor: python-executor
    steps:
      - checkout
      - run:
          name: Install uv
          command: |
            curl -LsSf https://astral.sh/uv/install.sh | sh
            source $HOME/.cargo/env
      - run:
          name: Install dependencies
          command: |
            source $HOME/.cargo/env
            uv sync --dev
      - persist_to_workspace:
          root: ~/project
          paths:
            - .venv
            - uv.lock

  unit-tests:
    executor: python-executor
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - run:
          name: Install uv
          command: |
            curl -LsSf https://astral.sh/uv/install.sh | sh
            source $HOME/.cargo/env
      - run:
          name: Run unit tests
          command: |
            source $HOME/.cargo/env
            uv run ci-test-unit
      - codecov/upload:
          file: coverage.xml
          flags: unit
      - store_test_results:
          path: test-results
      - store_artifacts:
          path: test-results

  integration-tests:
    executor: python-executor
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - run:
          name: Install uv
          command: |
            curl -LsSf https://astral.sh/uv/install.sh | sh
            source $HOME/.cargo/env
      - run:
          name: Run integration tests
          command: |
            source $HOME/.cargo/env
            uv run ci-test-integration
      - codecov/upload:
          file: coverage.xml
          flags: integration
      - store_test_results:
          path: test-results
      - store_artifacts:
          path: test-results

  browser-tests:
    executor: python-executor
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - run:
          name: Install uv
          command: |
            curl -LsSf https://astral.sh/uv/install.sh | sh
            source $HOME/.cargo/env
      - run:
          name: Install Playwright browsers
          command: |
            source $HOME/.cargo/env
            uv run playwright install chromium
      - run:
          name: Run browser tests
          command: |
            source $HOME/.cargo/env
            uv run ci-test-browser
          environment:
            BROWSER_HEADLESS: true
      - store_test_results:
          path: test-results
      - store_artifacts:
          path: test-results

  lint:
    executor: python-executor
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - run:
          name: Install uv
          command: |
            curl -LsSf https://astral.sh/uv/install.sh | sh
            source $HOME/.cargo/env
      - run:
          name: Run quality checks
          command: |
            source $HOME/.cargo/env
            uv run quality
      - store_test_results:
          path: lint-results.xml
      - store_artifacts:
          path: lint-results.xml

  security:
    executor: python-executor
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - run:
          name: Install uv
          command: |
            curl -LsSf https://astral.sh/uv/install.sh | sh
            source $HOME/.cargo/env
      - run:
          name: Security scan
          command: |
            source $HOME/.cargo/env
            uv run bandit -r core/ services/ -f json -o bandit-report.json || true
      - store_artifacts:
          path: bandit-report.json

  e2e-tests:
    executor: python-executor
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - run:
          name: Install uv
          command: |
            curl -LsSf https://astral.sh/uv/install.sh | sh
            source $HOME/.cargo/env
      - run:
          name: Install Playwright browsers
          command: |
            source $HOME/.cargo/env
            uv run playwright install chromium
      - run:
          name: Run E2E tests
          command: |
            source $HOME/.cargo/env
            uv run ci-test-e2e
          environment:
            BROWSER_HEADLESS: true
      - store_test_results:
          path: test-results
      - store_artifacts:
          path: test-results

  build:
    executor: python-executor
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - run:
          name: Install uv
          command: |
            curl -LsSf https://astral.sh/uv/install.sh | sh
            source $HOME/.cargo/env
      - run:
          name: Build package
          command: |
            source $HOME/.cargo/env
            uv run build
      - store_artifacts:
          path: dist/
      - persist_to_workspace:
          root: ~/project
          paths:
            - dist

  performance:
    executor: python-executor
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - run:
          name: Install uv
          command: |
            curl -LsSf https://astral.sh/uv/install.sh | sh
            source $HOME/.cargo/env
      - run:
          name: Run performance tests
          command: |
            source $HOME/.cargo/env
            uv run pytest tests/ -m "not slow" --benchmark-only --benchmark-json=benchmark.json || true
      - store_artifacts:
          path: benchmark.json

workflows:
  version: 2
  
  test-and-build:
    jobs:
      - setup
      
      # Parallel test execution
      - unit-tests:
          requires:
            - setup
      - integration-tests:
          requires:
            - setup
      - browser-tests:
          requires:
            - setup
      
      # Code quality checks
      - lint:
          requires:
            - setup
      - security:
          requires:
            - setup
      
      # Build after tests pass
      - build:
          requires:
            - unit-tests
            - integration-tests
            - lint
      
      # E2E tests only on main/develop
      - e2e-tests:
          requires:
            - build
          filters:
            branches:
              only:
                - main
                - develop
      
      # Performance tests only on main
      - performance:
          requires:
            - build
          filters:
            branches:
              only:
                - main

  # Nightly comprehensive test
  nightly:
    triggers:
      - schedule:
          cron: "0 2 * * *"  # 2 AM UTC
          filters:
            branches:
              only:
                - main
    jobs:
      - setup
      - unit-tests:
          requires:
            - setup
      - integration-tests:
          requires:
            - setup
      - browser-tests:
          requires:
            - setup
      - e2e-tests:
          requires:
            - setup
      - performance:
          requires:
            - setup