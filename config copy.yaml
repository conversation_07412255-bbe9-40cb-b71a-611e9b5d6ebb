email_domain_groups:
  primary:
    - domain: ai.whatisinitfor.me
      weight: 30
      enabled: true
    - domain: sg.164136.xyz
      weight: 30
      enabled: true
    - domain: iad.164136.xyz
      weight: 20
      enabled: true

browser_headless: false
browser_engines: 
  - chromium

email_retrieval:
  client_type: "webhook"
  
  webhook:
    local_baseurl: "http://localhost:8888"  # Used by automation scripts
    external_baseurl: "https://as6l7hmgokqt.share.zrok.io"  # External tunnel URL for Cloudflare worker

# Email interceptor worker configuration
email_interceptor:
  worker_name: "regbot-emailparser"
  store_raw_email: true
  forward_unknown: true  # Forward emails from unknown services
  forward_email: "<EMAIL>"  # Backup email address

services:
  exaai:
    name: ExaAI
    start_url: https://dashboard.exa.ai/login
    email_domain_groups: 
      - primary
    sender_domains: ["exa.ai"]
    enabled: true
    headless: false
    browser_engines:
      - chromium
      - webkit
      - firefox

  assemblyai:
    name: AssemblyAI
    start_url: https://www.assemblyai.com/dashboard/signup
    email_domain_groups:
      - primary
    sender_domains: ["assemblyai.com"] 
    enabled: true
    headless: false
    browser_engines:
      - firefox

  firecrawl:
    name: Firecrawl
    start_url: https://www.firecrawl.dev/signin/signup
    email_domain_groups:
      - primary
    sender_domains: ["firecrawl.dev"]
    enabled: true
    headless: false
    browser_engines:
      - chrome