#!/usr/bin/env python3
"""
Test script for EXA service dropdown selection fix.
"""

import asyncio
import logging
import yaml
from services.exaai import ExaAIService
from core.config import Config

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)


async def test_exa_service():
    """Test the EXA service with fixed dropdown selection."""
    print("🧪 Starting EXA Service Dropdown Test...")

    try:
        # Load configuration
        with open("config.yaml", "r") as f:
            config_data = yaml.safe_load(f)

        config = Config(**config_data)
        service_config = config.services.get("exaai")

        if not service_config:
            print("❌ ExaAI service not found in config")
            return False

        # Initialize service
        print("🔧 Initializing EXA service...")
        exa_service = ExaAIService(service_config, config)

        # Test the service
        print("🚀 Running EXA service registration test...")
        result = await exa_service.register_and_get_api_key()

        if result.success:
            print(f"✅ EXA Service Test PASSED!")
            print(
                f"   - API Key: {result.api_key[:12]}..."
                if result.api_key
                else "   - No API key"
            )
            print(f"   - Email: {result.email}")
            print(f"   - Service: {result.service}")
            return True
        else:
            print(f"❌ EXA Service Test FAILED!")
            print(f"   - Error: {result.error_message}")
            return False

    except Exception as e:
        print(f"💥 Test crashed with error: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🎯 Testing EXA Service Dropdown Selection Fix")
    print("=" * 50)

    success = asyncio.run(test_exa_service())

    print("=" * 50)
    if success:
        print("🎉 TEST PASSED - Dropdown selection is working!")
    else:
        print("💔 TEST FAILED - Check logs for details")
