[project]
name = "regbot"
version = "0.1.0"
description = "Service registration automation system"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
dependencies = [
    "fastapi>=0.116.1",
    "uvicorn[standard]>=0.35.0",
    "playwright>=1.54.0",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.1.0",
    "pyyaml>=6.0.2",
    "httpx>=0.28.1",
    "tenacity>=8.2.3",
    "structlog>=23.2.0",
    "python-multipart>=0.0.6",
    "aiofiles>=24.1.0",
    "faker>=37.5.3",
    "toml>=0.10.2",
    "aiohttp>=3.12.15",
    "hatch>=1.14.1",
    "setuptools>=80.9.0",
    "requests>=2.32.4",
]
readme = "README.md"
requires-python = ">= 3.13"

[project.scripts]
regbot = "main:main"

[tool.poe.tasks]
# === MAIN AUTOMATION TASKS ===
run-assemblyai = "python main.py assemblyai"
run-exaai = "python main.py exaai"
run-webhook = "uvicorn webhook_server:app --host 0.0.0.0 --port 8888 --reload"

# === SETUP TASKS ===
install-browsers = "playwright install"
install-browsers-full = "playwright install chromium firefox webkit"
setup-env = "playwright install"
setup-dev = ["install-browsers", "pre-commit install"]
setup-ci = "playwright install chromium"

# === CONFIGURATION SYNC ===
sync-worker-config = "python sync_worker_config.py"

# === EMAIL WORKER TASKS ===
worker-deploy = "cd email-interceptor && pnpm wrangler deploy"

# === DEVELOPMENT WORKFLOWS ===
dev = "uvicorn webhook_server:app --port 8888 --reload"
emailworker-ready = ["sync-worker-config", "worker-deploy"]
test-flow = "uvicorn webhook_server:app --host 0.0.0.0 --port 8888 --reload"
full-setup = ["sync-worker-config", "install-browsers", "worker-deploy"]

# === TESTING WORKFLOWS ===
test = "pytest"
test-unit = "pytest -m unit -v --cov=core --cov=services"
test-integration = "pytest -m integration -v --cov=core --cov=services"
test-browser = "pytest -m browser -v --maxfail=3"
test-e2e = "pytest -m e2e -v --maxfail=1"
test-smoke = "pytest -m smoke"
test-performance = "pytest -m performance"
test-fast = "pytest -m 'unit or integration' -v"
test-all = "pytest -m 'unit or integration or browser' -v --cov=core --cov=services"
test-coverage = "pytest --cov=core --cov=services --cov-report=html"
test-parallel = "pytest -n auto"
test-watch = "pytest-watch tests/unit/ -- -v"

# === CODE QUALITY ===
lint = "ruff check ."
format = "ruff format ."
format-check = "ruff format --check ."
type-check = "mypy core/ services/ --ignore-missing-imports"
security = "bandit -r core/ services/ -f json -o bandit-report.json"
quality = ["lint", "format-check", "type-check", "security"]

# === COVERAGE & REPORTING ===
coverage = "pytest tests/ --cov=core --cov=services --cov-report=html"
coverage-xml = "pytest tests/ --cov=core --cov=services --cov-report=xml"

# === BUILD & CLEAN ===
build = "hatch build"
clean = "python -c \"import shutil; import glob; [shutil.rmtree(p, ignore_errors=True) for p in glob.glob('.pytest_cache')+glob.glob('htmlcov')+glob.glob('dist')+glob.glob('*.egg-info')]\""

# === DEVELOPMENT WORKFLOWS ===
dev-test = ["format", "lint", "test-fast"]
ci-test = ["quality", "test-all"]
pre-commit = "pre-commit run --all-files"

# === SERVICE-SPECIFIC TESTING ===
test-assemblyai = "pytest tests/ -k 'assemblyai' -v"
test-firecrawl = "pytest tests/ -k 'firecrawl' -v"
test-exaai = "pytest tests/ -k 'exaai' -v"
test-browser-manager = "pytest tests/ -k 'browser' -v"

# === AUTOMATION & REPORTS ===
qa = "python scripts/qa.py"
qa-with-performance = "python scripts/qa.py --performance"
generate-report = "python scripts/generate_test_report.py"

# === CI-SPECIFIC TARGETS ===
ci-install = "pip install -e .[dev]"
ci-test-unit = "pytest tests/unit/ --cov=core --cov=services --cov-report=xml --junitxml=test-results/unit.xml"
ci-test-integration = "pytest tests/integration/ --cov=core --cov=services --cov-report=xml --junitxml=test-results/integration.xml"
ci-test-browser = "pytest tests/browser/ --junitxml=test-results/browser.xml"
ci-test-e2e = "pytest tests/e2e/ --junitxml=test-results/e2e.xml"

[tool.poe]
executor.type = "uv"

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.3.1",
    "pytest-html>=4.1.1",
    "pytest-watch>=4.2.0",
    "pytest-benchmark>=4.0.0",
    "black>=23.11.0",
    "ruff>=0.1.6",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
    "coverage>=7.3.2",
    "bandit>=1.7.5",
]
test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.3.1",
    "pytest-html>=4.1.1",
    "pytest-watch>=4.2.0",
    "pytest-benchmark>=4.0.0",
    "coverage>=7.3.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "bandit>=1.8.6",
    "mypy>=1.17.1",
    "poethepoet>=0.36.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "pytest-html>=4.1.1",
    "pytest-mock>=3.14.1",
    "pytest-xdist>=3.8.0",
    "ruff>=0.12.7",
]

[tool.hatch.build.targets.wheel]
packages = ["core", "services"]

[tool.ruff]
target-version = "py312"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.black]
target-version = ['py312']
include = '\.pyi?$'
line-length = 88

[tool.mypy]
python_version = "3.12"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=core",
    "--cov=services", 
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=80",
    "--html=reports/pytest_report.html",
    "--self-contained-html"
]
markers = [
    "unit: Unit tests (fast, isolated)",
    "integration: Integration tests (medium speed, mocked dependencies)",
    "browser: Browser tests (requires browser installation)",
    "e2e: End-to-end tests (slow, full system)",
    "smoke: Smoke tests (basic health checks)",
    "performance: Performance tests"
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning"
]

[tool.coverage.run]
source = ["core", "services"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
    "setup.py",
    "conftest.py"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"


