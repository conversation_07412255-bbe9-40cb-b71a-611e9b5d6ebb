# RegBot Email Interceptor

Cloudflare Worker that provides generic email interception for service registration automation. This worker captures emails from various services and forwards them to configurable storage mechanisms without hardcoded parsing logic.

## Features

- 🎯 **Generic Service Detection**: Automatically identifies services based on sender domains
- 📧 **Raw Email Capture**: Captures complete email content without parsing
- 🪝 **Configurable Storage**: Forwards to webhook server or Cloudflare KV
- 🔧 **Environment-Driven**: All configuration via environment variables
- 🔄 **Fallback Options**: Email forwarding for unknown services

## Architecture Philosophy

This worker follows a **separation of concerns** principle:
- **Worker**: Only captures and forwards raw emails
- **Storage**: Only stores and retrieves emails
- **Service Logic**: All parsing happens in Python automation code

## Quick Start

1. **Install dependencies**:
   ```bash
   cd email-interceptor
   pnpm install
   ```

2. **Configure and deploy**:
   ```bash
   # Sync configuration from main config.yaml
   cd ..
   uv run python sync_worker_config.py
   
   # Deploy worker
   cd email-interceptor
   pnpm wrangler deploy
   ```

3. **Configure Cloudflare Email Routing** (see [DEPLOYMENT.md](DEPLOYMENT.md))

## Configuration

Configuration is automatically generated from the main `config.yaml` using `sync_worker_config.py`.

### Environment Variables

| Variable | Description | Example | Generated From |
|----------|-------------|---------|----------------|
| `WEBHOOK_URL` | Where to send captured emails | `https://api.example.com/webhook/email` | `email_retrieval.webhook.external_baseurl` |
| `FORWARD_EMAIL` | Backup email for unknown services | `<EMAIL>` | `email_interceptor.forward_email` |
| `ASSEMBLYAI_SENDER_DOMAINS` | AssemblyAI sender domains | `assemblyai.com` | `services.assemblyai.sender_domains` |

### Service Detection

The worker automatically detects services by matching sender domains to environment variables:

```javascript
// If email is from assemblyai.com and ASSEMBLYAI_SENDER_DOMAINS="assemblyai.com"
// Then service = "assemblyai"

function detectService(senderDomain, env) {
  for (const [key, value] of Object.entries(env)) {
    if (key.endsWith('_SENDER_DOMAINS') && value) {
      const serviceName = key.replace('_SENDER_DOMAINS', '').toLowerCase();
      const domains = value.split(',').map(d => d.trim());
      for (const domain of domains) {
        if (senderDomain === domain || senderDomain.endsWith('.' + domain)) {
          return serviceName;
        }
      }
    }
  }
  return null;
}
```

## Email Flow

### 1. Email Reception
```
Service Email → Cloudflare Email Routing → Worker
```

### 2. Service Detection
```javascript
// Example: <NAME_EMAIL>
senderDomain = "assemblyai.com"
env.ASSEMBLYAI_SENDER_DOMAINS = "assemblyai.com"
→ service = "assemblyai"
```

### 3. Email Storage
```javascript
// Send to webhook server or store in KV
await storeEmail(service, emailData)
```

### 4. Automation Retrieval
```python
# Python automation retrieves and parses
client = EmailClientFactory.create_client()
raw_content = await client.get_latest_email_content("assemblyai", email)
magic_link = AssemblyAIService.parse_magic_link(raw_content)
```

## Adding New Services

### 1. Update Configuration
Add to `config.yaml`:
```yaml
services:
  newservice:
    name: "New Service"
    start_url: "https://newservice.com/signup"
    sender_domains: ["newservice.com", "mail.newservice.com"]
    enabled: true
```

### 2. Sync Configuration
```bash
uv run python sync_worker_config.py
```

This automatically generates:
```toml
# In wrangler.toml
[vars]
NEWSERVICE_SENDER_DOMAINS = "newservice.com,mail.newservice.com"
```

### 3. Deploy Worker
```bash
cd email-interceptor
pnpm wrangler deploy
```

### 4. Implement Service Automation
Create `services/newservice.py` with parsing logic.

## Storage Mechanisms

### Webhook Storage
Sends HTTP POST to webhook server:
```json
{
  "service": "assemblyai",
  "from": "<EMAIL>",
  "to": "<EMAIL>",
  "subject": "Verify your email",
  "raw_content": "...",
  "timestamp": "2025-01-01T12:00:00Z"
}
```

### KV Storage
Stores in Cloudflare KV:
```javascript
// Key: assemblyai:<EMAIL>
// Value: { from, to, subject, raw_content, timestamp }
```

## Development Workflow

### Local Development
1. Start webhook server: `uv run python webhook_server.py`
2. Use tunnel for external access: `zrok share public http://localhost:8888`
3. Update external URL in config and sync
4. Deploy worker with webhook URL

### Testing
```bash
# View worker logs
pnpm wrangler tail

# Test email storage
curl -X POST https://your-worker.workers.dev/ \
  -H "Content-Type: application/json" \
  -d '{"test": "email"}'

# List KV contents  
pnpm wrangler kv:key list --binding EMAIL_KV
```

### Debugging
```bash
# Enable raw email storage for debugging
# In config.yaml:
email_interceptor:
  store_raw_email: true

# Check stored emails
uv run python -c "
from core.email_client_factory import EmailClientFactory
client = EmailClientFactory.create_client()
data = await client.get_latest_email_data('assemblyai', '<EMAIL>')
print(data.get('raw_content', '')[:500])
"
```

## Security Considerations

### Email Security
- Raw email content stored temporarily
- Automatic cleanup mechanisms
- No persistent storage of sensitive data

### Worker Security
- Keep webhook URLs private
- Use HTTPS for production webhooks
- Monitor worker analytics for abuse
- Implement rate limiting if needed

### Configuration Security
- Environment variables generated from config
- No hardcoded secrets in worker code
- Secure tunnel URLs for development

## Troubleshooting

### Email Not Intercepted
1. Check Email Routing configuration in Cloudflare Dashboard
2. Verify catch-all rule points to worker
3. Check sender domain environment variables
4. Review worker logs for errors

### Service Not Detected
1. Verify sender domain matches environment variable
2. Check domain matching logic (exact match or subdomain)
3. Review environment variable generation in sync script

### Storage Issues
**Webhook Mode:**
- Verify webhook server is running
- Check external tunnel URL accessibility
- Test webhook endpoint manually

**KV Mode:**
- Verify KV namespace exists
- Check KV permissions and API token
- Test KV operations manually

## Performance

### Optimization
- Minimal processing in worker (just forward emails)
- Efficient domain matching algorithm
- Automatic cleanup of old emails

### Monitoring
- Use Cloudflare Analytics to monitor worker performance
- Track email volume and processing times
- Monitor storage usage (KV or webhook server)

## Documentation References

- [DEPLOYMENT.md](DEPLOYMENT.md) - Complete deployment guide
- [SERVICE_PARSING_GUIDE.md](SERVICE_PARSING_GUIDE.md) - Service implementation guide
- [Cloudflare Email Workers Documentation](https://developers.cloudflare.com/email-routing/email-workers/)
- [Main Configuration Guide](CONFIG_GUIDE.md) - Configuration options