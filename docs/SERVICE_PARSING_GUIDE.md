# Service Implementation Guide

Guide for adding new services to RegBot's automation system.

## Overview

RegBot uses a clean separation of concerns where:
- **Email Worker**: Generic email capture (no service-specific logic)
- **Storage**: Simple email storage and retrieval
- **Service Modules**: All service-specific logic and parsing

## Adding a New Service

### 1. Configuration Setup

Add your service to `config.yaml`:

```yaml
services:
  newservice:
    name: "New Service"
    start_url: "https://newservice.com/signup"
    email_domain_groups: ["primary"]
    sender_domains: ["newservice.com", "mail.newservice.com"]
    enabled: true
```

**Configuration Fields**:
- `name`: Human-readable service name
- `start_url`: URL to begin registration process
- `email_domain_groups`: Which email domain groups this service uses
- `sender_domains`: Email domains this service sends from (for worker detection)
- `enabled`: Whether this service is active

### 2. Worker Configuration Sync

The sync script automatically generates environment variables for the worker:

```bash
# This creates NEWSERVICE_SENDER_DOMAINS environment variable
uv run python sync_worker_config.py

# Deploy worker with new configuration
cd email-interceptor
pnpm wrangler deploy
```

### 3. Service Module Implementation

Create `services/newservice.py`:

```python
"""New Service automation."""

import asyncio
import re
from typing import Optional, Dict, Any
from core.browser import BrowserManager
from core.models import RegistrationResult, ServiceCredentials
from core.config import ServiceConfig, Config
from core.profile import ProfileGenerator
from core.email_client_factory import EmailClientFactory
from core.domain_selector import DomainSelector
import logging

logger = logging.getLogger(__name__)

class NewService:
    """Automate New Service registration and API key extraction."""
    
    def __init__(self, config: ServiceConfig, full_config: Config):
        self.config = config
        self.full_config = full_config
        
        # Initialize domain selector for email domains
        service_domains = full_config.get_domains_for_service("newservice")
        if not service_domains:
            raise ValueError("No email domains configured for New Service")
        
        self.domain_selector = DomainSelector(service_domains)
        self.selected_domain = self.domain_selector.select_domain()
        
        self.profile_generator = ProfileGenerator(self.selected_domain)
        self.browser_manager: Optional[BrowserManager] = None
        self.profile = None
        
        # Initialize email client based on configuration
        self.email_client = EmailClientFactory.create_client()
    
    @staticmethod
    def parse_magic_link(raw_email_content: str) -> Optional[str]:
        """Parse magic link from New Service email content.
        
        Implement service-specific parsing logic here.
        This method should handle the email format that New Service uses.
        """
        if not raw_email_content:
            return None
        
        # Example parsing logic - adjust for your service
        # Look for verification links in the email
        patterns = [
            r'https://newservice\.com/verify/[A-Za-z0-9-_]+',
            r'https://auth\.newservice\.com/[A-Za-z0-9-_]+',
            # Add more patterns as needed
        ]
        
        for pattern in patterns:
            match = re.search(pattern, raw_email_content, re.IGNORECASE)
            if match:
                logger.info(f"Found magic link: {match.group(0)}")
                return match.group(0)
        
        logger.warning("No magic link found in email content")
        return None
    
    @staticmethod
    def parse_email_data(email_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse email data and extract relevant information."""
        raw_content = email_data.get('raw_content', '')
        magic_link = NewService.parse_magic_link(raw_content)
        
        return {
            'service': 'newservice',
            'magic_link': magic_link,
            'from': email_data.get('from', ''),
            'to': email_data.get('to', ''),
            'subject': email_data.get('subject', ''),
            'timestamp': email_data.get('timestamp', '')
        }
    
    async def register_and_get_api_key(self) -> RegistrationResult:
        """Main registration workflow."""
        try:
            # Generate profile
            self.profile = self.profile_generator.generate_profile()
            logger.info(f"Generated profile for {self.profile['email']}")
            
            async with BrowserManager(headless=self.full_config.browser_headless) as browser:
                self.browser_manager = browser
                
                # Step 1: Navigate to signup page
                await browser.page.goto(self.config.start_url)
                logger.info(f"Navigated to {self.config.start_url}")
                
                # Step 2: Fill registration form
                # Implement your service-specific form filling here
                await browser.page.fill('input[name="email"]', self.profile['email'])
                await browser.page.click('button[type="submit"]')
                
                logger.info("Registration form submitted, waiting for verification email...")
                
                # Step 3: Wait for magic link
                magic_link = None
                for attempt in range(6):  # 30 seconds total
                    await asyncio.sleep(5)
                    
                    # Get raw email content for this specific email
                    raw_content = await self.email_client.get_latest_email_content("newservice", self.profile['email'])
                    if raw_content:
                        # Parse magic link from raw content
                        magic_link = self.parse_magic_link(raw_content)
                        if magic_link:
                            logger.info(f"Magic link received after {(attempt + 1) * 5} seconds")
                            break
                    
                    logger.info(f"Waiting for magic link... ({(attempt + 1) * 5}s)")
                
                if not magic_link:
                    return RegistrationResult(
                        service="newservice",
                        success=False,
                        error_message="No magic link received within 30 seconds"
                    )
                
                # Step 4: Navigate to magic link
                await browser.page.goto(magic_link)
                logger.info(f"Navigated to magic link: {magic_link}")
                
                # Wait for page to load
                await asyncio.sleep(3)
                
                # Step 5: Extract API key
                # Implement your service-specific API key extraction here
                api_key = await self._extract_api_key(browser)
                
                if api_key:
                    credentials = ServiceCredentials(
                        service="newservice",
                        email=self.profile['email'],
                        api_key=api_key,
                        additional_data={}
                    )
                    
                    return RegistrationResult(
                        service="newservice",
                        success=True,
                        credentials=credentials
                    )
                else:
                    return RegistrationResult(
                        service="newservice",
                        success=False,
                        error_message="Failed to extract API key"
                    )
                    
        except Exception as e:
            logger.error(f"Registration failed: {e}")
            return RegistrationResult(
                service="newservice",
                success=False,
                error_message=str(e)
            )
    
    async def _extract_api_key(self, browser: BrowserManager) -> Optional[str]:
        """Extract API key from the service dashboard."""
        try:
            # Wait for dashboard to load
            await browser.page.wait_for_load_state("networkidle")
            
            # Look for API key in common locations
            # Adjust selectors for your service
            selectors = [
                'input[name="api_key"]',
                '.api-key',
                '[data-testid="api-key"]',
                'code:has-text("sk-")',  # Common API key prefix
            ]
            
            for selector in selectors:
                try:
                    element = await browser.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        api_key = await element.text_content() or await element.get_attribute('value')
                        if api_key and len(api_key) > 10:  # Basic validation
                            logger.info(f"Found API key with selector: {selector}")
                            return api_key.strip()
                except:
                    continue
            
            logger.warning("No API key found with standard selectors")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting API key: {e}")
            return None
```

### 4. Email Parsing Patterns

Different services use different email formats. Common patterns:

#### Magic Link Services
```python
# Direct verification links
r'https://service\.com/verify/[A-Za-z0-9-_]+'

# Token-based links  
r'https://service\.com/auth\?token=[A-Za-z0-9-_]+'

# Complex URLs with multiple parameters
r'https://service\.com/verify\?[^"]*token=[A-Za-z0-9-_]+'
```

#### Email-Based Codes
```python
# Numeric codes
r'Your verification code is:?\s*(\d{4,8})'

# Alphanumeric codes
r'Verification code:\s*([A-Z0-9]{6,12})'

# Token codes
r'Use this code:\s*([a-zA-Z0-9-_]{20,})'
```

#### Multi-Step Authentication
```python
@staticmethod
def parse_email_content(raw_email_content: str) -> Dict[str, Any]:
    """Parse different types of authentication emails."""
    
    # Try magic link first
    magic_link = re.search(r'https://service\.com/verify/[A-Za-z0-9-_]+', raw_email_content)
    if magic_link:
        return {'type': 'magic_link', 'value': magic_link.group(0)}
    
    # Try verification code
    code = re.search(r'code is:?\s*(\d{6})', raw_email_content)
    if code:
        return {'type': 'verification_code', 'value': code.group(1)}
    
    # Try reset token
    token = re.search(r'reset token:\s*([A-Za-z0-9-_]{32,})', raw_email_content)
    if token:
        return {'type': 'reset_token', 'value': token.group(1)}
    
    return {'type': 'unknown', 'value': None}
```

### 5. Service-Specific Considerations

#### Single-Step Services (Simple Magic Link)
```python
async def register_and_get_api_key(self):
    # 1. Fill registration form
    # 2. Wait for magic link email
    # 3. Navigate to magic link
    # 4. Extract API key
```

#### Multi-Step Services (Email + Password Reset)
```python
async def register_and_get_api_key(self):
    # 1. Fill registration form
    # 2. Wait for verification email
    # 3. Navigate to verification link
    # 4. Set password
    # 5. Navigate to dashboard
    # 6. Extract API key
```

#### Code-Based Services (Verification Codes)
```python
async def register_and_get_api_key(self):
    # 1. Fill registration form
    # 2. Wait for verification code email
    # 3. Extract code from email
    # 4. Enter code in form
    # 5. Complete registration
    # 6. Extract API key
```

## Testing Your Service

### 1. Configuration Test
```bash
# Test configuration loading
uv run python -c "
from core.config import Config
config = Config.load()
service_config = config.services.get('newservice')
print(f'Service: {service_config.name if service_config else \"Not found\"}')"
```

### 2. Email Parsing Test
```python
# Test email parsing
raw_email = """
Subject: Verify your New Service account

Click here to verify: https://newservice.com/verify/abc123xyz
"""

from services.newservice import NewService
magic_link = NewService.parse_magic_link(raw_email)
print(f"Extracted: {magic_link}")
```

### 3. Full Integration Test
```bash
# Deploy worker with new service
uv run python sync_worker_config.py
cd email-interceptor && pnpm wrangler deploy

# Run automation
uv run python run_automation.py newservice
```

## Debugging Tips

### 1. Enable Debug Logging
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 2. Capture Screenshots
```python
# In your service module
await browser.page.screenshot(path=f"debug-{step}.png")
```

### 3. Check Raw Email Content
```python
# Enable raw email storage in config.yaml
email_interceptor:
  store_raw_email: true

# Check stored emails
raw_content = await self.email_client.get_latest_email_content("newservice", email)
print(f"Raw email: {raw_content[:500]}")
```

### 4. Worker Logs
```bash
# Monitor worker logs
cd email-interceptor
pnpm wrangler tail
```

## Best Practices

### 1. Error Handling
```python
try:
    # Registration logic
    pass
except Exception as e:
    logger.error(f"Registration failed: {e}")
    return RegistrationResult(service="newservice", success=False, error_message=str(e))
```

### 2. Robust Selectors
```python
# Use multiple selector strategies
selectors = [
    'input[data-testid="api-key"]',  # Specific test ID
    'input[name="api_key"]',         # Name attribute
    '.api-key-value',                # CSS class
    'code:has-text("sk-")',          # Content-based
]
```

### 3. Timing Considerations
```python
# Wait for elements
await browser.page.wait_for_selector('button[type="submit"]')

# Wait for network
await browser.page.wait_for_load_state("networkidle")

# Custom waits
await asyncio.sleep(2)  # Simple delay when needed
```

### 4. Email Pattern Testing
```python
import re

def test_email_patterns():
    test_emails = [
        "Click here: https://service.com/verify/abc123",
        "Your code is: 123456",
        "Use token: xyz789abc"
    ]
    
    for email in test_emails:
        result = parse_magic_link(email)
        print(f"Email: {email[:30]}... → {result}")
```

## Common Issues

### 1. Email Not Detected
- Check sender domains in config match actual sender
- Verify worker environment variables
- Check worker logs for service detection

### 2. Magic Link Not Found
- Test parsing patterns with actual email content
- Enable raw email storage for debugging
- Check for email format changes

### 3. API Key Extraction Fails
- Update selectors for current dashboard
- Take screenshots to debug page state
- Check for authentication redirects

### 4. Timing Issues
- Increase wait times for slow services
- Use proper wait conditions
- Handle dynamic content loading