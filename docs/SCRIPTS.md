# RegBot Development Scripts

This project uses `uv` for dependency management and `<PERSON> the Poet` for task automation. All scripts are executed using `uv run poe <task>` which provides a modern, fast, and standards-compliant workflow.

## Setup

```bash
# Install dependencies
uv sync --dev

# Set up development environment (browsers + pre-commit hooks)
uv run poe setup-dev

# Set up CI environment (browsers only)
uv run poe setup-ci
```

## Main Automation

```bash
# Run individual services
uv run poe run-assemblyai
uv run poe run-exaai
uv run poe run-webhook

# Full setup workflow
uv run poe full-setup          # sync config + install browsers + deploy worker
uv run poe emailworker-ready   # sync config + deploy worker
```

## Testing

```bash
# Basic testing
uv run poe test               # basic pytest
uv run poe test-unit          # unit tests with coverage
uv run poe test-integration   # integration tests with coverage
uv run poe test-browser       # browser tests (requires browser install)
uv run poe test-e2e           # end-to-end tests
uv run poe test-fast          # unit + integration only (fast)
uv run poe test-all           # unit + integration + browser tests

# Testing utilities
uv run poe test-coverage      # generate HTML coverage report
uv run poe test-parallel      # run tests in parallel
uv run poe test-watch         # watch mode for unit tests

# Service-specific testing
uv run poe test-assemblyai    # test AssemblyAI service only
uv run poe test-firecrawl     # test Firecrawl service only
uv run poe test-exaai         # test ExaAI service only
```

## Code Quality

```bash
# Individual quality checks
uv run poe lint               # run ruff linter
uv run poe format             # format code with ruff
uv run poe format-check       # check if code is formatted
uv run poe type-check         # run mypy type checking
uv run poe security           # run bandit security scan

# Combined quality check
uv run poe quality            # run all quality checks
```

## Development Workflows

```bash
# Quick development cycle
uv run poe dev-test           # format + lint + fast tests

# Full CI simulation
uv run poe ci-test            # quality + all tests

# Pre-commit checks
uv run poe pre-commit         # run all pre-commit hooks
```

## Coverage & Reporting

```bash
# Coverage reports
uv run poe coverage           # HTML coverage report
uv run poe coverage-xml       # XML coverage report (for CI)

# Quality assurance
uv run poe qa                 # comprehensive QA script
uv run poe qa-with-performance # QA with performance benchmarks
uv run poe generate-report    # generate test report
```

## Build & Clean

```bash
# Build package
uv run poe build

# Clean generated files
uv run poe clean
```

## CI-Specific Commands

These are optimized for CI/CD pipelines:

```bash
uv run poe ci-install         # install for CI
uv run poe ci-test-unit       # unit tests with JUnit output
uv run poe ci-test-integration # integration tests with JUnit output
uv run poe ci-test-browser    # browser tests with JUnit output
uv run poe ci-test-e2e        # e2e tests with JUnit output
```

## Configuration & Workers

```bash
# Sync worker configuration
uv run poe sync-worker-config

# Deploy email worker
uv run poe worker-deploy
```

## Why Poe the Poet?

This project uses **Poe the Poet** as the task runner because:

- **Modern Best Practice**: Represents the current state-of-the-art for Python task automation in 2025
- **uv Integration**: Automatically detects and uses uv virtual environments 
- **Standards Compliant**: Works with standard Python packaging (PEP 518, 621)
- **Fast Performance**: Leverages uv's speed advantages
- **Simple Syntax**: Clean, readable task definitions in pyproject.toml

All tasks are defined in `pyproject.toml` under `[tool.poe.tasks]` and automatically use the project's virtual environment.