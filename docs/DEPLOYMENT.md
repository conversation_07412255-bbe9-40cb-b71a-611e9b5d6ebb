# RegBot Deployment Guide

Complete guide for deploying RegBot's email interception and automation system.

## Overview

RegBot consists of three main components:
1. **Cloudflare Email Worker**: Intercepts and forwards emails
2. **Storage Mechanism**: Webhook server or KV client for email retrieval
3. **Python Automation**: Service-specific registration automation

## Prerequisites

### Required Accounts
- **Cloudflare Account**: With Email Routing enabled
- **Domain(s)**: Configured in Cloudflare with Email Routing
- **Tunnel Service** (optional): For webhook server external access (zrok, ngrok, etc.)

### Required Tools
- **Node.js & pnpm**: For worker deployment
- **Python 3.11+**: For automation scripts
- **UV**: Python package manager (as per project requirements)
- **Wrangler CLI**: `pnpm install -g wrangler`

## Deployment Workflows

### Development Deployment

For local development with webhook server:

#### 1. Setup Configuration
```bash
# Copy example configuration
cp docs/config.example.yaml config.yaml

# Edit config.yaml with your settings:
# - Update email_domain_groups with your domains
# - Set client_type to "webhook"
# - Configure webhook URLs
```

#### 2. Setup External Tunnel
```bash
# Install and setup zrok (or use ngrok)
zrok share public http://localhost:8888

# Update config.yaml with tunnel URL:
email_retrieval:
  webhook:
    external_baseurl: "https://your-tunnel.zrok.io"
```

#### 3. Deploy Worker
```bash
# Sync configuration to worker
uv run python sync_worker_config.py

# Deploy worker
cd email-interceptor
pnpm install
pnpm wrangler deploy
```

#### 4. Configure Cloudflare Email Routing
1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com)
2. Select your domain
3. Navigate to **Email > Email Routing**
4. Enable Email Routing
5. Under **Routing Rules**, create catch-all:
   - **Match**: `*` (all addresses)
   - **Action**: Send to Worker → Select your deployed worker

#### 5. Start Local Services
```bash
# Start webhook server
uv run python webhook_server.py

# In another terminal, run automation
uv run python run_automation.py assemblyai
```

### Production Deployment

For production with KV storage:

#### 1. Setup Configuration
```bash
# Configure for production
vim config.yaml

# Set:
email_retrieval:
  client_type: "kv"

browser_headless: true

# Add KV credentials:
cloudflare_account_id: "your-account-id"
cloudflare_namespace_id: "your-namespace-id"
cloudflare_api_token: "your-api-token"
```

#### 2. Create KV Namespace
```bash
cd email-interceptor
pnpm wrangler kv:namespace create "EMAIL_KV"

# Update wrangler.toml with the namespace ID
```

#### 3. Deploy Worker
```bash
# Sync and deploy
cd ..
uv run python sync_worker_config.py
cd email-interceptor
pnpm wrangler deploy
```

#### 4. Run Automation
```bash
# Run automation (uses KV client automatically)
uv run python run_automation.py assemblyai
```

## Email Domain Configuration

### Cloudflare Email Routing Setup

For each domain in your `email_domain_groups`:

1. **Enable Email Routing**:
   - Dashboard → Domain → Email → Email Routing
   - Click "Enable Email Routing"
   - Follow DNS configuration prompts

2. **Create Catch-All Rule**:
   - **Routing Rules** → **Add Rule**
   - **Match**: `*` (matches all addresses)
   - **Action**: "Send to Worker"
   - **Worker**: Select your deployed worker

3. **Verify Configuration**:
   ```bash
   # Test email delivery
   echo "Test" | mail <EMAIL>
   
   # Check worker logs
   cd email-interceptor
   pnpm wrangler tail
   ```

### Domain Examples

**Single Domain Setup**:
```yaml
email_domain_groups:
  primary:
    - domain: "ai.company.com"
      weight: 100
      enabled: true
```

**Multi-Domain Load Balancing**:
```yaml
email_domain_groups:
  production:
    - domain: "reg1.company.com"
      weight: 40
      enabled: true
    - domain: "reg2.company.com"
      weight: 40
      enabled: true
    - domain: "reg3.company.com"
      weight: 20
      enabled: true
```

## Service Configuration

### Adding New Services

1. **Update Configuration**:
```yaml
services:
  newservice:
    name: "New Service"
    start_url: "https://newservice.com/signup"
    email_domain_groups: ["primary"]
    sender_domains: ["newservice.com"]
    enabled: true
```

2. **Sync and Deploy**:
```bash
uv run python sync_worker_config.py
cd email-interceptor && pnpm wrangler deploy
```

3. **Implement Service Logic**:
```python
# Create services/newservice.py
class NewService:
    @staticmethod
    def parse_magic_link(raw_email_content: str) -> Optional[str]:
        # Service-specific parsing logic
        pass
```

## Monitoring and Debugging

### Worker Monitoring
```bash
# View real-time logs
cd email-interceptor
pnpm wrangler tail

# View analytics
pnpm wrangler worker analytics

# List KV contents
pnpm wrangler kv:key list --binding EMAIL_KV
```

### Webhook Server Monitoring
```bash
# Check webhook server status
curl http://localhost:8888/health

# List stored emails
curl http://localhost:8888/emails

# Get specific email
curl http://localhost:8888/raw-email/assemblyai/<EMAIL>
```

### Configuration Testing
```bash
# Test domain selection
uv run python -c "
from core.config import Config
config = Config.load()
domains = config.get_domains_for_service('assemblyai')
print([d.domain for d in domains])
"

# Test email client
uv run python -c "
from core.email_client_factory import EmailClientFactory
client = EmailClientFactory.create_client()
print(f'Using: {type(client).__name__}')
"
```

## Security Configuration

### Production Security
- **Use HTTPS**: For all webhook URLs
- **Secure Tunnels**: For external webhook access
- **API Tokens**: Use minimal permissions for KV access
- **Rate Limiting**: Monitor worker usage for abuse

### API Key Security
```yaml
# Store API keys securely
data_path: "/secure/path/data"

# Use environment variables when possible
cloudflare_api_token: "${CF_API_TOKEN}"
```

### Email Security
```yaml
# Configure cleanup and forwarding
email_interceptor:
  store_raw_email: false  # Don't store raw emails in production
  forward_unknown: true   # Forward unknown emails
  forward_email: "<EMAIL>"
```

## Troubleshooting

### Common Issues

**Worker Not Receiving Emails**:
1. Check Email Routing configuration
2. Verify DNS MX records are correct
3. Confirm catch-all rule points to worker
4. Check worker logs for errors

**Service Detection Failing**:
1. Verify sender domains in config
2. Check environment variable generation
3. Review sync script output
4. Test with known sender addresses

**Storage Issues**:
- **Webhook**: Verify server running and accessible
- **KV**: Check credentials and namespace configuration
- Test storage mechanisms manually

### Debug Commands
```bash
# Check worker configuration
cat email-interceptor/wrangler.toml

# Test email interception
echo "Test" | mail <EMAIL>

# Check automation logs
uv run python run_automation.py assemblyai --debug

# Verify configuration sync
uv run python sync_worker_config.py --dry-run
```

## Performance Optimization

### Worker Performance
- Minimal processing in worker (just forward emails)
- Efficient domain matching
- Proper error handling and timeouts

### Storage Performance
- **Webhook**: Use fast HTTP server (FastAPI)
- **KV**: Leverage edge caching
- Implement email cleanup policies

### Automation Performance
- Parallel processing for multiple services
- Browser session reuse
- Intelligent retry mechanisms

## Cost Optimization

### Cloudflare Costs
- **Email Routing**: Free for most usage
- **Workers**: 100k requests/day free
- **KV**: 10GB storage, 1M reads/day free

### Resource Management
- Clean up old emails regularly
- Use efficient storage keys
- Monitor usage with analytics

## Backup and Recovery

### Configuration Backup
```bash
# Backup configuration
cp config.yaml config.backup.yaml

# Version control
git add docs/config.example.yaml
git commit -m "Update configuration template"
```

### Data Backup
```bash
# Export KV data
pnpm wrangler kv:key list --binding EMAIL_KV > kv-backup.json

# Backup webhook data
cp -r data/ data-backup/
```

## Migration Guide

### From Webhook to KV
1. Update `config.yaml`: Set `client_type: "kv"`
2. Configure KV credentials
3. Sync and deploy worker
4. Test email retrieval
5. Stop webhook server

### From KV to Webhook
1. Start webhook server
2. Update `config.yaml`: Set `client_type: "webhook"`
3. Sync and deploy worker
4. Test email retrieval