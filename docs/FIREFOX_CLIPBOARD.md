# Firefox Clipboard Enhancement

## Overview

This document describes the Firefox clipboard enhancement system implemented in RegBot's core browser library. The enhancement provides universal clipboard functionality for Firefox across all services.

## Problem Statement

Firefox has different clipboard API support and permission models compared to Chromium-based browsers:

1. **Permission Issues**: Firefox doesn't support `clipboard-read`/`clipboard-write` permissions in browser context creation
2. **API Limitations**: Firefox's `navigator.clipboard` API has stricter security requirements
3. **HTTPS Requirements**: Clipboard API often requires secure contexts that may not be available
4. **Event Handling**: Different clipboard event handling compared to Chromium

## Solution Architecture

### 1. Permission Filtering (`core/browser.py:134-159`)

```javascript
// Automatically filters out unsupported permissions for Firefox
if (perm == 'clipboard-read' || perm == 'clipboard-write') {
    if (browser_engine in ['chromium', 'chrome']) {
        safe_permissions.append(perm)
    } else {
        logger.debug(f"Skipping {perm} permission for {browser_engine}")
    }
}
```

### 2. Enhancement Script Injection (`core/browser.py:172-302`)

Automatically injects comprehensive clipboard enhancement scripts when `browser_engine == 'firefox'`:

#### Key Components:

**Global Storage Variables**:
- `window.clipboardContent`: Primary clipboard content storage
- `window.lastCopiedContent`: Backup clipboard content storage

**API Overrides**:
- `navigator.clipboard.writeText()`: Enhanced with content capture and execCommand fallback
- `navigator.clipboard.readText()`: Enhanced with multiple retrieval methods

**Event Listeners**:
- `copy` events: Automatically capture clipboard content
- `paste` events: Store pasted content for later retrieval

### 3. Service Integration

All services use standardized Firefox clipboard reading methods:

```python
async def _firefox_clipboard_read(self, page) -> Optional[str]:
    """Firefox-specific clipboard reading with multiple fallback methods."""
    
    # Method 1: Enhanced clipboard API (injected)
    clipboard_content = await page.evaluate("() => navigator.clipboard.readText()")
    
    # Method 2: Global clipboard content
    global_content = await page.evaluate("() => window.clipboardContent || ''")
    
    # Method 3: Last copied content
    last_content = await page.evaluate("() => window.lastCopiedContent || ''")
    
    # Method 4: execCommand paste
    # Method 5: Trigger copy and capture
```

## Implementation Details

### Automatic Activation

The enhancement is automatically applied when Firefox is detected:

```python
# In core/browser.py start() method
if self.browser_engine == 'firefox':
    await self._add_firefox_clipboard_enhancements()
```

### Enhancement Script Features

1. **Content Capture**: Intercepts all clipboard write operations
2. **Fallback Methods**: Provides execCommand-based operations when native API fails
3. **Event Monitoring**: Listens for copy/paste events to capture content
4. **Debug Logging**: Comprehensive console logging for troubleshooting

### Service Usage Pattern

Services implement Firefox-specific clipboard reading:

```python
# Handle different browser engines for clipboard access
if browser.browser_engine == 'firefox':
    api_key = await self._firefox_clipboard_read(browser.page)
elif browser.browser_engine in ['chromium', 'webkit']:
    api_key = await browser.page.evaluate("() => navigator.clipboard.readText()")
else:
    api_key = await self._universal_clipboard_read(browser.page)
```

## Testing

### Test Coverage

- **Unit Tests**: `tests/test_firefox_clipboard.py`
- **Integration Tests**: ExaAI service end-to-end testing
- **Manual Testing**: `main.py exaai --debug` with Firefox configuration

### Verification Methods

1. **Debug Logging**: Console messages show clipboard operations
2. **Screenshots**: Debug screenshots capture clipboard interaction points
3. **Content Validation**: API key extraction success confirms functionality

## Configuration

### Browser Engine Selection

Configure Firefox usage in `config.yaml`:

```yaml
services:
  exaai:
    browser_engines: ["firefox"]  # Firefox only
    # or
    browser_engines: ["firefox", "chromium"]  # Firefox preferred
```

### Debug Mode

Enable debug mode for detailed clipboard operation logging:

```bash
uv run python main.py exaai --debug
```

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure clipboard permissions are filtered for Firefox
2. **Empty Clipboard**: Check if enhancement scripts are properly injected
3. **API Failures**: Verify fallback methods are being attempted

### Debug Information

Enable debug logging to see clipboard operation details:

```
🦊 Firefox clipboard enhancements loaded
📋 Clipboard writeText called with: [content]
📋 Copy event detected
🦊 Firefox Method 1 (enhanced API) success: 36 chars
```

## References

- **Core Implementation**: `core/browser.py:172-302`
- **Service Examples**: `services/exaai.py:2077+`, `services/firecrawl.py:363+`
- **Architecture Documentation**: `docs/ARCHITECTURE.md`
- **Test Suite**: `tests/test_firefox_clipboard.py`

## Future Enhancements

1. **WebKit Support**: Extend similar enhancements for WebKit clipboard limitations
2. **Content Type Support**: Add support for rich text and HTML clipboard content
3. **Performance Optimization**: Reduce script injection overhead
4. **Cross-Browser Compatibility**: Standardize clipboard API across all browsers
