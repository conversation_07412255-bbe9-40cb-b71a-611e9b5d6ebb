#!/usr/bin/env python3
"""Sync configuration from config.yaml to wrangler.toml with enhanced domain handling."""

import yaml
import sys
from pathlib import Path
from typing import Dict, Any, List


def load_config() -> Dict[str, Any]:
    """Load configuration from config.yaml"""
    config_path = Path("config.yaml")
    if not config_path.exists():
        print("❌ config.yaml not found. Please create it first.")
        sys.exit(1)

    with open(config_path, "r") as f:
        return yaml.safe_load(f)


def get_enabled_domains(config: Dict[str, Any]) -> List[str]:
    """Extract all enabled domains from email_domain_groups"""
    domains = []

    if "email_domain_groups" not in config:
        return domains

    for group_name, group_domains in config["email_domain_groups"].items():
        for domain_config in group_domains:
            if domain_config.get("enabled", False):
                domains.append(domain_config["domain"])

    return domains


def get_domain_statistics(config: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
    """Get domain statistics and configuration details."""
    stats = {}

    if "email_domain_groups" not in config:
        return stats

    for group_name, group_domains in config["email_domain_groups"].items():
        group_stats = {
            "total_domains": len(group_domains),
            "enabled_domains": sum(1 for d in group_domains if d.get("enabled", False)),
            "total_weight": sum(
                d.get("weight", 100) for d in group_domains if d.get("enabled", False)
            ),
            "domains": [],
        }

        for domain_config in group_domains:
            group_stats["domains"].append(
                {
                    "domain": domain_config["domain"],
                    "enabled": domain_config.get("enabled", False),
                    "weight": domain_config.get("weight", 100),
                }
            )

        stats[group_name] = group_stats

    return stats


def validate_configuration(config: Dict[str, Any]) -> List[str]:
    """Validate configuration and return list of warnings/issues."""
    warnings = []

    # Check if any domains are enabled
    enabled_domains = get_enabled_domains(config)
    if not enabled_domains:
        warnings.append(
            "No email domains are enabled - the worker will reject all emails"
        )

    # Check webhook URL configuration
    email_retrieval = config.get("email_retrieval", {})
    webhook_config = email_retrieval.get("webhook", {})
    if not webhook_config.get("external_baseurl"):
        warnings.append(
            "No external webhook URL configured - emails may not be processed"
        )

    # Check service configurations
    services_config = config.get("services", {})
    enabled_services = [
        name for name, svc in services_config.items() if svc.get("enabled", True)
    ]
    if not enabled_services:
        warnings.append("No services are enabled")

    # Check if enabled services have corresponding sender domains
    for service_name in enabled_services:
        service_config = services_config[service_name]
        if not service_config.get("sender_domains"):
            warnings.append(
                f"Service '{service_name}' has no sender domains configured"
            )

    return warnings


def update_wrangler_toml(config: Dict[str, Any]) -> None:
    """Update wrangler.toml with values from config.yaml"""
    wrangler_path = Path("email-interceptor/wrangler.toml")

    if not wrangler_path.exists():
        print("❌ email-interceptor/wrangler.toml not found")
        sys.exit(1)

    # Extract email interceptor config
    email_config = config.get("email_interceptor", {})
    email_retrieval = config.get("email_retrieval", {})
    webhook_config = email_retrieval.get("webhook", {})
    services_config = config.get("services", {})

    # Get enabled domains
    enabled_domains = get_enabled_domains(config)

    # Build new wrangler.toml content
    lines = []

    # Basic config
    worker_name = email_config.get("worker_name", "regbot-emailparser")
    lines.append(f'name = "{worker_name}"')
    lines.append('main = "src/index.js"')
    lines.append('compatibility_date = "2025-07-01"')
    lines.append("")

    # KV namespace
    lines.append("[[kv_namespaces]]")
    lines.append('binding = "EMAIL_KV"')
    lines.append('id = "a3685ff710954275ae7a92f599d940d7"')
    lines.append("")

    # [vars] section
    lines.append("[vars]")

    # Set webhook URL from external URL + /webhook/email
    if "external_baseurl" in webhook_config:
        webhook_url = f"{webhook_config['external_baseurl']}/webhook/email"
        lines.append(f'WEBHOOK_URL = "{webhook_url}"')

    # Set other email interceptor options
    if "forward_email" in email_config:
        lines.append(f'FORWARD_EMAIL = "{email_config["forward_email"]}"')

    if "forward_unknown" in email_config:
        lines.append(
            f'FORWARD_UNKNOWN = "{str(email_config["forward_unknown"]).lower()}"'
        )

    if "store_raw_email" in email_config:
        lines.append(
            f'STORE_RAW_EMAIL = "{str(email_config["store_raw_email"]).lower()}"'
        )

    # Set configured domains
    if enabled_domains:
        lines.append(f'CONFIGURED_DOMAINS = "{",".join(enabled_domains)}"')

    # Add service-specific sender domains as environment variables
    for service_name, service_config in services_config.items():
        if service_config.get("enabled", True):
            sender_domains = service_config.get("sender_domains", [])
            if sender_domains:
                env_var_name = f"{service_name.upper()}_SENDER_DOMAINS"
                domains_str = ",".join(sender_domains)
                lines.append(f'{env_var_name} = "{domains_str}"')

    lines.append("")

    # Write updated wrangler.toml
    with open(wrangler_path, "w") as f:
        f.write("\n".join(lines))

    print("✅ Successfully synced config.yaml → wrangler.toml")
    print(f"   Worker name: {worker_name}")

    if "external_baseurl" in webhook_config:
        print(f"   Webhook URL: {webhook_config['external_baseurl']}/webhook/email")

    print(
        f"   Configured domains: {','.join(enabled_domains) if enabled_domains else 'None'}"
    )

    # Show service configurations
    for service_name, service_config in services_config.items():
        if service_config.get("enabled", True):
            sender_domains = service_config.get("sender_domains", [])
            if sender_domains:
                print(f"   {service_name} sender domains: {','.join(sender_domains)}")


def show_domain_statistics(config: Dict[str, Any]) -> None:
    """Display domain statistics and configuration overview."""
    stats = get_domain_statistics(config)

    if not stats:
        print("   No domain groups configured")
        return

    print("\n📊 Domain Group Statistics:")
    for group_name, group_stats in stats.items():
        print(f"   {group_name}:")
        print(f"     Total domains: {group_stats['total_domains']}")
        print(f"     Enabled domains: {group_stats['enabled_domains']}")
        print(f"     Total weight: {group_stats['total_weight']}")

        for domain_info in group_stats["domains"]:
            status = "✅" if domain_info["enabled"] else "❌"
            print(
                f"       {status} {domain_info['domain']} (weight: {domain_info['weight']})"
            )


def main():
    """Main function"""
    print("🔄 Syncing configuration from config.yaml to wrangler.toml...")

    try:
        config = load_config()

        # Validate configuration
        warnings = validate_configuration(config)
        if warnings:
            print("\n⚠️  Configuration warnings:")
            for warning in warnings:
                print(f"   - {warning}")
            print()

        # Update wrangler.toml
        update_wrangler_toml(config)

        # Show domain statistics
        show_domain_statistics(config)

        # Show service statistics
        services_config = config.get("services", {})
        enabled_services = [
            name for name, svc in services_config.items() if svc.get("enabled", True)
        ]
        disabled_services = [
            name
            for name, svc in services_config.items()
            if not svc.get("enabled", True)
        ]

        print(f"\n🎯 Service Status:")
        if enabled_services:
            print(f"   Enabled: {', '.join(enabled_services)}")
        if disabled_services:
            print(f"   Disabled: {', '.join(disabled_services)}")

        # Show continuous automation settings
        automation_config = config.get("continuous_automation", {})
        if automation_config:
            print(f"\n🤖 Continuous Automation:")
            print(
                f"   Interval: {automation_config.get('min_interval_minutes', 30)}-{automation_config.get('max_interval_minutes', 90)} minutes"
            )
            print(
                f"   Target registrations: {automation_config.get('target_registrations', 10)}"
            )
            print(f"   Max failures: {automation_config.get('max_failed_attempts', 3)}")
            print(
                f"   Randomization: {'enabled' if automation_config.get('enable_mouse_movements', True) else 'disabled'}"
            )

        print("\n💡 Next steps:")
        print("   1. Deploy worker: cd email-interceptor && pnpm wrangler deploy")
        print("   2. Test automation: uv run python continuous_main.py assemblyai")
        print("   3. Check status: uv run python continuous_main.py --status")

    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
