"""Profile generator for service registrations."""

from faker import Faker
from typing import Dict, Any
import string


class ProfileGenerator:
    """Generate fake profiles for service registrations."""

    def __init__(self, domain: str, randomizer=None):
        self.faker = Faker()
        self.domain = domain
        self.randomizer = randomizer  # Optional randomizer injection

    def generate_profile(self) -> Dict[str, Any]:
        """Generate a complete profile for registration."""
        # Generate shorter username (just first name + numbers)
        first_name = self.faker.first_name().lower()
        if self.randomizer:
            random_suffix = "".join(self.randomizer.choices(string.digits, k=3))
        else:
            # Fallback to Python's random for backwards compatibility
            import random

            random_suffix = "".join(
                random.choices(string.digits, k=3)
            )  # Shorter suffix

        username = f"{first_name}{random_suffix}"  # Just firstname + numbers
        email = f"{username}@{self.domain}"

        # Generate secure password
        password = self._generate_password()

        return {
            "first_name": first_name.capitalize(),
            "last_name": self.faker.last_name().capitalize(),  # Generate last name when needed
            "username": username,
            "email": email,
            "password": password,
            "company": self.faker.company(),
            "job_title": self.faker.job(),
            "phone": self.faker.phone_number(),
            "country": self.faker.country(),
            "city": self.faker.city(),
            "address": self.faker.address(),
            "website": self.faker.url(),
            "bio": self.faker.text(max_nb_chars=200),
        }

    def _generate_password(self, length: int = 16) -> str:
        """Generate a secure password."""
        # Ensure password has all required character types
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special = "!@#$%^&*"

        # At least one of each type
        if self.randomizer:
            password = [
                self.randomizer.choice(lowercase),
                self.randomizer.choice(uppercase),
                self.randomizer.choice(digits),
                self.randomizer.choice(special),
            ]

            # Fill the rest
            all_chars = lowercase + uppercase + digits + special
            for _ in range(length - 4):
                password.append(self.randomizer.choice(all_chars))

            # Shuffle to avoid predictable patterns
            self.randomizer.shuffle(password)
        else:
            # Fallback to Python's random for backwards compatibility
            import random

            password = [
                random.choice(lowercase),
                random.choice(uppercase),
                random.choice(digits),
                random.choice(special),
            ]

            # Fill the rest
            all_chars = lowercase + uppercase + digits + special
            for _ in range(length - 4):
                password.append(random.choice(all_chars))

            # Shuffle to avoid predictable patterns
            random.shuffle(password)
        return "".join(password)

    def generate_email_variations(self, base_email: str) -> Dict[str, str]:
        """Generate email variations using + addressing."""
        username, domain = base_email.split("@")

        return {
            "base": base_email,
            "plus_service": f"{username}+assemblyai@{domain}",
            "plus_timestamp": f"{username}+{int(self.faker.unix_time())}@{domain}",
            "plus_random": f"{username}+{self.faker.word()}@{domain}",
        }
