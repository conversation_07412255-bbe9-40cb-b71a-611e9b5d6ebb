"""Browser profile generation for anti-detection."""

import random
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass


@dataclass
class BrowserProfile:
    """Complete browser profile for anti-detection."""

    # Screen & Window
    screen_width: int
    screen_height: int
    available_width: int
    available_height: int
    device_scale_factor: float

    # Browser properties
    user_agent: str
    platform: str
    language: str
    languages: List[str]
    timezone: str

    # Hardware fingerprint
    hardware_concurrency: int
    color_depth: int
    pixel_depth: int

    # WebGL fingerprint (critical for detection)
    webgl_vendor: str
    webgl_renderer: str

    # Behavior settings
    is_mobile: bool = False


class BrowserProfileGenerator:
    """Generate realistic browser profiles for anti-detection."""

    # Platform configurations
    WINDOWS_CONFIGS = {
        "platforms": ["Win32"],
        "screen_sizes": [
            (1920, 1080),
            (1366, 768),
            (1440, 900),
            (1536, 864),
        ],  # Removed 2560x1440 fullscreen
        "webgl_vendors": [
            "Google Inc. (NVIDIA)",
            "Google Inc. (AMD)",
            "Google Inc. (Intel)",
        ],
        "webgl_renderers": [
            "ANGLE (NVIDIA, NVIDIA GeForce RTX 3070)",
            "ANGLE (NVIDIA, NVIDIA GeForce GTX 1660)",
            "ANGLE (AMD, AMD Radeon RX 6700 XT)",
            "ANGLE (Intel, Intel(R) UHD Graphics 630)",
        ],
        "hardware_cores": [4, 6, 8, 12, 16],
        "user_agent_template": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version} Safari/537.36",
    }

    MACOS_CONFIGS = {
        "platforms": ["MacIntel"],
        "screen_sizes": [
            (1440, 900),
            (1920, 1080),
            (1680, 1050),
        ],  # Removed 2560x1440 fullscreen
        "webgl_vendors": ["Google Inc. (Apple)"],
        "webgl_renderers": [
            "ANGLE (Apple, Apple M1, OpenGL 4.1)",
            "ANGLE (Apple, AMD Radeon Pro 5500M, OpenGL 4.1)",
            "ANGLE (Apple, Intel Iris Pro, OpenGL 4.1)",
        ],
        "hardware_cores": [4, 8, 10],
        "device_scale_factors": [1, 2],  # Retina vs non-Retina
        "user_agent_template": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version} Safari/537.36",
    }

    ANDROID_CONFIGS = {
        "platforms": ["Linux armv8l"],
        "screen_sizes": [(393, 851), (412, 915), (360, 800), (414, 896)],
        "webgl_vendors": ["Google Inc. (Qualcomm)", "Google Inc. (ARM)"],
        "webgl_renderers": [
            "Adreno (TM) 730",
            "Adreno (TM) 660",
            "Mali-G78 MP20",
            "Mali-G710 MC10",
        ],
        "hardware_cores": [6, 8],
        "device_scale_factors": [2.75, 3, 3.5],
        "user_agent_template": "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version} Mobile Safari/537.36",
    }

    TIMEZONES = [
        "America/New_York",
        "America/Chicago",
        "America/Los_Angeles",
        "America/Denver",
        "America/Phoenix",
        "America/Anchorage",
    ]

    LANGUAGES = [
        (["en-US", "en"], "en-US,en;q=0.9"),
        (["en-GB", "en"], "en-GB,en;q=0.9"),
        (["en-CA", "en"], "en-CA,en;q=0.9"),
    ]

    CHROME_VERSIONS = [
        "124.0.6367.118",
        "124.0.6367.119",
        "124.0.6367.120",
        "125.0.6422.112",
        "125.0.6422.113",
        "125.0.6422.114",
        "126.0.6478.114",
        "126.0.6478.115",
        "126.0.6478.116",
        "127.0.6533.88",
        "127.0.6533.89",
        "127.0.6533.99",
    ]

    def __init__(self, randomizer=None):
        """Initialize with optional randomizer for reproducible profiles."""
        self.randomizer = randomizer or random

    def generate_profile(self, platform_type: str = None) -> BrowserProfile:
        """Generate a complete browser profile."""
        platform_type = platform_type or self.randomizer.choice(
            ["windows", "macos", "android"]
        )

        if platform_type == "windows":
            return self._generate_windows_profile()
        elif platform_type == "macos":
            return self._generate_macos_profile()
        elif platform_type == "android":
            return self._generate_android_profile()
        else:
            raise ValueError(f"Unsupported platform: {platform_type}")

    def _generate_windows_profile(self) -> BrowserProfile:
        """Generate Windows desktop profile."""
        config = self.WINDOWS_CONFIGS
        screen_width, screen_height = self.randomizer.choice(config["screen_sizes"])
        chrome_version = self.randomizer.choice(self.CHROME_VERSIONS)
        languages_list, language_header = self.randomizer.choice(self.LANGUAGES)

        return BrowserProfile(
            screen_width=screen_width,
            screen_height=screen_height,
            available_width=screen_width,
            available_height=screen_height - 40,  # Account for taskbar
            device_scale_factor=1.0,
            user_agent=config["user_agent_template"].format(version=chrome_version),
            platform=config["platforms"][0],
            language=language_header,
            languages=languages_list,
            timezone=self.randomizer.choice(self.TIMEZONES),
            hardware_concurrency=self.randomizer.choice(config["hardware_cores"]),
            color_depth=24,
            pixel_depth=24,
            webgl_vendor=self.randomizer.choice(config["webgl_vendors"]),
            webgl_renderer=self.randomizer.choice(config["webgl_renderers"]),
            is_mobile=False,
        )

    def _generate_macos_profile(self) -> BrowserProfile:
        """Generate macOS desktop profile."""
        config = self.MACOS_CONFIGS
        screen_width, screen_height = self.randomizer.choice(config["screen_sizes"])
        chrome_version = self.randomizer.choice(self.CHROME_VERSIONS)
        languages_list, language_header = self.randomizer.choice(self.LANGUAGES)
        scale_factor = self.randomizer.choice(config.get("device_scale_factors", [1]))

        return BrowserProfile(
            screen_width=screen_width,
            screen_height=screen_height,
            available_width=screen_width,
            available_height=screen_height - 50,  # Account for macOS menu bar and dock
            device_scale_factor=scale_factor,
            user_agent=config["user_agent_template"].format(version=chrome_version),
            platform=config["platforms"][0],
            language=language_header,
            languages=languages_list,
            timezone=self.randomizer.choice(self.TIMEZONES),
            hardware_concurrency=self.randomizer.choice(config["hardware_cores"]),
            color_depth=24,
            pixel_depth=24,
            webgl_vendor=config["webgl_vendors"][0],
            webgl_renderer=self.randomizer.choice(config["webgl_renderers"]),
            is_mobile=False,
        )

    def _generate_android_profile(self) -> BrowserProfile:
        """Generate Android mobile profile."""
        config = self.ANDROID_CONFIGS
        screen_width, screen_height = self.randomizer.choice(config["screen_sizes"])
        chrome_version = self.randomizer.choice(self.CHROME_VERSIONS)
        languages_list, language_header = self.randomizer.choice(self.LANGUAGES)
        scale_factor = self.randomizer.choice(config["device_scale_factors"])

        return BrowserProfile(
            screen_width=screen_width,
            screen_height=screen_height,
            available_width=screen_width,
            available_height=screen_height - 30,  # Account for mobile navigation bar
            device_scale_factor=scale_factor,
            user_agent=config["user_agent_template"].format(version=chrome_version),
            platform=config["platforms"][0],
            language=language_header,
            languages=languages_list,
            timezone=self.randomizer.choice(self.TIMEZONES),
            hardware_concurrency=self.randomizer.choice(config["hardware_cores"]),
            color_depth=24,
            pixel_depth=24,
            webgl_vendor=self.randomizer.choice(config["webgl_vendors"]),
            webgl_renderer=self.randomizer.choice(config["webgl_renderers"]),
            is_mobile=True,
        )

    def convert_to_playwright_args(self, profile: BrowserProfile) -> Dict:
        """Convert profile to Playwright launch arguments (lightweight implementation)."""
        # Essential browser args - minimal set for basic functionality and performance
        base_args = [
            f"--window-size={profile.screen_width},{profile.screen_height}",  # Required for viewport consistency
            "--disable-blink-features=AutomationControlled",  # Basic automation property cleanup
            "--no-first-run",  # Skip first-run setup (performance)
            "--disable-background-timer-throttling",  # Performance optimization
            "--disable-backgrounding-occluded-windows",  # Performance optimization
            "--disable-renderer-backgrounding",  # Performance optimization
            "--disable-extensions",  # Security and performance
            "--no-sandbox",  # Required for some environments
            "--disable-dev-shm-usage",  # Required for some environments
        ]

        # Add mobile-specific args
        if profile.is_mobile:
            base_args.extend(
                [
                    "--use-mobile-user-agent",
                    "--enable-touch-events",
                    "--force-device-scale-factor=" + str(profile.device_scale_factor),
                ]
            )

        return {
            "args": base_args,
            "viewport": {
                "width": profile.screen_width,
                "height": profile.screen_height,
            },
            "user_agent": profile.user_agent,
            "device_scale_factor": profile.device_scale_factor,
            "locale": profile.languages[0] if profile.languages else "en-US",
            "timezone_id": profile.timezone,
            "is_mobile": profile.is_mobile,
            "has_touch": profile.is_mobile,
            "extra_http_headers": {
                "Accept-Language": profile.language,
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "max-age=0",
                "sec-ch-ua": f'"Google Chrome";v="{self.CHROME_VERSIONS[0].split(".")[0]}", "Chromium";v="{self.CHROME_VERSIONS[0].split(".")[0]}", "Not_A Brand";v="24"',
                "sec-ch-ua-mobile": "?1" if profile.is_mobile else "?0",
                "sec-ch-ua-platform": f'"{profile.platform}"',
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "none",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1",
            },
            "permissions": ["clipboard-read"] if not profile.is_mobile else [],
        }

    def generate_navigator_script(self, profile: BrowserProfile) -> str:
        """Generate JavaScript to override navigator properties."""
        return f"""
        () => {{
            // Remove webdriver property completely
            Object.defineProperty(navigator, 'webdriver', {{
                get: () => undefined,
                configurable: true
            }});

            // Remove automation indicators
            delete navigator.__webdriver_script_fn;
            delete navigator.__webdriver_evaluate;
            delete navigator.__webdriver_unwrapped;
            delete navigator.__fxdriver_evaluate;
            delete navigator.__fxdriver_unwrapped;
            delete navigator.__driver_evaluate;
            delete navigator.__webdriver_evaluate__;
            delete navigator.__selenium_evaluate;
            delete navigator.__selenium_unwrapped;
            delete navigator.__fxdriver_evaluate__;
            delete navigator.__driver_unwrapped;
            delete navigator.__selenium_unwrapped__;
            delete navigator.__fxdriver_unwrapped__;
            delete navigator._Selenium_IDE_Recorder;
            delete navigator._selenium;
            delete navigator.calledSelenium;
            delete navigator.$cdc_asdjflasutopfhvcZLmcfl_;
            delete navigator.$chrome_asyncScriptInfo;
            delete navigator.__$webdriverAsyncExecutor;

            // Override platform
            Object.defineProperty(navigator, 'platform', {{
                get: () => '{profile.platform}',
                configurable: true
            }});

            // Override languages
            Object.defineProperty(navigator, 'languages', {{
                get: () => {profile.languages},
                configurable: true
            }});

            Object.defineProperty(navigator, 'language', {{
                get: () => '{profile.languages[0] if profile.languages else "en-US"}',
                configurable: true
            }});

            // Override hardware
            Object.defineProperty(navigator, 'hardwareConcurrency', {{
                get: () => {profile.hardware_concurrency},
                configurable: true
            }});

            // Override memory (common values)
            Object.defineProperty(navigator, 'deviceMemory', {{
                get: () => 8,
                configurable: true
            }});

            // Override connection
            Object.defineProperty(navigator, 'connection', {{
                get: () => ({{
                    effectiveType: '4g',
                    rtt: 50,
                    downlink: 10,
                    saveData: false
                }}),
                configurable: true
            }});

            // Override screen properties
            Object.defineProperty(screen, 'width', {{
                get: () => {profile.screen_width},
                configurable: true
            }});

            Object.defineProperty(screen, 'height', {{
                get: () => {profile.screen_height},
                configurable: true
            }});

            Object.defineProperty(screen, 'availWidth', {{
                get: () => {profile.available_width},
                configurable: true
            }});

            Object.defineProperty(screen, 'availHeight', {{
                get: () => {profile.available_height},
                configurable: true
            }});

            Object.defineProperty(screen, 'colorDepth', {{
                get: () => {profile.color_depth},
                configurable: true
            }});

            Object.defineProperty(screen, 'pixelDepth', {{
                get: () => {profile.pixel_depth},
                configurable: true
            }});

            // Override WebGL properties
            const getContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(contextType, ...args) {{
                const context = getContext.apply(this, [contextType, ...args]);
                if (contextType === 'webgl' || contextType === 'experimental-webgl') {{
                    const getParameter = context.getParameter;
                    context.getParameter = function(parameter) {{
                        if (parameter === context.RENDERER) {{
                            return '{profile.webgl_renderer}';
                        }}
                        if (parameter === context.VENDOR) {{
                            return '{profile.webgl_vendor}';
                        }}
                        return getParameter.apply(this, arguments);
                    }};
                }}
                return context;
            }};

            // Override permissions API
            const originalQuery = navigator.permissions.query;
            navigator.permissions.query = (parameters) => {{
                return parameters.name === 'notifications' ?
                    Promise.resolve({{ state: Notification.permission }}) :
                    originalQuery(parameters);
            }};

            // Override plugins to appear realistic
            Object.defineProperty(navigator, 'plugins', {{
                get: () => [
                    {{
                        0: {{ type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format" }},
                        description: "Portable Document Format",
                        filename: "internal-pdf-viewer",
                        length: 1,
                        name: "Chrome PDF Plugin"
                    }},
                    {{
                        0: {{ type: "application/pdf", suffixes: "pdf", description: "Portable Document Format" }},
                        description: "Portable Document Format",
                        filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                        length: 1,
                        name: "Chrome PDF Viewer"
                    }}
                ],
                configurable: true
            }});
        }}
        """
