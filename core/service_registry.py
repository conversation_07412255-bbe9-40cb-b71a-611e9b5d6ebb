"""Service registry for email parsing and automation."""

from typing import Dict, Any, Optional, Type
import importlib
import logging

logger = logging.getLogger(__name__)


class ServiceRegistry:
    """Registry for service modules that can parse emails and handle automation."""

    _services = {
        "assemblyai": "services.assemblyai.AssemblyAIService",
        "exaai": "services.exaai.ExaAIService",
        "firecrawl": "services.firecrawl.FirecrawlService",
    }

    @classmethod
    def get_service_class(cls, service_name: str) -> Optional[Type]:
        """Get service class by name."""
        if service_name not in cls._services:
            return None

        try:
            module_path, class_name = cls._services[service_name].rsplit(".", 1)
            module = importlib.import_module(module_path)
            return getattr(module, class_name)
        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to import service {service_name}: {e}")
            return None

    @classmethod
    def parse_email_data(
        cls, service_name: str, email_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Parse email data using the appropriate service module.

        This method can be used by both webhook server and automation scripts
        to parse emails from KV or webhook data.
        """
        service_class = cls.get_service_class(service_name)
        if not service_class:
            logger.warning(f"No service found for {service_name}")
            return email_data

        # Check if service has email parsing capability
        if hasattr(service_class, "parse_email_data"):
            try:
                return service_class.parse_email_data(email_data)
            except Exception as e:
                logger.error(f"Error parsing email for {service_name}: {e}")
                return email_data
        else:
            logger.warning(f"Service {service_name} does not support email parsing")
            return email_data

    @classmethod
    def parse_magic_link(
        cls, service_name: str, raw_email_content: str
    ) -> Optional[str]:
        """Parse magic link using the appropriate service module.

        This method can be used by automation scripts to parse magic links
        from KV data or raw email content.
        """
        service_class = cls.get_service_class(service_name)
        if not service_class:
            logger.warning(f"No service found for {service_name}")
            return None

        # Check if service has magic link parsing capability
        if hasattr(service_class, "parse_magic_link"):
            try:
                return service_class.parse_magic_link(raw_email_content)
            except Exception as e:
                logger.error(f"Error parsing magic link for {service_name}: {e}")
                return None
        else:
            logger.warning(
                f"Service {service_name} does not support magic link parsing"
            )
            return None

    @classmethod
    def get_supported_services(cls) -> list:
        """Get list of supported service names."""
        return list(cls._services.keys())
