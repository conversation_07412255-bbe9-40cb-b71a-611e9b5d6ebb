"""Simple HTTP client to retrieve raw email content from webhook server."""

import httpx
from typing import Optional, Dict, Any
import logging
from urllib.parse import quote

logger = logging.getLogger(__name__)

# Suppress httpx INFO logs to reduce noise
logging.getLogger("httpx").setLevel(logging.WARNING)


class WebhookClient:
    """Client for retrieving raw email content from webhook server."""

    def __init__(self, webhook_url: str = "http://localhost:8888"):
        self.webhook_url = webhook_url.rstrip("/")

    async def get_latest_email_content(self, service: str, email: str) -> Optional[str]:
        """Get raw email content for a specific email address."""
        async with httpx.AsyncClient() as client:
            try:
                # URL encode the email properly
                encoded_email = quote(email, safe="@")
                response = await client.get(
                    f"{self.webhook_url}/email-data/{service}/{encoded_email}"
                )

                if response.status_code == 200:
                    data = response.json()
                    raw_content = data.get("raw", "")
                    logger.info(
                        f"Retrieved raw email content for {service}/{email}: {len(raw_content)} characters"
                    )
                    return raw_content
                else:
                    logger.warning(
                        f"No raw email content found for {service}/{email} (status: {response.status_code})"
                    )
                    return None

            except Exception as e:
                logger.error(f"Error retrieving raw email content: {e}")
                return None

    async def get_latest_email_data(
        self, service: str, email: str
    ) -> Optional[Dict[str, Any]]:
        """Get complete email data for a specific email address."""
        async with httpx.AsyncClient() as client:
            try:
                # URL encode the email properly
                encoded_email = quote(email, safe="@")
                response = await client.get(
                    f"{self.webhook_url}/email-data/{service}/{encoded_email}"
                )

                if response.status_code == 200:
                    email_data = response.json()
                    logger.info(f"Retrieved email data for {service}/{email}")
                    return email_data
                else:
                    logger.warning(
                        f"No email data found for {service}/{email} (status: {response.status_code})"
                    )
                    return None

            except Exception as e:
                logger.error(f"Error retrieving email data: {e}")
                return None

    async def clear_emails(self, service: str, email: str) -> bool:
        """Clear stored emails for a specific email address."""
        async with httpx.AsyncClient() as client:
            try:
                # URL encode the email properly
                encoded_email = quote(email, safe="@")
                response = await client.delete(
                    f"{self.webhook_url}/emails/{service}/{encoded_email}"
                )
                return response.status_code == 200
            except Exception as e:
                logger.error(f"Error clearing emails: {e}")
                return False
