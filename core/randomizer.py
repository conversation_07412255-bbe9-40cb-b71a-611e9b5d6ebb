"""Centralized randomization module for lightweight browser automation."""

import random
import asyncio
from typing import Dict, Any, Optional, Tuple
from pydantic import BaseModel, Field
from enum import Enum


class RandomizationProfile(str, Enum):
    """Predefined behavioral profiles for different automation styles."""

    CAUTIOUS = "cautious"  # Slower, more careful behavior
    NORMAL = "normal"  # Balanced behavior
    IMPATIENT = "impatient"  # Faster, more aggressive behavior
    TESTING = "testing"  # Minimal delays for development


class RandomizerConfig(BaseModel):
    """Configuration for the centralized randomizer."""

    # Global settings
    enabled: bool = True
    profile: RandomizationProfile = RandomizationProfile.NORMAL
    global_multiplier: float = 1.0  # Scale all delays by this factor

    # Typing behavior (in seconds)
    typing_delay_mean: float = 0.12
    typing_delay_stddev: float = 0.04
    typo_probability: float = 0.02

    # Essential click behavior (lightweight, required for service functionality)
    pre_click_delay_range: Tuple[float, float] = (0.1, 0.3)
    post_click_delay_range: Tuple[float, float] = (0.2, 0.5)

    # Essential page interactions (lightweight, required for service functionality)
    page_load_delay_range: Tuple[float, float] = (2.0, 4.0)
    user_action_delay_range: Tuple[float, float] = (0.5, 2.0)
    short_delay_range: Tuple[float, float] = (0.5, 1.5)
    medium_delay_range: Tuple[float, float] = (3.0, 5.0)
    long_delay_range: Tuple[float, float] = (10.0, 20.0)

    # Essential workflow delays (required for service functionality)
    workflow_delays: Dict[str, Tuple[float, float]] = Field(
        default_factory=lambda: {
            "after_email_entry": (0.5, 1.5),
            "after_navigation": (3.0, 5.0),
            "session_ending": (10.0, 20.0),
            "search_completion": (10.0, 15.0),
            "verification_wait": (2.0, 5.0),
        }
    )

    # Basic scroll behavior (lightweight)
    scroll_amount_range: Tuple[int, int] = (-300, 300)
    scroll_pause_range: Tuple[float, float] = (0.5, 1.5)


class Randomizer:
    """Centralized randomization engine for lightweight browser automation."""

    def __init__(self, config: Optional[RandomizerConfig] = None):
        """Initialize the randomizer with configuration."""
        self.config = config or RandomizerConfig()
        self._apply_profile()

    def _apply_profile(self):
        """Apply behavioral profile adjustments to base configuration."""
        profile_multipliers = {
            RandomizationProfile.CAUTIOUS: 1.5,
            RandomizationProfile.NORMAL: 1.0,
            RandomizationProfile.IMPATIENT: 0.6,
            RandomizationProfile.TESTING: 0.1,
        }

        multiplier = profile_multipliers.get(self.config.profile, 1.0)
        self.config.global_multiplier *= multiplier

    def _apply_global_multiplier(self, value: float) -> float:
        """Apply global multiplier to a delay value."""
        if not self.config.enabled:
            return 0.0
        return value * self.config.global_multiplier

    def _random_from_range(self, range_tuple: Tuple[float, float]) -> float:
        """Get random value from a range with global multiplier applied."""
        min_val, max_val = range_tuple
        raw_value = random.uniform(min_val, max_val)
        return self._apply_global_multiplier(raw_value)

    def _gaussian_value(
        self, mean: float, stddev: float, min_val: float = 0.0
    ) -> float:
        """Get gaussian-distributed value with global multiplier applied."""
        raw_value = max(min_val, random.gauss(mean, stddev))
        return self._apply_global_multiplier(raw_value)

    # === Typing and Input Delays ===

    def get_typing_delay(self) -> float:
        """Get realistic typing delay between keystrokes."""
        return self._gaussian_value(
            self.config.typing_delay_mean,
            self.config.typing_delay_stddev,
            min_val=0.02,  # Minimum 20ms
        )

    def should_make_typo(self) -> bool:
        """Determine if a typo should be made."""
        return self.config.enabled and random.random() < self.config.typo_probability

    def get_typo_correction_delay(self) -> float:
        """Get delay for typo correction (backspace + retype)."""
        return self._random_from_range((0.1, 0.3))

    # === Mouse and Click Delays ===

    # Removed complex mouse movement methods for performance

    def get_pre_click_delay(self) -> float:
        """Get delay before clicking."""
        return self._random_from_range(self.config.pre_click_delay_range)

    def get_post_click_delay(self) -> float:
        """Get delay after clicking."""
        return self._random_from_range(self.config.post_click_delay_range)

    # === Page Interaction Delays ===

    def get_page_load_delay(self) -> float:
        """Get delay for page loading simulation."""
        return self._random_from_range(self.config.page_load_delay_range)

    def get_user_action_delay(self) -> float:
        """Get delay between user actions."""
        return self._random_from_range(self.config.user_action_delay_range)

    def get_short_delay(self) -> float:
        """Get short delay (0.5-1.5s)."""
        return self._random_from_range(self.config.short_delay_range)

    def get_medium_delay(self) -> float:
        """Get medium delay (3-5s)."""
        return self._random_from_range(self.config.medium_delay_range)

    def get_long_delay(self) -> float:
        """Get long delay (10-20s)."""
        return self._random_from_range(self.config.long_delay_range)

    # === Workflow-Specific Delays ===

    def get_workflow_delay(self, workflow_step: str) -> float:
        """Get delay for specific workflow step."""
        if workflow_step in self.config.workflow_delays:
            return self._random_from_range(self.config.workflow_delays[workflow_step])
        else:
            # Fallback to user action delay
            return self.get_user_action_delay()

    # === Scroll Behavior ===

    def get_scroll_amount(self) -> int:
        """Get random scroll amount."""
        if not self.config.enabled:
            return 0
        return random.randint(*self.config.scroll_amount_range)

    def get_scroll_pause(self) -> float:
        """Get pause after scrolling."""
        return self._random_from_range(self.config.scroll_pause_range)

    # Removed complex reading scroll configuration for simplicity

    # === Basic Random Operations ===

    def choice(self, sequence):
        """Choose a random element from a sequence."""
        return random.choice(sequence)

    def choices(self, sequence, k=1):
        """Choose k random elements from a sequence with replacement."""
        return random.choices(sequence, k=k)

    def shuffle(self, sequence):
        """Shuffle a sequence in place."""
        return random.shuffle(sequence)

    def randint(self, a: int, b: int) -> int:
        """Return random integer between a and b (inclusive)."""
        return random.randint(a, b)

    def uniform(self, a: float, b: float) -> float:
        """Return random float between a and b."""
        return random.uniform(a, b)

    # === Async Convenience Methods ===

    async def delay(self, min_seconds: float, max_seconds: float):
        """Async sleep for a random delay in the given range."""
        delay_time = self.uniform(min_seconds, max_seconds)
        await asyncio.sleep(self._apply_global_multiplier(delay_time))

    async def sleep_typing_delay(self):
        """Async sleep for typing delay."""
        await asyncio.sleep(self.get_typing_delay())

    async def sleep_user_action_delay(self):
        """Async sleep for user action delay."""
        await asyncio.sleep(self.get_user_action_delay())

    async def sleep_workflow_delay(self, workflow_step: str):
        """Async sleep for workflow-specific delay."""
        await asyncio.sleep(self.get_workflow_delay(workflow_step))

    async def short_delay(self):
        """Async sleep for short delay."""
        await asyncio.sleep(self.get_short_delay())

    async def medium_delay(self):
        """Async sleep for medium delay."""
        await asyncio.sleep(self.get_medium_delay())

    async def long_delay(self):
        """Async sleep for long delay."""
        await asyncio.sleep(self.get_long_delay())

    async def page_load_delay(self):
        """Async sleep for page load delay."""
        await asyncio.sleep(self.get_page_load_delay())

    async def user_action_delay(self):
        """Async sleep for user action delay."""
        await asyncio.sleep(self.get_user_action_delay())

    # === High-Level Interaction Methods ===

    async def human_type(self, element, text: str):
        """Type text with human-like delays and errors."""
        await element.clear()

        for i, char in enumerate(text):
            # Random typing delay
            await asyncio.sleep(self.get_typing_delay())

            # Simulate occasional typos
            if self.should_make_typo() and i < len(text) - 1:
                # Type wrong character then correct it
                wrong_chars = "qwertyuiopasdfghjklzxcvbnm"
                wrong_char = self.choice(wrong_chars)
                await element.type(wrong_char)
                await asyncio.sleep(self.get_typo_correction_delay())
                await element.press("Backspace")
                await asyncio.sleep(self.get_typo_correction_delay())

            await element.type(char)

        # Random pause after typing
        await asyncio.sleep(self.uniform(0.2, 0.8))

    async def human_click(self, page, element):
        """Perform simple click with basic delays (lightweight implementation)."""
        if not self.config.enabled:
            await element.click()
            return

        # Simple pre-click delay (essential for service functionality)
        await asyncio.sleep(self.get_pre_click_delay())

        # Standard click without complex mouse movement
        await element.click()

        # Simple post-click delay (essential for service functionality)
        await asyncio.sleep(self.get_post_click_delay())

    # Removed complex mouse movement method for performance and simplicity

    async def random_scroll(self, page):
        """Perform random scrolling to simulate reading."""
        if not self.config.enabled:
            return

        try:
            # Random scroll amount and direction
            scroll_amount = self.get_scroll_amount()
            await page.evaluate(f"window.scrollBy(0, {scroll_amount})")
            await asyncio.sleep(self.get_scroll_pause())
        except Exception:
            pass  # Ignore scroll errors

    async def simulate_reading_behavior(self, page):
        """Simple reading behavior simulation (lightweight implementation)."""
        if not self.config.enabled:
            return

        try:
            # Simple delay to simulate page reading (minimal overhead)
            await asyncio.sleep(self.uniform(0.5, 1.5))
        except Exception:
            pass  # Ignore any errors

    async def sleep_short_delay(self):
        """Async sleep for short delay."""
        await asyncio.sleep(self.get_short_delay())

    async def sleep_medium_delay(self):
        """Async sleep for medium delay."""
        await asyncio.sleep(self.get_medium_delay())

    async def sleep_long_delay(self):
        """Async sleep for long delay."""
        await asyncio.sleep(self.get_long_delay())


# === Factory Functions ===


def create_randomizer(
    profile: RandomizationProfile = RandomizationProfile.NORMAL, enabled: bool = True
) -> Randomizer:
    """Create a randomizer with the specified profile."""
    config = RandomizerConfig(profile=profile, enabled=enabled)
    return Randomizer(config)


def create_testing_randomizer() -> Randomizer:
    """Create a randomizer optimized for testing (minimal delays)."""
    return create_randomizer(RandomizationProfile.TESTING, enabled=True)


def create_disabled_randomizer() -> Randomizer:
    """Create a randomizer with all delays disabled."""
    return create_randomizer(enabled=False)
