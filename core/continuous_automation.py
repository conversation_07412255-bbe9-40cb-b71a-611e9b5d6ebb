"""Continuous automation system for RegBot with randomization and session management."""

import asyncio
import random
import time
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable
from pydantic import BaseModel, Field
from enum import Enum

from core.config import Config
from core.results_storage import ResultsStorage

logger = logging.getLogger(__name__)


class SessionStatus(str, Enum):
    """Session status enumeration."""

    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"


class ContinuousAutomationConfig(BaseModel):
    """Configuration for continuous automation."""

    # Session interval settings (in minutes)
    min_interval_minutes: int = 30
    max_interval_minutes: int = 90

    # Goal settings
    target_registrations: int = 10
    max_failed_attempts: int = 3

    # Session persistence - saves progress to resume later if interrupted
    session_persistence_enabled: bool = True

    # Keep some essential settings for functionality
    page_delay_min_seconds: int = 2
    page_delay_max_seconds: int = 8
    max_session_duration_hours: int = 12
    enable_graceful_shutdown: bool = True


class SessionState(BaseModel):
    """Current state of an automation session."""

    session_id: str
    service_name: str
    status: SessionStatus = SessionStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    # Progress tracking
    total_attempts: int = 0
    successful_registrations: int = 0
    failed_attempts: int = 0
    target_registrations: int = 10

    # Current session info
    current_iteration: int = 0
    next_run_time: Optional[datetime] = None
    last_run_time: Optional[datetime] = None

    # Error tracking
    consecutive_failures: int = 0
    last_error: Optional[str] = None

    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ContinuousAutomationManager:
    """Manager for continuous automation sessions with randomization and human-like behavior."""

    def __init__(self, config: Config, automation_config: ContinuousAutomationConfig):
        self.config = config
        self.automation_config = automation_config
        self.storage = ResultsStorage()
        self.current_session: Optional[SessionState] = None
        self.is_running = False
        self.shutdown_requested = False

        # Session persistence
        self.session_file = Path(config.data_path) / "sessions" / "current_session.json"
        self.session_file.parent.mkdir(parents=True, exist_ok=True)

        # User agent pool for rotation
        self.user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
        ]

        # Viewport sizes for randomization
        self.viewport_sizes = [
            (1920, 1080),
            (1366, 768),
            (1440, 900),
            (1536, 864),
            (1280, 720),
        ]

    def create_session(
        self, service_name: str, target_registrations: int = None
    ) -> SessionState:
        """Create a new automation session."""
        session_id = f"{service_name}_{int(time.time())}"
        target = target_registrations or self.automation_config.target_registrations

        session = SessionState(
            session_id=session_id,
            service_name=service_name,
            target_registrations=target,
            next_run_time=datetime.now(),
        )

        self.current_session = session
        self._save_session()

        logger.info(
            f"Created new session {session_id} for {service_name} with target {target} registrations"
        )
        return session

    def load_session(self) -> Optional[SessionState]:
        """Load existing session from persistence."""
        if not self.session_file.exists():
            return None

        try:
            with open(self.session_file, "r") as f:
                data = json.load(f)
                session = SessionState(**data)

                # Check if session is still valid
                if session.status in [SessionStatus.COMPLETED, SessionStatus.FAILED]:
                    logger.info(
                        f"Session {session.session_id} already {session.status}, not loading"
                    )
                    return None

                # Check session age
                max_age = timedelta(
                    hours=self.automation_config.max_session_duration_hours
                )
                if datetime.now() - session.created_at > max_age:
                    logger.info(f"Session {session.session_id} too old, not loading")
                    return None

                self.current_session = session
                logger.info(f"Loaded existing session {session.session_id}")
                return session

        except Exception as e:
            logger.error(f"Error loading session: {e}")
            return None

    def _save_session(self):
        """Save current session to persistence."""
        if (
            not self.current_session
            or not self.automation_config.session_persistence_enabled
        ):
            return

        try:
            with open(self.session_file, "w") as f:
                json.dump(
                    self.current_session.model_dump(mode="json"),
                    f,
                    indent=2,
                    default=str,
                )
        except Exception as e:
            logger.error(f"Error saving session: {e}")

    def _calculate_next_run_time(self) -> datetime:
        """Calculate next run time with randomization."""
        min_seconds = self.automation_config.min_interval_minutes * 60
        max_seconds = self.automation_config.max_interval_minutes * 60

        # Add some additional randomization (±10%)
        variance = random.uniform(0.9, 1.1)
        interval_seconds = random.randint(min_seconds, max_seconds) * variance

        return datetime.now() + timedelta(seconds=interval_seconds)

    def _get_random_page_delay(self) -> float:
        """Get random page delay in seconds."""
        return random.uniform(
            self.automation_config.page_delay_min_seconds,
            self.automation_config.page_delay_max_seconds,
        )

    def _get_random_typing_delay(self) -> int:
        """Get random typing delay in milliseconds."""
        return random.randint(
            self.automation_config.typing_delay_min_ms,
            self.automation_config.typing_delay_max_ms,
        )

    def _get_random_user_agent(self) -> str:
        """Get random user agent for browser automation."""
        if self.automation_config.enable_user_agent_rotation:
            return random.choice(self.user_agents)
        return self.user_agents[0]  # Default to first one

    def _get_random_viewport(self) -> tuple:
        """Get random viewport size."""
        if self.automation_config.enable_viewport_randomization:
            return random.choice(self.viewport_sizes)
        return self.viewport_sizes[0]  # Default to first one

    async def _simulate_human_behavior(self, browser_manager):
        """Simulate human-like behavior during automation."""
        if not self.automation_config.enable_mouse_movements:
            return

        try:
            # Random mouse movement
            page = browser_manager.page
            viewport = await page.evaluate(
                "() => ({ width: window.innerWidth, height: window.innerHeight })"
            )

            # Move mouse to random position
            random_x = random.randint(100, viewport["width"] - 100)
            random_y = random.randint(100, viewport["height"] - 100)
            await page.mouse.move(random_x, random_y)

            # Random scroll if enabled
            if self.automation_config.enable_scroll_simulation:
                scroll_amount = random.randint(-200, 200)
                await page.evaluate(f"window.scrollBy(0, {scroll_amount})")

            # Random small delay
            await asyncio.sleep(random.uniform(0.5, 1.5))

        except Exception as e:
            logger.debug(f"Error in human behavior simulation: {e}")

    async def _run_single_registration(self, service_name: str) -> bool:
        """Run a single registration attempt with randomization."""
        try:
            logger.info(
                f"Starting registration attempt {self.current_session.current_iteration + 1}"
            )

            # Add random delay before starting
            pre_delay = self._get_random_page_delay()
            logger.info(f"Random pre-start delay: {pre_delay:.2f} seconds")
            await asyncio.sleep(pre_delay)

            # Use the same service registration logic as main.py (single source of truth)
            from main import register_service
            
            success = await register_service(service_name, self.config, self.storage, debug_mode=False)
            
            if success:
                logger.info(f"Registration successful for {service_name}")
                return True
            else:
                logger.error(f"Registration failed for {service_name}")
                return False

        except Exception as e:
            logger.error(f"Error during registration: {e}")
            return False

    async def run_continuous_automation(
        self, service_name: str, target_registrations: int = None
    ) -> SessionState:
        """Run continuous automation until goal is reached or max failures exceeded."""

        # Load or create session
        session = self.load_session()
        if not session:
            session = self.create_session(service_name, target_registrations)

        self.current_session = session
        self.current_session.status = SessionStatus.RUNNING
        self.current_session.started_at = datetime.now()
        self.is_running = True

        logger.info(f"Starting continuous automation for {service_name}")
        logger.info(f"Target: {session.target_registrations} registrations")
        logger.info(
            f"Current progress: {session.successful_registrations}/{session.target_registrations}"
        )

        try:
            while (
                self.is_running
                and not self.shutdown_requested
                and session.successful_registrations < session.target_registrations
                and session.consecutive_failures
                < self.automation_config.max_failed_attempts
            ):
                # Check if it's time to run
                now = datetime.now()
                if session.next_run_time and now < session.next_run_time:
                    sleep_seconds = (session.next_run_time - now).total_seconds()
                    logger.info(
                        f"Waiting {sleep_seconds:.1f} seconds until next run..."
                    )

                    # Sleep in small increments to allow for graceful shutdown
                    while sleep_seconds > 0 and not self.shutdown_requested:
                        sleep_time = min(
                            60, sleep_seconds
                        )  # Sleep max 1 minute at a time
                        await asyncio.sleep(sleep_time)
                        sleep_seconds -= sleep_time

                    if self.shutdown_requested:
                        break

                # Update session state
                session.current_iteration += 1
                session.total_attempts += 1
                session.last_run_time = datetime.now()

                logger.info(f"Running iteration {session.current_iteration}")

                # Run registration attempt
                success = await self._run_single_registration(service_name)

                if success:
                    session.successful_registrations += 1
                    session.consecutive_failures = 0
                    session.last_error = None
                    logger.info(
                        f"Success! Progress: {session.successful_registrations}/{session.target_registrations}"
                    )
                else:
                    session.failed_attempts += 1
                    session.consecutive_failures += 1
                    logger.warning(
                        f"Failed attempt. Consecutive failures: {session.consecutive_failures}"
                    )

                # Save progress
                self._save_session()

                # Calculate next run time if not completed
                if session.successful_registrations < session.target_registrations:
                    session.next_run_time = self._calculate_next_run_time()
                    interval_minutes = (
                        session.next_run_time - datetime.now()
                    ).total_seconds() / 60
                    logger.info(f"Next run scheduled in {interval_minutes:.1f} minutes")

            # Determine final status
            if session.successful_registrations >= session.target_registrations:
                session.status = SessionStatus.COMPLETED
                logger.info(
                    f"🎉 Goal achieved! {session.successful_registrations} successful registrations"
                )
            elif (
                session.consecutive_failures
                >= self.automation_config.max_failed_attempts
            ):
                session.status = SessionStatus.FAILED
                logger.error(
                    f"❌ Max consecutive failures reached ({session.consecutive_failures})"
                )
            elif self.shutdown_requested:
                session.status = SessionStatus.PAUSED
                logger.info("⏸️ Session paused due to shutdown request")

            session.completed_at = datetime.now()
            self._save_session()

            return session

        except Exception as e:
            session.status = SessionStatus.FAILED
            session.last_error = str(e)
            session.completed_at = datetime.now()
            self._save_session()
            logger.error(f"Critical error in continuous automation: {e}")
            raise

        finally:
            self.is_running = False

    def request_shutdown(self):
        """Request graceful shutdown of continuous automation."""
        logger.info("Graceful shutdown requested")
        self.shutdown_requested = True

    def get_session_summary(self) -> Dict[str, Any]:
        """Get summary of current session."""
        if not self.current_session:
            return {"error": "No active session"}

        session = self.current_session
        duration = datetime.now() - session.created_at

        return {
            "session_id": session.session_id,
            "service": session.service_name,
            "status": session.status,
            "progress": f"{session.successful_registrations}/{session.target_registrations}",
            "success_rate": f"{(session.successful_registrations / max(1, session.total_attempts)) * 100:.1f}%",
            "total_attempts": session.total_attempts,
            "failed_attempts": session.failed_attempts,
            "consecutive_failures": session.consecutive_failures,
            "duration": str(duration).split(".")[0],  # Remove microseconds
            "next_run": session.next_run_time.isoformat()
            if session.next_run_time
            else None,
            "last_error": session.last_error,
        }
