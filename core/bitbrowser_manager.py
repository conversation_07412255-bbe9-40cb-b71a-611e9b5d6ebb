"""BitBrowser Local API integration manager."""

import asyncio
import logging
import requests
import json
from typing import Optional, Dict, Any, List

logger = logging.getLogger(__name__)


class BitBrowserManager:
    """Manager for BitBrowser Local API integration."""

    def __init__(
        self,
        browser_id: str = "bit",
        api_url: str = "http://127.0.0.1:54345",
        credentials: Dict[str, Any] = None,
    ):
        self.browser_id = browser_id
        self.api_url = api_url
        self.credentials = credentials or {}
        self.headers = {"Content-Type": "application/json"}

    def open_browser(self, headless: bool = False, service_name: str = None) -> Dict[str, Any]:
        """Open BitBrowser instance with optional headless mode.

        Args:
            headless: Whether to open in headless mode
            service_name: Name of the service (for service-specific configurations)

        Returns:
            Dict containing success status and WebSocket endpoint
        """
        logger.info(f"🌐 Opening BitBrowser instance: {self.browser_id}")

        json_data = {"id": f"{self.browser_id}"}

        # Add headless arguments if requested
        if headless:
            logger.info("🔇 Opening BitBrowser in headless mode")
            
            # Service-specific headless configurations
            if service_name == "assemblyai":
                # Minimal headless flags for AssemblyAI to avoid anti-bot detection
                logger.info("🎯 Using AssemblyAI-optimized headless configuration")
                json_data["args"] = [
                    "--headless=new",
                    "--no-sandbox",  # Required for headless operation
                    "--disable-dev-shm-usage",  # Required for Docker/CI environments
                    # Removed aggressive flags that trigger AssemblyAI's anti-bot detection
                ]
            else:
                # Default headless configuration for other services (including FireCrawl)
                json_data["args"] = [
                    "--headless=new",
                    "--no-sandbox",
                    "--disable-gpu",
                    "--disable-dev-shm-usage",
                    "--disable-password-manager",
                    "--disable-save-password-bubble",
                    "--disable-web-security",
                    "--disable-features=TranslateUI",
                    "--disable-extensions",
                    "--disable-plugins",
                ]

        try:
            response = requests.post(
                f"{self.api_url}/browser/open",
                data=json.dumps(json_data),
                headers=self.headers,
                timeout=30,
            )
            response.raise_for_status()
            result = response.json()

            if result.get("success"):
                ws_endpoint = result["data"]["ws"]
                logger.info(f"✅ BitBrowser opened successfully")
                logger.info(f"🔗 WebSocket endpoint: {ws_endpoint}")
                return {
                    "success": True,
                    "ws_endpoint": ws_endpoint,
                    "data": result["data"],
                }
            else:
                error_msg = result.get("msg", "Unknown error")
                logger.error(f"❌ Failed to open BitBrowser: {error_msg}")
                return {"success": False, "error": error_msg}

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Network error opening BitBrowser: {e}")
            return {"success": False, "error": f"Network error: {e}"}
        except Exception as e:
            logger.error(f"❌ Unexpected error opening BitBrowser: {e}")
            return {"success": False, "error": f"Unexpected error: {e}"}

    def close_browser(self) -> bool:
        """Close BitBrowser instance.

        Returns:
            True if successful, False otherwise
        """
        logger.info(f"🔒 Closing BitBrowser instance: {self.browser_id}")

        json_data = {"id": f"{self.browser_id}"}

        try:
            response = requests.post(
                f"{self.api_url}/browser/close",
                data=json.dumps(json_data),
                headers=self.headers,
                timeout=15,
            )
            response.raise_for_status()
            result = response.json()

            if result.get("success"):
                logger.info("✅ BitBrowser closed successfully")
                return True
            else:
                error_msg = result.get("msg", "Unknown error")
                logger.warning(f"⚠️ Error closing BitBrowser: {error_msg}")
                return False

        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ Network error closing BitBrowser: {e}")
            return False
        except Exception as e:
            logger.warning(f"⚠️ Unexpected error closing BitBrowser: {e}")
            return False

    def get_browser_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the browser instance.

        Returns:
            Browser info dict or None if failed
        """
        json_data = {"id": f"{self.browser_id}"}

        try:
            response = requests.post(
                f"{self.api_url}/browser/status",
                data=json.dumps(json_data),
                headers=self.headers,
                timeout=10,
            )
            response.raise_for_status()
            result = response.json()

            if result.get("success"):
                return result.get("data")
            else:
                logger.warning(
                    f"⚠️ Failed to get browser info: {result.get('msg', 'Unknown error')}"
                )
                return None

        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ Network error getting browser info: {e}")
            return None
        except Exception as e:
            logger.warning(f"⚠️ Error getting browser info: {e}")
            return None

    def list_browsers(self) -> Optional[List[Dict[str, Any]]]:
        """List all available browser instances.

        Returns:
            List of browser info dicts or None if failed
        """
        try:
            response = requests.get(
                f"{self.api_url}/browser/list", headers=self.headers, timeout=10
            )
            response.raise_for_status()
            result = response.json()

            if result.get("success"):
                return result.get("data", [])
            else:
                logger.warning(
                    f"⚠️ Failed to list browsers: {result.get('msg', 'Unknown error')}"
                )
                return None

        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ Network error listing browsers: {e}")
            return None
        except Exception as e:
            logger.warning(f"⚠️ Error listing browsers: {e}")
            return None

    def is_browser_running(self) -> bool:
        """Check if the browser instance is currently running.

        Returns:
            True if running, False otherwise
        """
        browser_info = self.get_browser_info()
        if browser_info:
            status = browser_info.get("status", "").lower()
            return status in ["active", "running", "open"]
        return False

    async def ensure_browser_closed(self):
        """Ensure browser is properly closed, with retry logic."""
        max_attempts = 3
        for attempt in range(max_attempts):
            if not self.is_browser_running():
                logger.info("✅ Browser is not running")
                return True

            logger.info(f"🔄 Attempt {attempt + 1}/{max_attempts}: Closing browser")
            success = self.close_browser()

            if success:
                # Wait a bit and check again
                await asyncio.sleep(2)
                if not self.is_browser_running():
                    logger.info("✅ Browser successfully closed")
                    return True

            # Wait before next attempt
            if attempt < max_attempts - 1:
                await asyncio.sleep(3)

        logger.warning("⚠️ Could not ensure browser is closed after all attempts")
        return False


class BitBrowserError(Exception):
    """Custom exception for BitBrowser operations."""

    pass


# Legacy functions for backward compatibility with existing test scripts
def openBrowser(browser_id: str) -> Dict[str, Any]:
    """Legacy function for opening BitBrowser (non-headless)."""
    manager = BitBrowserManager(browser_id=browser_id)
    result = manager.open_browser(headless=False)

    # Convert to legacy format
    if result["success"]:
        return {"success": True, "data": {"ws": result["ws_endpoint"]}}
    else:
        return {"success": False, "msg": result.get("error", "Unknown error")}


def openBrowserHeadless(browser_id: str) -> Dict[str, Any]:
    """Legacy function for opening BitBrowser in headless mode."""
    manager = BitBrowserManager(browser_id=browser_id)
    result = manager.open_browser(headless=True)

    # Convert to legacy format
    if result["success"]:
        return {"success": True, "data": {"ws": result["ws_endpoint"]}}
    else:
        return {"success": False, "msg": result.get("error", "Unknown error")}


def closeBrowser(browser_id: str) -> Dict[str, Any]:
    """Legacy function for closing BitBrowser."""
    manager = BitBrowserManager(browser_id=browser_id)
    success = manager.close_browser()

    return {
        "success": success,
        "msg": "Browser closed" if success else "Failed to close browser",
    }
