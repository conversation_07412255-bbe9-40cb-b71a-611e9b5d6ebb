import secrets
import string


def generate_strong_password(
    length_min: int = 14,
    length_max: int = 18,
    num_digits_min: int = 2,
    num_digits_max: int = 4,
    num_symbols_min: int = 1,
    num_symbols_max: int = 2,
    num_upper_min: int = 2,
    num_upper_max: int = 4,
) -> str:
    """
    Generates a strong, random password with specific character constraints.

    Args:
        length_min: Minimum password length (default: 14)
        length_max: Maximum password length (default: 18)
        num_digits_min: Minimum number of digits (default: 2)
        num_digits_max: Maximum number of digits (default: 4)
        num_symbols_min: Minimum number of symbols (default: 1)
        num_symbols_max: Maximum number of symbols (default: 2)
        num_upper_min: Minimum number of uppercase letters (default: 2)
        num_upper_max: Maximum number of uppercase letters (default: 4)

    Returns:
        A strong password meeting all constraints
    """
    length = secrets.randbelow(length_max - length_min + 1) + length_min
    num_digits = secrets.randbelow(num_digits_max - num_digits_min + 1) + num_digits_min
    num_symbols = (
        secrets.randbelow(num_symbols_max - num_symbols_min + 1) + num_symbols_min
    )
    num_upper = secrets.randbelow(num_upper_max - num_upper_min + 1) + num_upper_min

    # Ensure we have enough space for all required character types
    min_required = (
        num_digits + num_symbols + num_upper + 1
    )  # +1 for at least one lowercase
    if length < min_required:
        length = min_required

    # Calculate remaining lowercase letters
    num_lower = length - num_digits - num_symbols - num_upper

    # Ensure we have at least one lowercase letter
    if num_lower < 1:
        num_lower = 1
        length = num_digits + num_symbols + num_upper + num_lower

    password_list = (
        [secrets.choice(string.ascii_lowercase) for _ in range(num_lower)]
        + [secrets.choice(string.ascii_uppercase) for _ in range(num_upper)]
        + [secrets.choice(string.digits) for _ in range(num_digits)]
        + [secrets.choice(string.punctuation) for _ in range(num_symbols)]
    )

    # Ensure the list is exactly the desired length
    while len(password_list) < length:
        password_list.append(secrets.choice(string.ascii_lowercase))

    secrets.SystemRandom().shuffle(password_list)
    return "".join(password_list[:length])
