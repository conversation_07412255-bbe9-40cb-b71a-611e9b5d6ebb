
import yaml
import os

# Define the services and file paths
services = ["assemblyai", "exaai", "firecrawl"]
source_dir = "data/results-merged"
dest_dir = "data/results"

# Ensure the destination directory exists
os.makedirs(dest_dir, exist_ok=True)

for service in services:
    source_file = os.path.join(source_dir, f"{service}.yaml")
    dest_file = os.path.join(dest_dir, f"{service}.yaml")

    # Read the source YAML file
    if os.path.exists(source_file):
        with open(source_file, 'r') as f:
            source_data = yaml.safe_load(f) or {}
    else:
        print(f"Source file not found: {source_file}")
        continue

    # Read the destination YAML file, or create it if it doesn't exist
    if os.path.exists(dest_file):
        with open(dest_file, 'r') as f:
            dest_data = yaml.safe_load(f) or {}
    else:
        dest_data = {"api_keys": []}

    # Get the list of api_keys from both source and destination
    source_keys = source_data.get("api_keys", [])
    dest_keys = dest_data.get("api_keys", [])

    # Add new api_keys from source to destination
    for key_info in source_keys:
        if key_info not in dest_keys:
            dest_keys.append(key_info)

    # Update the destination data and write back to the file
    dest_data["api_keys"] = dest_keys
    with open(dest_file, 'w') as f:
        yaml.dump(dest_data, f, default_flow_style=False)

    print(f"Successfully merged {service}.yaml")

print("All services have been merged.")
