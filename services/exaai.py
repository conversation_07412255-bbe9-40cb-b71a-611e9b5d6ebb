"""ExaAI service automation."""

import asyncio
import re
from typing import Optional, Dict, Any, List
from datetime import datetime
from pathlib import Path
from playwright.async_api import Page
from core.browser import BrowserManager
from core.models import RegistrationResult, ServiceCredentials
from core.config import ServiceConfig, Config
from core.profile import ProfileGenerator
from core.email_client_factory import EmailClientFactory
from core.domain_selector import DomainSelector, EmailDomain
from core.randomizer import Randomizer, create_randomizer, RandomizationProfile
import logging

logger = logging.getLogger(__name__)


class ExaAIService:
    """Automate ExaAI registration and API key extraction."""

    def __init__(self, config: ServiceConfig, full_config: Config):
        self.config = config
        self.full_config = full_config
        self.debug_mode = False  # Debug mode disabled by default

        # Initialize centralized randomizer
        randomizer_config = getattr(full_config, "randomizer", None)
        if randomizer_config:
            self.randomizer = Randomizer(randomizer_config)
        else:
            # Use default randomizer configuration
            self.randomizer = create_randomizer(
                RandomizationProfile.NORMAL, enabled=True
            )

        # Get email domains for this service
        service_domains = full_config.get_domains_for_service("exaai")
        if not service_domains:
            raise ValueError("No email domains configured for ExaAI")

        # Initialize domain selector for load balancing
        self.domain_selector = DomainSelector(
            service_domains, randomizer=self.randomizer
        )

        # Select a domain for this registration
        self.selected_domain = self.domain_selector.select_domain()
        if not self.selected_domain:
            raise ValueError("No email domains configured")

        logger.info(f"Selected email domain: {self.selected_domain}")

        self.profile_generator = ProfileGenerator(
            self.selected_domain, randomizer=self.randomizer
        )
        self.browser_manager: Optional[BrowserManager] = None
        self.profile = None

        # Initialize email client based on configuration
        self.email_client = EmailClientFactory.create_client()

    def _get_headless_setting(self) -> Optional[bool]:
        """Get headless setting from service config with default to global config."""
        if self.config.headless is not None:
            return self.config.headless
        # Don't override - let BrowserManager handle global engine configs
        return None

    def _get_browser_engines(self) -> List[str]:
        """Get browser engines list from service config or fall back to global default."""
        if self.config.browser_engines is not None:
            return self.config.browser_engines
        return self.full_config.browser_engines

    async def _debug_screenshot(self, browser: BrowserManager, name: str):
        """Take a debug screenshot if debug mode is enabled."""
        if not self.debug_mode:
            return

        try:
            # Create debug directory
            debug_dir = (
                Path("debug") / "exaai" / datetime.now().strftime("%Y%m%d_%H%M%S")
            )
            debug_dir.mkdir(parents=True, exist_ok=True)

            # Take screenshot
            screenshot_path = debug_dir / f"{name}.png"
            await browser.page.screenshot(path=str(screenshot_path))
            logger.info(f"🐛 Debug screenshot saved: {screenshot_path}")
        except Exception as e:
            logger.warning(f"Failed to save debug screenshot: {e}")

    def _save_email_content(
        self, email_content: str, email_address: str, status: str = "wip"
    ):
        """Save email content to data directory with proper organization."""
        if not email_content:
            return None

        try:
            # Determine base data directory
            data_dir = Path("data") / status / "exaai" / "email"
            data_dir.mkdir(parents=True, exist_ok=True)

            # Save email content with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            email_filename = f"{timestamp}_{email_address.replace('@', '_')}.txt"
            email_path = data_dir / email_filename

            with open(email_path, "w", encoding="utf-8") as f:
                f.write(f"Email Address: {email_address}\n")
                f.write(f"Timestamp: {datetime.now().isoformat()}\n")
                f.write(f"Status: {status}\n")
                f.write("=" * 50 + "\n")
                f.write(email_content)

            logger.info(f"📧 Email content saved: {email_path}")
            return email_path
        except Exception as e:
            logger.warning(f"Failed to save email content: {e}")
            return None

    def _move_email_to_done(self, email_path: Path):
        """Move email from wip to done directory."""
        if not email_path or not email_path.exists():
            return

        try:
            done_dir = Path("data") / "done" / "exaai" / "email"
            done_dir.mkdir(parents=True, exist_ok=True)

            done_path = done_dir / email_path.name
            email_path.rename(done_path)
            logger.info(f"📧 Email moved to done: {done_path}")
        except Exception as e:
            logger.warning(f"Failed to move email to done: {e}")

    async def _debug_inspect_page(self, browser: BrowserManager, name: str):
        """Inspect page source and save DOM elements for debugging."""
        if not self.debug_mode:
            return

        try:
            # Create debug directory
            debug_dir = (
                Path("debug") / "exaai" / datetime.now().strftime("%Y%m%d_%H%M%S")
            )
            debug_dir.mkdir(parents=True, exist_ok=True)

            # Get page source
            page_source = await browser.page.content()
            source_path = debug_dir / f"{name}_page_source.html"
            with open(source_path, "w", encoding="utf-8") as f:
                f.write(page_source)

            # Get all input elements
            inputs = await browser.page.query_selector_all("input")
            input_info = []
            for i, input_elem in enumerate(inputs):
                try:
                    attrs = await input_elem.evaluate("""(element) => {
                        const attrs = {};
                        for (let attr of element.attributes) {
                            attrs[attr.name] = attr.value;
                        }
                        return attrs;
                    }""")
                    is_visible = await input_elem.is_visible()
                    input_info.append(
                        {"index": i, "attributes": attrs, "visible": is_visible}
                    )
                except:
                    pass

            # Get all button elements
            buttons = await browser.page.query_selector_all("button")
            button_info = []
            for i, button_elem in enumerate(buttons):
                try:
                    text = await button_elem.text_content()
                    attrs = await button_elem.evaluate("""(element) => {
                        const attrs = {};
                        for (let attr of element.attributes) {
                            attrs[attr.name] = attr.value;
                        }
                        return attrs;
                    }""")
                    is_visible = await button_elem.is_visible()
                    button_info.append(
                        {
                            "index": i,
                            "text": text,
                            "attributes": attrs,
                            "visible": is_visible,
                        }
                    )
                except:
                    pass

            # Save DOM analysis
            dom_path = debug_dir / f"{name}_dom_analysis.txt"
            with open(dom_path, "w", encoding="utf-8") as f:
                f.write(f"DOM Analysis for {name}\n")
                f.write(f"Timestamp: {datetime.now().isoformat()}\n")
                f.write("=" * 50 + "\n\n")

                f.write("INPUT ELEMENTS:\n")
                f.write("-" * 20 + "\n")
                for input_data in input_info:
                    f.write(f"Input {input_data['index']}:\n")
                    f.write(f"  Visible: {input_data['visible']}\n")
                    f.write(f"  Attributes: {input_data['attributes']}\n\n")

                f.write("\nBUTTON ELEMENTS:\n")
                f.write("-" * 20 + "\n")
                for button_data in button_info:
                    f.write(f"Button {button_data['index']}:\n")
                    f.write(f"  Text: '{button_data['text']}'\n")
                    f.write(f"  Visible: {button_data['visible']}\n")
                    f.write(f"  Attributes: {button_data['attributes']}\n\n")

            logger.info(f"🐛 Debug DOM analysis saved: {dom_path}")
            logger.info(f"🐛 Debug page source saved: {source_path}")

        except Exception as e:
            logger.warning(f"Failed to save debug page inspection: {e}")

    @staticmethod
    def parse_magic_link(raw_email_content: str) -> Optional[str]:
        """Parse ExaAI dashboard link from verification email content.

        This method can be used by both webhook server and automation scripts.
        ExaAI sends verification emails with a "continue to dashboard" link.
        """
        if not raw_email_content:
            return None

        # ExaAI sends emails with dashboard links
        # Look for various patterns that might contain the dashboard link

        # Strategy 1: Look for direct dashboard links in HTML
        html_patterns = [
            r'href=3D"(https://dashboard\.exa\.ai[^"]*)',
            r'href="(https://dashboard\.exa\.ai[^"]*)',
            r'href=\\?"(https://dashboard\.exa\.ai[^"]*)',
        ]

        for pattern in html_patterns:
            html_match = re.search(
                pattern, raw_email_content, re.DOTALL | re.IGNORECASE
            )
            if html_match:
                url = html_match.group(1)
                # Clean up quoted-printable encoding properly
                url = url.replace("=\r\n", "").replace("=\n", "")
                # Fix quoted-printable decoding: =3D should become =
                url = url.replace("=3D", "=")
                url = url.replace("&amp;", "&")
                logger.info(f"Extracted ExaAI dashboard link from HTML: {url[:50]}...")
                return url

        # Strategy 2: Look for plain text links
        text_patterns = [
            r"https://dashboard\.exa\.ai[^\s\)]*",
            r"dashboard\.exa\.ai[^\s\)]*",
        ]

        for pattern in text_patterns:
            text_match = re.search(pattern, raw_email_content, re.IGNORECASE)
            if text_match:
                url = text_match.group(0)
                if not url.startswith("https://"):
                    url = "https://" + url

                # Clean up any line breaks or encoding issues
                url = url.replace("=\r\n", "").replace("=\n", "")
                logger.info(f"Extracted ExaAI dashboard link from text: {url[:50]}...")
                return url

        logger.warning("No ExaAI dashboard link found in email content")
        return None

    @staticmethod
    def parse_email_data(email_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse email data and extract dashboard link for ExaAI.

        This method can be used by both webhook server and automation scripts.
        """
        raw_content = email_data.get("raw", "")
        if not raw_content:
            logger.warning("No raw content for ExaAI email parsing")
            return email_data

        # Parse dashboard link
        dashboard_link = ExaAIService.parse_magic_link(raw_content)
        if dashboard_link:
            email_data["dashboardLink"] = dashboard_link

        # Add parsing metadata
        email_data["parsed_by"] = "exaai_service"
        email_data["parser_version"] = "1.0"

        return email_data

    async def register_and_get_api_key(self) -> RegistrationResult:
        """Main workflow: go to login page, enter email, wait for verification email, then process it."""
        try:
            # Generate profile data
            self.profile = self.profile_generator.generate_profile()
            logger.info(f"Generated profile for {self.profile['email']}")

            async with BrowserManager(
                browser_engines=self._get_browser_engines(),
                global_engine_configs=self.full_config.browser_engines,
                service_headless_override=self._get_headless_setting(),
                randomizer=self.randomizer,  # Pass the centralized randomizer
            ) as browser:
                self.browser_manager = browser

                # Log selected browser engine for debugging
                logger.info(f"🌐 Selected browser engine: {browser.browser_engine}")

                # Flexible navigation: start_url (homepage) vs signup_url (direct)
                if self.config.start_url:
                    # Navigate to homepage first, then click Sign Up button (natural flow)
                    logger.info(f"Navigating to homepage: {self.config.start_url}")
                    await browser.goto(self.config.start_url)
                    await browser.wait(self.config.delay_times.get("page_load", 3000))
                    await self._debug_screenshot(browser, "01_homepage")

                    # Look for Sign Up / Get Started button
                    logger.info("🔍 Looking for 'Sign Up' or 'Get Started' button")
                    signup_nav_selectors = [
                        'a:has-text("Sign up")',
                        'a:has-text("Get started")', 
                        'button:has-text("Sign up")',
                        'button:has-text("Get started")',
                        '[href*="/signup"]',
                        '[href*="/register"]', 
                        '[href*="/dashboard"]',
                        'nav a:has-text("Sign up")',
                        'header a:has-text("Sign up")',
                    ]

                    button_found = False
                    for selector in signup_nav_selectors:
                        try:
                            signup_button = browser.page.locator(selector).first
                            if await signup_button.is_visible():
                                await signup_button.click()
                                logger.info(f"✅ Clicked Sign Up button with selector: {selector}")
                                button_found = True
                                await browser.wait(3000)  # Wait for signup page to load
                                break
                        except Exception as e:
                            logger.debug(f"Sign Up button selector {selector} failed: {e}")
                            continue

                    if not button_found:
                        if self.config.signup_url:
                            logger.info("⚠️ Could not find Sign Up button, falling back to direct signup URL")
                            await browser.goto(self.config.signup_url)
                            await browser.wait(self.config.delay_times.get("page_load", 3000))
                        else:
                            logger.error("❌ Could not find 'Sign Up' button and no signup_url configured")
                            return RegistrationResult(success=False, error_message="Could not find Sign Up button", service="exaai")

                elif self.config.signup_url:
                    # Direct navigation to signup page
                    logger.info(f"Navigating directly to signup page: {self.config.signup_url}")
                    await browser.goto(self.config.signup_url)
                    await browser.wait(self.config.delay_times.get("page_load", 3000))
                    await self._debug_screenshot(browser, "01_signup_page")

                else:
                    logger.error("❌ Neither start_url nor signup_url configured")
                    return RegistrationResult(success=False, error_message="No start_url or signup_url configured", service="exaai")

                # Handle any cookie consent popups
                try:
                    await browser.wait(2000)
                    # Look for common cookie consent selectors
                    cookie_selectors = [
                        "#onetrust-accept-btn-handler",
                        'button:has-text("Accept")',
                        'button:has-text("Accept All")',
                        '[data-testid*="accept"]',
                        ".cookie-accept",
                    ]

                    for selector in cookie_selectors:
                        try:
                            cookie_button = await browser.page.query_selector(selector)
                            if cookie_button and await cookie_button.is_visible():
                                await cookie_button.click()
                                logger.info(
                                    f"Clicked cookie consent button: {selector}"
                                )
                                await browser.wait(1000)
                                break
                        except:
                            continue
                except Exception as e:
                    logger.debug(f"Cookie handling: {e}")

                # Step 2: Enter email and submit
                logger.info(f"Entering email: {self.profile['email']}")
                await self._debug_screenshot(browser, "02_before_email_entry")

                # Find email input field - try multiple selectors
                email_selectors = [
                    'input[type="email"]',
                    'input[name="email"]',
                    'input[placeholder*="email" i]',
                    "#email",
                    '[data-testid*="email"]',
                ]

                email_input = None
                for selector in email_selectors:
                    try:
                        email_input = await browser.page.query_selector(selector)
                        if email_input and await email_input.is_visible():
                            break
                    except:
                        continue

                if not email_input:
                    return RegistrationResult(
                        service="exaai",
                        success=False,
                        error_message="Could not find email input field",
                    )

                await email_input.fill(self.profile["email"])
                await self._debug_screenshot(browser, "03_email_entered")

                # Add human-like delay
                await self.randomizer.user_action_delay()

                # Find and click "Continue With Email" button
                continue_selectors = [
                    'button:has-text("Continue With Email")',
                    'button:has-text("Continue with Email")',
                    'button:has-text("Continue")',
                    'input[type="submit"]',
                    'button[type="submit"]',
                    '[data-testid*="continue"]',
                    '[data-testid*="submit"]',
                ]

                continue_button = None
                for selector in continue_selectors:
                    try:
                        continue_button = await browser.page.query_selector(selector)
                        if continue_button and await continue_button.is_visible():
                            break
                    except:
                        continue

                if not continue_button:
                    return RegistrationResult(
                        service="exaai",
                        success=False,
                        error_message="Could not find continue button",
                    )

                await continue_button.click()
                logger.info("Email submitted, waiting for verification email...")
                await self._debug_screenshot(browser, "04_after_email_submit")

                # Step 3: Wait for verification email (10-30 seconds)
                verification_data = None
                for attempt in range(6):  # Check every 5 seconds for 30 seconds total
                    await asyncio.sleep(5)

                    # Get raw email content for this specific email
                    raw_content = await self.email_client.get_latest_email_content(
                        "exaai", self.profile["email"]
                    )
                    if raw_content:
                        # Parse dashboard link
                        dashboard_link = self.parse_magic_link(raw_content)

                        if dashboard_link:
                            verification_data = {
                                "dashboard_link": dashboard_link,
                                "raw": raw_content,
                            }
                            # Save email content to WIP directory
                            email_path = self._save_email_content(
                                raw_content or "", self.profile["email"], "wip"
                            )
                            verification_data["email_path"] = email_path

                            logger.info(
                                f"Verification email received for {self.profile['email']} after {(attempt + 1) * 5} seconds"
                            )
                            break

                    logger.info(
                        f"Waiting for verification email for {self.profile['email']}... ({(attempt + 1) * 5}s)"
                    )

                if not verification_data:
                    # Fallback: Try to parse locally
                    logger.info(
                        f"Attempting local email parsing as fallback for {self.profile['email']}..."
                    )
                    try:
                        raw_email_data = await self._get_raw_email_content_for_email(
                            self.profile["email"]
                        )
                        if raw_email_data:
                            dashboard_link = self.parse_magic_link(raw_email_data)

                            if dashboard_link:
                                verification_data = {"dashboard_link": dashboard_link}
                                logger.info(
                                    f"Successfully parsed verification data locally for {self.profile['email']}"
                                )
                    except Exception as e:
                        logger.error(
                            f"Local parsing failed for {self.profile['email']}: {e}"
                        )

                if not verification_data:
                    return RegistrationResult(
                        service="exaai",
                        success=False,
                        error_message=f"No verification email received after 30 seconds for {self.profile['email']}",
                    )

                # Extract the dashboard link from the email
                dashboard_link = verification_data.get("dashboard_link")
                if not dashboard_link:
                    return RegistrationResult(
                        service="exaai",
                        success=False,
                        error_message="No dashboard link found in verification email",
                    )

                logger.info(f"🔗 Navigating to verification page: {dashboard_link}")
                await browser.goto(dashboard_link)
                await browser.wait(3000)
                await self._debug_screenshot(browser, "02_landed_on_verification")

                # Try to verify with the original code first
                success = await self._attempt_verification(browser)
                if not success:
                    # If verification fails, try resend flow
                    logger.warning(
                        "⚠️ Initial verification failed, trying resend flow..."
                    )
                    success = await self._resend_verification_flow(browser)

                if not success:
                    return RegistrationResult(
                        service="exaai",
                        success=False,
                        error_message="Failed to verify account even after resend",
                    )

                logger.info("✅ Successfully verified and on dashboard")
                await self._debug_screenshot(browser, "04_on_dashboard")

                # Navigate to Search page first to simulate human activity
                logger.info("🔍 Navigating to Search page under API Playground...")
                search_successful = await self._simulate_search_activity(browser)

                # Now navigate to API Keys page
                logger.info("🔑 Now navigating to API Keys page...")
                try:
                    # Try multiple selectors for API Keys link
                    api_keys_selectors = [
                        'a:has-text("API Keys")',
                        'a[href*="api-key"]',
                        'a[href="/settings"]',
                        'a[title*="API"]',
                        'nav a:has-text("API")',
                        'a:has-text("Settings")',
                    ]

                    api_keys_link = None
                    for selector in api_keys_selectors:
                        try:
                            api_keys_link = await browser.page.query_selector(selector)
                            if api_keys_link and await api_keys_link.is_visible():
                                logger.info(
                                    f"✅ Found API Keys link with selector: {selector}"
                                )
                                break
                        except:
                            continue

                    if api_keys_link:
                        await api_keys_link.click()
                        await self.randomizer.delay(3, 5)
                        await self._debug_screenshot(browser, "07_api_keys_page")
                        logger.info("✅ Navigated to API Keys page")
                    else:
                        # Fallback: Navigate directly to API Keys URL
                        logger.info("⚠️ No API Keys navigation found, using direct URL")
                        await browser.goto("https://dashboard.exa.ai/api-keys")
                        await browser.wait(3000)
                        await self._debug_screenshot(browser, "07_api_keys_page_direct")
                        logger.info("✅ Navigated directly to API Keys page")

                except Exception as e:
                    return RegistrationResult(
                        service="exaai",
                        success=False,
                        error_message=f"Failed to navigate to API Keys: {e}",
                    )

                # Extract API key using clipboard method
                api_key = await self._extract_api_key_via_clipboard(browser)

                if not api_key:
                    return RegistrationResult(
                        service="exaai",
                        success=False,
                        error_message="Could not extract API key via clipboard",
                    )

                logger.info(
                    f"🔑 Successfully extracted API key: {api_key[:12]}... (total length: {len(api_key)} chars)"
                )
                await self._debug_screenshot(browser, "09_api_key_extracted")

                # Stay on the page based on configuration
                stay_duration = (
                    self.config.delay_times.get("stay_on_finish", 10000) / 1000
                )  # Convert to seconds
                logger.info(
                    f"⏳ Staying on API keys page for {stay_duration} seconds before closing session..."
                )
                await asyncio.sleep(stay_duration)
                await self._debug_screenshot(browser, "10_before_session_end")

                # Move email to done directory on success
                email_path = verification_data.get("email_path")
                if email_path:
                    self._move_email_to_done(Path(email_path))

                return RegistrationResult(
                    service="exaai",
                    success=True,
                    api_key=api_key,
                    email=self.profile["email"],
                    search_successful=search_successful,  # Add search status
                )

        except Exception as e:
            logger.error(f"Error during ExaAI automation: {e}")
            return RegistrationResult(
                service="exaai", success=False, error_message=str(e)
            )
        finally:
            # Clean up debug screenshots after session
            self._cleanup_debug_screenshots()

    async def _extract_api_key(self, browser: BrowserManager) -> Optional[str]:
        """Extract the generated API key from the page."""
        try:
            # Strategy 1: Look for API key in input fields (readonly or regular)
            input_selectors = [
                "input[readonly]",
                'input[type="text"]',
                'input[value*="exa_"]',  # ExaAI keys might start with exa_
                'input[value*="key"]',
            ]

            for selector in input_selectors:
                try:
                    inputs = await browser.page.query_selector_all(selector)
                    for input_elem in inputs:
                        value = await input_elem.get_attribute("value") or ""
                        if self._looks_like_api_key(value):
                            logger.info(f"Found API key in input: {value}")
                            return value
                except:
                    continue

            # Strategy 2: Look for API key in text elements, but skip code examples and long texts
            all_elements = await browser.page.query_selector_all("*")
            for elem in all_elements:
                try:
                    # Skip elements that are likely code examples
                    elem_class = await elem.get_attribute("class") or ""
                    if "example" in elem_class or "code" in elem_class:
                        continue

                    # Skip elements with long text that might be code
                    text = await elem.text_content() or ""
                    text = text.strip()
                    if len(text) > 100 or "=" in text or "(" in text:
                        continue

                    if self._looks_like_api_key(text):
                        logger.info(f"Found API key in text: {text}")
                        return text
                except:
                    continue

            # Strategy 3: Look in page source for patterns with word boundaries
            page_content = await browser.page.content()
            api_key_patterns = [
                r"\bexa_[a-zA-Z0-9]{20,}\b",  # ExaAI pattern with word boundaries
                r"\b[a-f0-9]{32}\b",  # 32-char hex
                r"\b[A-Za-z0-9]{40,}\b",  # Long alphanumeric
                r'"key":\s*"([^"]+)"',  # JSON key field
                r'"api_key":\s*"([^"]+)"',  # JSON api_key field
            ]

            for pattern in api_key_patterns:
                matches = re.findall(pattern, page_content)
                for match in matches:
                    key = match if isinstance(match, str) else match[0]
                    if self._looks_like_api_key(key):
                        logger.info(f"Found API key in page source: {key}")
                        return key

            logger.warning("No API key found using any extraction strategy")
            return None

        except Exception as e:
            logger.error(f"Error extracting API key: {e}")
            return None

    def _looks_like_api_key(self, text: str) -> bool:
        """Check if text looks like an API key."""
        if not text or len(text) < 16:
            return False

        text = text.strip()

        # ExaAI specific patterns
        if text.startswith("exa_") and len(text) > 20:
            return True

        # Generic patterns
        if len(text) >= 32 and all(c.isalnum() or c in "-_" for c in text):
            return True

        if len(text) == 32 and all(c in "0123456789abcdef" for c in text.lower()):
            return True

        return False

    async def _get_raw_email_content_for_email(self, email: str) -> str:
        """Get raw email content for a specific email address."""
        try:
            # Get raw email content using the configured client
            raw_content = await self.email_client.get_latest_email_content(
                "exaai", email
            )
            if raw_content:
                logger.info(
                    f"Retrieved raw email content for {email}: {len(raw_content)} characters"
                )
                return raw_content
            else:
                logger.warning(f"No raw email content found for {email}")
                return ""

        except Exception as e:
            logger.error(f"Error getting raw email content for {email}: {e}")
            return ""

    async def _attempt_verification(self, browser: BrowserManager) -> bool:
        """Attempt to verify with the current verification code."""
        try:
            # Look for VERIFY CODE button
            verify_button = await browser.page.query_selector(
                'button:has-text("VERIFY CODE")'
            )
            if verify_button and await verify_button.is_visible():
                logger.info("🔄 Clicking 'VERIFY CODE' with original code...")
                await verify_button.click()

                # Wait for verification to complete - be patient, just like the recovery script
                await self._wait_for_verification_complete(browser.page)

                # Wait patiently for dashboard to load - retry 12 times for 5 seconds each
                logger.info("⏳ Waiting for dashboard to load...")
                for attempt in range(12):  # 12 attempts = 60 seconds total
                    await asyncio.sleep(5)

                    current_url = browser.page.url
                    logger.info(
                        f"Attempt {attempt + 1}/12 - Current URL: {current_url}"
                    )

                    # Check for error messages on login page first
                    if "/login" in current_url:
                        try:
                            error_element = await browser.page.query_selector(
                                'text="Invalid or expired verification code"'
                            )
                            if error_element and await error_element.is_visible():
                                logger.warning(
                                    "Found 'Invalid or expired verification code' error"
                                )
                                return False  # Failed verification
                        except:
                            pass
                        continue  # Still on login page, keep waiting

                    # Check if we successfully got to dashboard URL
                    if "/home" in current_url or "/dashboard" in current_url:
                        logger.info(f"✅ Dashboard URL detected: {current_url}")

                        # Now wait for dashboard elements to be fully loaded and interactive
                        logger.info(
                            "⏳ Waiting for dashboard elements to be fully loaded..."
                        )
                        dashboard_loaded = (
                            await self._wait_for_dashboard_elements_loaded(browser.page)
                        )

                        if dashboard_loaded:
                            logger.info("✅ Dashboard is fully loaded and interactive!")
                            return True
                        else:
                            logger.info(
                                f"Dashboard URL reached but elements not yet loaded (attempt {attempt + 1}/12)"
                            )
                            continue

                    logger.info(
                        f"Still waiting for dashboard... (attempt {attempt + 1}/12)"
                    )

                # After 12 attempts, do final verification
                logger.info("⏳ Final dashboard verification after 12 attempts...")
                return await self._verify_dashboard_access(browser.page)
            else:
                logger.warning("No VERIFY CODE button found")
                return False
        except Exception as e:
            logger.error(f"Error during verification attempt: {e}")
            return False

    async def _wait_for_verification_complete(self, page: Page) -> None:
        """Wait for verification process to complete."""
        logger.info("⏳ Waiting for verification to complete...")
        for attempt in range(15):  # Wait up to 75 seconds (more patient)
            await asyncio.sleep(5)

            # Check if we're still in a "verifying" state
            try:
                verifying_indicators = [
                    'text="Verifying"',
                    'text="Loading"',
                    ".spinner",
                    ".loading",
                ]

                still_verifying = False
                for indicator in verifying_indicators:
                    element = await page.query_selector(indicator)
                    if element and await element.is_visible():
                        still_verifying = True
                        break

                if not still_verifying:
                    logger.info("✅ Verification process completed!")
                    break
                else:
                    logger.info(f"Still verifying... attempt {attempt + 1}/15")
            except:
                break

    async def _wait_for_dashboard_elements_loaded(self, page: Page) -> bool:
        """Wait for dashboard navigation elements to be fully loaded and interactive."""
        try:
            # Wait for key navigation elements to be present and visible
            essential_elements = [
                'a:has-text("Search")',  # Search link
                'a:has-text("API Keys")',  # API Keys link
                'a:has-text("Playground")',  # Playground link
                "nav",  # Main navigation
            ]

            # Try up to 6 times (30 seconds) to find interactive elements
            for attempt in range(6):
                await asyncio.sleep(5)
                logger.info(f"Checking dashboard elements... (attempt {attempt + 1}/6)")

                elements_found = 0
                for selector in essential_elements:
                    try:
                        element = await page.query_selector(selector)
                        if element and await element.is_visible():
                            elements_found += 1
                    except:
                        continue

                logger.info(
                    f"Found {elements_found}/{len(essential_elements)} essential dashboard elements"
                )

                # If we have at least 2 essential elements, dashboard is likely loaded
                if elements_found >= 2:
                    # Extra verification: try to find search-specific navigation
                    try:
                        search_selectors = [
                            "a.command-bar-nav-search",
                            'a[href="/playground/search"]',
                            'a[title="Search"]',
                        ]

                        search_nav_found = False
                        for search_selector in search_selectors:
                            search_element = await page.query_selector(search_selector)
                            if search_element and await search_element.is_visible():
                                logger.info(
                                    f"✅ Found search navigation: {search_selector}"
                                )
                                search_nav_found = True
                                break

                        if search_nav_found:
                            logger.info(
                                "✅ Dashboard fully loaded with search navigation available!"
                            )
                            return True
                        else:
                            logger.info(
                                "Dashboard elements found but search navigation not yet ready"
                            )
                    except:
                        pass

                logger.info(
                    f"Dashboard not fully loaded yet... waiting more (attempt {attempt + 1}/6)"
                )

            # If we couldn't find proper navigation after 6 attempts, dashboard might still be loading
            logger.warning("Dashboard elements not fully loaded after 30 seconds")
            return False

        except Exception as e:
            logger.error(f"Error waiting for dashboard elements: {e}")
            return False

    async def _verify_dashboard_access(self, page: Page) -> bool:
        """Robustly verify if dashboard access is successful."""
        try:
            await asyncio.sleep(5)  # Wait for page to settle
            current_url = page.url
            logger.info(f"Current URL: {current_url}")

            # If we're on the home page, that's success!
            if "/home" in current_url or "/dashboard" in current_url:
                logger.info("✅ Successfully on dashboard home page!")
                return True

            # Check if still on verification page
            if "/login" in current_url or "/verify" in current_url:
                logger.warning(f"Still on login/verify page: {current_url}")

                # Only check for error indicators if we're still on login page
                error_selectors = [
                    'text="Invalid or expired verification code"',
                    'text="Error"',
                    ".error",
                ]

                for error_selector in error_selectors:
                    try:
                        error_element = await page.query_selector(error_selector)
                        if error_element and await error_element.is_visible():
                            logger.warning(f"Found error indicator: {error_selector}")
                            return False
                    except:
                        continue

                # Check page text for error keywords
                try:
                    page_text = await page.text_content("body")
                    if page_text:
                        error_keywords = ["invalid", "expired", "error", "failed"]
                        for keyword in error_keywords:
                            if keyword.lower() in page_text.lower():
                                logger.warning(
                                    f"Found error keyword in page text: {keyword}"
                                )
                                return False
                except:
                    pass

                return False

            # Look for dashboard indicators
            dashboard_indicators = [
                'a:has-text("Search")',
                'a:has-text("API Keys")',
                'a:has-text("Playground")',
                "nav",
                "header",
            ]

            for indicator in dashboard_indicators:
                try:
                    element = await page.query_selector(indicator)
                    if element and await element.is_visible():
                        logger.info(f"✅ Found dashboard indicator: {indicator}")
                        return True
                except:
                    continue

            logger.warning("❌ No dashboard navigation elements found")
            return False

        except Exception as e:
            logger.error(f"Error verifying dashboard access: {e}")
            return False

    async def _resend_verification_flow(self, browser: BrowserManager) -> bool:
        """Handle resend verification flow."""
        try:
            # Look for resend button
            resend_button = await browser.page.query_selector(
                'button:has-text("Resend verification email")'
            )
            if not resend_button:
                logger.error("No resend button found")
                return False

            # Clear old emails and resend
            await self.email_client.clear_emails("exaai", self.profile["email"])
            await resend_button.click()
            logger.info("📧 Clicked resend button")
            await asyncio.sleep(3)

            # Wait for new verification email
            return await self._get_new_verification_and_verify(browser)

        except Exception as e:
            logger.error(f"Error in resend flow: {e}")
            return False

    async def _get_new_verification_and_verify(self, browser: BrowserManager) -> bool:
        """Get new verification email and verify."""
        try:
            # Poll for new email
            logger.info("Polling webhook for new verification email...")
            for attempt in range(18):  # 90 seconds total
                await asyncio.sleep(5)

                raw_content = await self.email_client.get_latest_email_content(
                    "exaai", self.profile["email"]
                )
                if raw_content:
                    dashboard_link = self.parse_magic_link(raw_content)
                    if dashboard_link:
                        logger.info("📧 Got new verification email")

                        # Check if this is a welcome email with direct API keys link
                        if "/api-keys" in dashboard_link:
                            logger.info(
                                "🎉 Received welcome email with direct API keys link!"
                            )
                            return True  # Success - we're already verified

                        # Navigate to new magic link for verification
                        await browser.goto(dashboard_link)
                        await asyncio.sleep(3)

                        # Try verification again
                        verify_button = await browser.page.query_selector(
                            'button:has-text("VERIFY CODE")'
                        )
                        if verify_button:
                            await verify_button.click()
                            await self._wait_for_verification_complete(browser.page)
                            return await self._verify_dashboard_access(browser.page)
                        else:
                            # If no verify button, check if we're already on dashboard
                            return await self._verify_dashboard_access(browser.page)

            logger.error("No new verification email received")
            return False

        except Exception as e:
            logger.error(f"Error getting new verification: {e}")
            return False

    async def _find_result_category_dropdown(self, browser: BrowserManager):
        """Find Result Category dropdown using real Playwright selectors."""
        logger.info(
            "🔍 Looking for Result Category dropdown with real element detection..."
        )

        # Strategy 1: Look for text "Result Category" and find nearby buttons
        try:
            # First, try to find elements containing "Result Category" text
            category_text_elements = await browser.page.query_selector_all(
                '*:has-text("Result Category"), *:has-text("Category")'
            )

            for text_element in category_text_elements:
                if await text_element.is_visible():
                    logger.info(
                        "🔍 Found 'Category' text, looking for nearby dropdown..."
                    )

                    # Look for buttons/dropdowns near this text element
                    nearby_selectors = [
                        "button",
                        '[role="combobox"]',
                        '[role="button"]',
                        "select",
                    ]

                    # Check parent container for dropdowns
                    for selector in nearby_selectors:
                        try:
                            # Look in the same container
                            container = await text_element.evaluate_handle(
                                'el => el.closest("div, section, form") || el.parentElement'
                            )
                            if container:
                                dropdown = await container.query_selector(selector)
                                if dropdown and await dropdown.is_visible():
                                    logger.info(
                                        f"✅ Found dropdown near category text: {selector}"
                                    )
                                    return dropdown
                        except:
                            continue

        except Exception as e:
            logger.debug(f"Text-based search failed: {e}")

        # Strategy 2: Look for common dropdown patterns
        logger.info("🔍 Trying common dropdown selectors...")
        dropdown_selectors = [
            'button[role="combobox"]',
            'button[aria-haspopup="listbox"]',
            'button[aria-expanded="false"]',
            "select",
            'button:has-text("Auto")',  # Common default text
            'button:has-text("All")',
            'button:has-text("Any")',
            '[class*="select" i]',
            '[class*="dropdown" i]',
        ]

        for selector in dropdown_selectors:
            try:
                elements = await browser.page.query_selector_all(selector)
                for element in elements:
                    if await element.is_visible():
                        # Check if this looks like a category dropdown
                        text_content = await element.text_content()
                        if (
                            text_content and len(text_content.strip()) < 50
                        ):  # Reasonable dropdown text
                            logger.info(
                                f"✅ Found potential dropdown: {selector} (text: '{text_content.strip()}')"
                            )
                            return element
            except Exception as e:
                logger.debug(f"Selector {selector} failed: {e}")
                continue

        # Strategy 3: Look for any visible combobox or select elements
        logger.info("🔍 Looking for any visible dropdown elements...")
        try:
            all_dropdowns = await browser.page.query_selector_all(
                'button[role="combobox"], select, [aria-haspopup="listbox"]'
            )
            for dropdown in all_dropdowns:
                if await dropdown.is_visible():
                    text = await dropdown.text_content()
                    logger.info(
                        f"✅ Found visible dropdown with text: '{text.strip() if text else 'No text'}'"
                    )
                    return dropdown
        except Exception as e:
            logger.debug(f"Generic dropdown search failed: {e}")

        return None

    async def _try_select_elements(self, browser: BrowserManager) -> bool:
        """Try to interact with simple <select> elements."""
        try:
            logger.info("🔍 Looking for <select> elements...")

            # Find all select elements
            select_elements = await browser.page.query_selector_all("select")

            for select_element in select_elements:
                if await select_element.is_visible():
                    # Get the current value and available options
                    current_value = await select_element.input_value()
                    options = await select_element.query_selector_all("option")

                    if len(options) > 1:  # Has multiple options
                        logger.info(
                            f"🔍 Found select with {len(options)} options, current: '{current_value}'"
                        )

                        # Try to select a different option (not the first one)
                        for option in options[1:]:  # Skip first option
                            option_value = await option.get_attribute("value")
                            option_text = await option.text_content()

                            if option_value and option_value != current_value:
                                await select_element.select_option(option_value)
                                logger.info(
                                    f"✅ Selected option: '{option_text}' (value: '{option_value}')"
                                )
                                await asyncio.sleep(1)
                                return True

            logger.info("ℹ️ No suitable select elements found")
            return False

        except Exception as e:
            logger.debug(f"Select element interaction failed: {e}")
            return False

    async def _try_checkbox_radio_elements(self, browser: BrowserManager) -> bool:
        """Try to interact with checkboxes or radio buttons."""
        try:
            logger.info("🔍 Looking for checkboxes and radio buttons...")

            # Look for unchecked checkboxes
            checkboxes = await browser.page.query_selector_all(
                'input[type="checkbox"]:not(:checked)'
            )
            for checkbox in checkboxes:
                if await checkbox.is_visible():
                    # Get associated label or nearby text
                    label_text = await self._get_element_label(browser, checkbox)
                    logger.info(f"🔍 Found unchecked checkbox: '{label_text}'")

                    # Check the checkbox
                    await checkbox.check()
                    logger.info(f"✅ Checked checkbox: '{label_text}'")
                    await asyncio.sleep(1)
                    return True

            # Look for radio buttons (try to select a different one)
            radio_groups = {}
            radios = await browser.page.query_selector_all('input[type="radio"]')

            for radio in radios:
                if await radio.is_visible():
                    name = await radio.get_attribute("name")
                    if name:
                        if name not in radio_groups:
                            radio_groups[name] = []
                        radio_groups[name].append(radio)

            # Try to select a different radio button in each group
            for group_name, group_radios in radio_groups.items():
                if len(group_radios) > 1:
                    for radio in group_radios:
                        is_checked = await radio.is_checked()
                        if not is_checked:
                            label_text = await self._get_element_label(browser, radio)
                            logger.info(
                                f"🔍 Found unchecked radio: '{label_text}' (group: {group_name})"
                            )

                            await radio.check()
                            logger.info(f"✅ Selected radio: '{label_text}'")
                            await asyncio.sleep(1)
                            return True

            logger.info("ℹ️ No suitable checkboxes or radio buttons found")
            return False

        except Exception as e:
            logger.debug(f"Checkbox/radio interaction failed: {e}")
            return False

    async def _try_text_input_elements(self, browser: BrowserManager) -> bool:
        """Try to interact with text input elements."""
        try:
            logger.info("🔍 Looking for text input elements...")

            # Look for empty text inputs, textareas, or inputs with placeholder text
            input_selectors = [
                'input[type="text"]:not([value])',
                'input[type="search"]:not([value])',
                "input:not([type]):not([value])",
                "textarea:empty",
                "input[placeholder]",
            ]

            for selector in input_selectors:
                inputs = await browser.page.query_selector_all(selector)
                for input_element in inputs:
                    if (
                        await input_element.is_visible()
                        and await input_element.is_enabled()
                    ):
                        current_value = await input_element.input_value()
                        placeholder = await input_element.get_attribute("placeholder")
                        label_text = await self._get_element_label(
                            browser, input_element
                        )

                        # Skip if already has content
                        if current_value and len(current_value.strip()) > 0:
                            continue

                        logger.info(
                            f"🔍 Found empty input: '{label_text}' (placeholder: '{placeholder}')"
                        )

                        # Fill with a simple test value
                        test_values = ["test", "example", "sample", "demo"]
                        for test_value in test_values:
                            try:
                                await input_element.fill(test_value)
                                logger.info(
                                    f"✅ Filled input '{label_text}' with: '{test_value}'"
                                )
                                await asyncio.sleep(1)
                                return True
                            except:
                                continue

            logger.info("ℹ️ No suitable text input elements found")
            return False

        except Exception as e:
            logger.debug(f"Text input interaction failed: {e}")
            return False

    async def _get_element_label(self, browser: BrowserManager, element) -> str:
        """Get the label or descriptive text for an element."""
        try:
            # Try to find associated label
            element_id = await element.get_attribute("id")
            if element_id:
                label = await browser.page.query_selector(f'label[for="{element_id}"]')
                if label:
                    label_text = await label.text_content()
                    if label_text:
                        return label_text.strip()

            # Try parent label
            parent_label = await element.evaluate('el => el.closest("label")')
            if parent_label:
                label_text = await parent_label.text_content()
                if label_text:
                    return label_text.strip()

            # Try nearby text
            placeholder = await element.get_attribute("placeholder")
            if placeholder:
                return f"[placeholder: {placeholder}]"

            # Try aria-label
            aria_label = await element.get_attribute("aria-label")
            if aria_label:
                return f"[aria-label: {aria_label}]"

            return "[no label]"

        except:
            return "[unknown]"

    async def _try_search_type_dropdown(self, browser: BrowserManager) -> bool:
        """Try to change the Search Type dropdown from 'Auto' to something else."""
        try:
            logger.info("🔍 Looking for Search Type dropdown...")

            # Look for elements containing "Search Type" and nearby dropdowns
            search_type_selectors = [
                'button:has-text("Auto")',
                'select:has-text("Auto")',
                '[aria-label*="Search Type"]',
                '[aria-label*="search type"]',
                'button[role="combobox"]:has-text("Auto")',
                # Look near "Search Type" text
                '*:has-text("Search Type") + * button',
                '*:has-text("Search Type") ~ * button',
            ]

            for selector in search_type_selectors:
                try:
                    element = await browser.page.query_selector(selector)
                    if element and await element.is_visible():
                        logger.info(f"🎯 Found Search Type control: {selector}")
                        await element.click()
                        await asyncio.sleep(1)

                        # Try to select a different option
                        options = await browser.page.query_selector_all(
                            '[role="option"], li, .option'
                        )
                        for option in options:
                            if await option.is_visible():
                                text = await option.text_content()
                                if text and text.strip().lower() in [
                                    "neural",
                                    "keyword",
                                    "semantic",
                                ]:
                                    await option.click()
                                    logger.info(
                                        f"✅ Changed Search Type to: {text.strip()}"
                                    )
                                    await asyncio.sleep(1)
                                    return True
                        break
                except Exception as e:
                    logger.debug(f"Search type selector {selector} failed: {e}")
                    continue

            return False
        except Exception as e:
            logger.debug(f"Search type dropdown failed: {e}")
            return False

    async def _try_result_category_dropdown(self, browser: BrowserManager) -> bool:
        """Try to select a Result Category."""
        try:
            logger.info("🔍 Looking for Result Category dropdown...")

            # Look for Result Category dropdown
            category_selectors = [
                '*:has-text("Result category") + * button',
                '*:has-text("Result category") ~ * button',
                '[aria-label*="Result category"]',
                '[aria-label*="category"]',
                'button[role="combobox"]:near(*:has-text("Result category"))',
            ]

            for selector in category_selectors:
                try:
                    element = await browser.page.query_selector(selector)
                    if element and await element.is_visible():
                        logger.info(f"🎯 Found Result Category control: {selector}")
                        await element.click()
                        await asyncio.sleep(1)

                        # Try to select any category option
                        options = await browser.page.query_selector_all(
                            '[role="option"], li, .option'
                        )
                        for option in options:
                            if await option.is_visible():
                                text = await option.text_content()
                                if text and len(text.strip()) > 0:
                                    await option.click()
                                    logger.info(
                                        f"✅ Selected Result Category: {text.strip()}"
                                    )
                                    await asyncio.sleep(1)
                                    return True
                        break
                except Exception as e:
                    logger.debug(f"Result category selector {selector} failed: {e}")
                    continue

            return False
        except Exception as e:
            logger.debug(f"Result category dropdown failed: {e}")
            return False

    async def _try_all_text_inputs(self, browser: BrowserManager) -> int:
        """Try to fill all available text inputs. Returns count of successful interactions."""
        interactions = 0

        try:
            logger.info("🔍 Looking for text inputs to fill...")

            # Define only essential inputs to change (conservative approach)
            input_targets = [
                {
                    "name": "Number of results",
                    "selectors": [
                        'input[placeholder*="Default"]',
                        'input[placeholder*="max"]',
                        'input[placeholder*="10"]',
                    ],
                    "value": "15",  # Reasonable number, not too high
                },
                {
                    "name": "Timeout",
                    "selectors": [
                        'input[placeholder*="10000"]',
                        'input[placeholder*="timeout"]',
                        'input[placeholder*="Max:"]',
                    ],
                    "value": "8000",  # Reasonable timeout value
                },
            ]

            for target in input_targets:
                for selector in target["selectors"]:
                    try:
                        elements = await browser.page.query_selector_all(selector)
                        for element in elements:
                            if (
                                await element.is_visible()
                                and await element.is_enabled()
                            ):
                                current_value = await element.input_value()
                                if not current_value or len(current_value.strip()) == 0:
                                    await element.fill(target["value"])
                                    logger.info(
                                        f"✅ Filled {target['name']}: {target['value']}"
                                    )
                                    interactions += 1
                                    await asyncio.sleep(0.5)
                                    break
                    except Exception as e:
                        logger.debug(f"Input {target['name']} failed: {e}")
                        continue

            return interactions
        except Exception as e:
            logger.debug(f"Text inputs failed: {e}")
            return interactions

    async def _try_toggle_switches(self, browser: BrowserManager) -> int:
        """Try to toggle switches/checkboxes. Returns count of successful interactions."""
        interactions = 0

        try:
            logger.info("🔍 Looking for toggle switches...")

            # Look for toggle switches based on the screenshot labels
            toggle_targets = [
                "Full webpage text",
                "Include HTML tags",
                "AI page summary",
                "Subpages",
                "Links",
            ]

            for target in toggle_targets:
                try:
                    # Look for switches/checkboxes near these labels
                    selectors = [
                        f'*:has-text("{target}") input[type="checkbox"]',
                        f'*:has-text("{target}") button[role="switch"]',
                        f'*:has-text("{target}") + * input',
                        f'*:has-text("{target}") ~ * input',
                        f'label:has-text("{target}") input',
                    ]

                    for selector in selectors:
                        elements = await browser.page.query_selector_all(selector)
                        for element in elements:
                            if await element.is_visible():
                                # Toggle the switch
                                is_checked = await element.is_checked()
                                if is_checked:
                                    await element.uncheck()
                                    logger.info(f"✅ Unchecked: {target}")
                                else:
                                    await element.check()
                                    logger.info(f"✅ Checked: {target}")
                                interactions += 1
                                await asyncio.sleep(0.5)
                                break
                        if interactions > 0:
                            break
                except Exception as e:
                    logger.debug(f"Toggle {target} failed: {e}")
                    continue

            return interactions
        except Exception as e:
            logger.debug(f"Toggle switches failed: {e}")
            return interactions

    async def _try_remaining_dropdowns(self, browser: BrowserManager) -> int:
        """Try to interact with any remaining dropdowns. Returns count of successful interactions."""
        interactions = 0

        try:
            logger.info("🔍 Looking for remaining dropdowns...")

            # Look for Livecrawl strategy dropdown (shows "Fallback" in screenshot)
            livecrawl_selectors = [
                'button:has-text("Fallback")',
                '*:has-text("Livecrawl") + * button',
                '*:has-text("strategy") + * button',
                '[aria-label*="Livecrawl"]',
            ]

            for selector in livecrawl_selectors:
                try:
                    element = await browser.page.query_selector(selector)
                    if element and await element.is_visible():
                        logger.info(f"🎯 Found Livecrawl dropdown: {selector}")
                        await element.click()
                        await asyncio.sleep(1)

                        # Try to select a different option
                        options = await browser.page.query_selector_all(
                            '[role="option"], li, .option'
                        )
                        for option in options:
                            if await option.is_visible():
                                text = await option.text_content()
                                if text and text.strip().lower() != "fallback":
                                    await option.click()
                                    logger.info(
                                        f"✅ Changed Livecrawl strategy to: {text.strip()}"
                                    )
                                    interactions += 1
                                    await asyncio.sleep(1)
                                    break
                        break
                except Exception as e:
                    logger.debug(f"Livecrawl selector {selector} failed: {e}")
                    continue

            return interactions
        except Exception as e:
            logger.debug(f"Remaining dropdowns failed: {e}")
            return interactions

    async def _find_run_button(self, browser: BrowserManager):
        """Find the Run button using multiple detection strategies."""
        try:
            logger.info("🔍 Searching for Run button with multiple strategies...")

            # Strategy 1: Simple text-based selectors
            simple_selectors = [
                'button:has-text("Run")',
                'button:has-text("run")',
                'input[type="submit"][value*="Run"]',
                'input[type="button"][value*="Run"]',
                '[role="button"]:has-text("Run")',
            ]

            for selector in simple_selectors:
                try:
                    element = await browser.page.query_selector(selector)
                    if element and await element.is_visible():
                        logger.info(f"✅ Found Run button with selector: {selector}")
                        return element
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue

            # Strategy 2: Look for buttons with specific attributes or classes
            attribute_selectors = [
                'button[type="submit"]',
                'button[class*="submit"]',
                'button[class*="run"]',
                'button[class*="search"]',
                'button[class*="execute"]',
                'button[id*="run"]',
                'button[id*="submit"]',
            ]

            for selector in attribute_selectors:
                try:
                    elements = await browser.page.query_selector_all(selector)
                    for element in elements:
                        if await element.is_visible():
                            text = await element.text_content()
                            if text and "run" in text.lower():
                                logger.info(
                                    f"✅ Found Run button with attribute selector: {selector}"
                                )
                                return element
                except Exception as e:
                    logger.debug(f"Attribute selector {selector} failed: {e}")
                    continue

            # Strategy 3: Look for any visible button that might be the primary action
            try:
                all_buttons = await browser.page.query_selector_all("button")
                for button in all_buttons:
                    if await button.is_visible():
                        text = await button.text_content()
                        if text:
                            text_lower = text.strip().lower()
                            if text_lower in [
                                "run",
                                "search",
                                "execute",
                                "submit",
                                "go",
                            ]:
                                logger.info(
                                    f"✅ Found potential Run button with text: '{text.strip()}'"
                                )
                                return button
            except Exception as e:
                logger.debug(f"Generic button search failed: {e}")

            logger.warning("❌ Could not find Run button with any strategy")
            return None

        except Exception as e:
            logger.debug(f"Run button detection failed: {e}")
            return None

    async def _try_coordinate_based_dropdown_detection(self, browser: BrowserManager):
        """Simple fallback: try clicking near any visible buttons that might be dropdowns."""
        try:
            logger.info("🎯 Trying to click near potential dropdown buttons...")

            # Find all visible buttons and try clicking them
            buttons_clicked = await browser.page.evaluate("""
                () => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    const visibleButtons = buttons.filter(btn =>
                        btn.offsetHeight > 0 && btn.offsetWidth > 0
                    );

                    // Try clicking buttons that might be dropdowns
                    for (const btn of visibleButtons) {
                        const text = btn.textContent?.toLowerCase() || '';
                        const className = btn.className?.toLowerCase() || '';

                        // Skip obvious non-dropdown buttons
                        if (text.includes('run') || text.includes('submit') || text.includes('search')) {
                            continue;
                        }

                        // Click buttons that might be dropdowns
                        if (btn.getAttribute('role') === 'combobox' ||
                            className.includes('select') ||
                            className.includes('dropdown') ||
                            text.includes('auto') ||
                            text === '') {
                            btn.click();
                            return true;
                        }
                    }
                    return false;
                }
            """)

            if buttons_clicked:
                logger.info("✅ Clicked a potential dropdown button")
                await asyncio.sleep(1)

                # Check if dropdown options appeared
                options_visible = await browser.page.query_selector_all(
                    '[role="option"], [role="menuitem"], li, .option'
                )

                if options_visible:
                    logger.info("✅ Dropdown options appeared")
                    return await browser.page.query_selector(
                        "body"
                    )  # Return placeholder

            return None

        except Exception as e:
            logger.debug(f"Coordinate-based detection error: {e}")
            return None

    async def _interact_with_category_dropdown(
        self, browser: BrowserManager, category_button
    ) -> bool:
        """Interact with the found category dropdown button."""
        try:
            # Check if this was found via coordinate-based detection
            is_coordinate_based = (
                category_button
                and await category_button.evaluate("el => el.tagName") == "BODY"
            )

            if is_coordinate_based:
                logger.info("🎯 Using coordinate-based dropdown interaction")
                # For coordinate-based, we already clicked and opened the dropdown
                # Just try to find and select an option
                return await self._select_dropdown_option(browser)
            else:
                logger.info("🎯 Using element-based dropdown interaction")
                # Click the dropdown button to open it
                await category_button.click()
                logger.info("✅ Clicked Result Category dropdown")
                await asyncio.sleep(2)  # Wait for dropdown to open

                # Try to select an option
                return await self._select_dropdown_option(browser)

        except Exception as e:
            logger.warning(f"Error interacting with dropdown: {e}")
            return False

    async def _select_dropdown_option(self, browser: BrowserManager) -> bool:
        """Select any available option using real Playwright interactions."""
        try:
            logger.info(
                "🔍 Looking for dropdown options with real element detection..."
            )

            # Wait for dropdown to open
            await asyncio.sleep(2)

            # Look for dropdown options using real Playwright selectors
            option_selectors = [
                '[role="option"]',
                '[role="menuitem"]',
                "li[data-value]",
                ".option",
                '[class*="option" i]',
                "li:visible",
                "div[data-value]",
                '[tabindex="0"]:not(button)',
            ]

            for selector in option_selectors:
                try:
                    options = await browser.page.query_selector_all(selector)
                    logger.info(
                        f"🔍 Found {len(options)} elements with selector: {selector}"
                    )

                    for option in options:
                        if await option.is_visible():
                            text = await option.text_content()
                            text = text.strip() if text else ""

                            # Skip empty or very long text
                            if text and 0 < len(text) < 100:
                                logger.info(f"🎯 Attempting to click option: '{text}'")
                                await option.click()
                                await asyncio.sleep(1)
                                logger.info(f"✅ Successfully clicked option: '{text}'")
                                return True

                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue

            # Fallback: try clicking any recently appeared clickable elements
            logger.info("🔍 Fallback: looking for any new clickable elements...")
            try:
                new_clickables = await browser.page.query_selector_all(
                    'button:visible, [role="button"]:visible, [tabindex="0"]:visible'
                )
                for clickable in new_clickables:
                    text = await clickable.text_content()
                    text = text.strip() if text else ""

                    if text and 0 < len(text) < 50:
                        logger.info(f"🎯 Trying fallback click on: '{text}'")
                        await clickable.click()
                        await asyncio.sleep(1)
                        logger.info(f"✅ Fallback click successful: '{text}'")
                        return True

            except Exception as e:
                logger.debug(f"Fallback clicking failed: {e}")

            logger.warning("⚠️ No clickable dropdown options found")
            # Close dropdown
            await browser.page.keyboard.press("Escape")
            await asyncio.sleep(0.5)
            return False

        except Exception as e:
            logger.warning(f"Error selecting dropdown option: {e}")
            return False

    async def _save_enhanced_dropdown_debug(self, browser: BrowserManager):
        """Save comprehensive debug information about dropdown detection failure."""
        if not self.debug_mode:
            return

        try:
            logger.info("🐛 Saving enhanced dropdown debug information...")

            # Get comprehensive page analysis
            debug_info = await browser.page.evaluate("""
                () => {
                    const analysis = {
                        pageInfo: {
                            url: window.location.href,
                            title: document.title,
                            width: window.innerWidth,
                            height: window.innerHeight
                        },
                        dropdownCandidates: [],
                        allButtons: [],
                        selectElements: [],
                        comboboxElements: [],
                        elementsWithCategory: []
                    };

                    // Find all potential dropdown candidates
                    const allElements = document.querySelectorAll('*');
                    allElements.forEach((el, index) => {
                        const text = el.textContent || el.innerText || '';
                        const className = el.className || '';
                        const tagName = el.tagName.toLowerCase();

                        // Dropdown candidates
                        if (text.toLowerCase().includes('category') ||
                            text.toLowerCase().includes('result') ||
                            (typeof className === 'string' && className.includes('category')) ||
                            (typeof className === 'string' && className.includes('dropdown')) ||
                            tagName === 'select') {
                            analysis.dropdownCandidates.push({
                                tagName: el.tagName,
                                className: className,
                                id: el.id || '',
                                text: text.substring(0, 100),
                                visible: el.offsetHeight > 0 && el.offsetWidth > 0,
                                rect: el.getBoundingClientRect(),
                                attributes: Array.from(el.attributes).map(attr => `${attr.name}="${attr.value}"`),
                                outerHTML: el.outerHTML.substring(0, 500)
                            });
                        }

                        // All buttons
                        if (tagName === 'button') {
                            analysis.allButtons.push({
                                text: text.substring(0, 50),
                                className: className,
                                visible: el.offsetHeight > 0 && el.offsetWidth > 0,
                                rect: el.getBoundingClientRect(),
                                role: el.getAttribute('role'),
                                ariaHaspopup: el.getAttribute('aria-haspopup'),
                                ariaExpanded: el.getAttribute('aria-expanded')
                            });
                        }

                        // Select elements
                        if (tagName === 'select') {
                            analysis.selectElements.push({
                                text: text.substring(0, 50),
                                className: className,
                                visible: el.offsetHeight > 0 && el.offsetWidth > 0,
                                options: Array.from(el.options || []).map(opt => opt.text)
                            });
                        }

                        // Combobox elements
                        if (el.getAttribute('role') === 'combobox') {
                            analysis.comboboxElements.push({
                                tagName: el.tagName,
                                text: text.substring(0, 50),
                                className: className,
                                visible: el.offsetHeight > 0 && el.offsetWidth > 0,
                                rect: el.getBoundingClientRect()
                            });
                        }

                        // Elements containing "category"
                        if (text.toLowerCase().includes('category')) {
                            analysis.elementsWithCategory.push({
                                tagName: el.tagName,
                                text: text.substring(0, 100),
                                className: className,
                                visible: el.offsetHeight > 0 && el.offsetWidth > 0,
                                rect: el.getBoundingClientRect()
                            });
                        }
                    });

                    return analysis;
                }
            """)

            # Save debug information
            debug_dir = (
                Path("debug") / "exaai" / datetime.now().strftime("%Y%m%d_%H%M%S")
            )
            debug_dir.mkdir(parents=True, exist_ok=True)

            # Save comprehensive analysis
            debug_path = debug_dir / "05_enhanced_dropdown_debug.json"
            with open(debug_path, "w", encoding="utf-8") as f:
                import json

                json.dump(debug_info, f, indent=2, ensure_ascii=False)

            # Save human-readable summary
            summary_path = debug_dir / "05_dropdown_debug_summary.txt"
            with open(summary_path, "w", encoding="utf-8") as f:
                f.write("ENHANCED DROPDOWN DETECTION DEBUG SUMMARY\n")
                f.write("=" * 60 + "\n\n")

                f.write(f"Page URL: {debug_info['pageInfo']['url']}\n")
                f.write(f"Page Title: {debug_info['pageInfo']['title']}\n")
                f.write(
                    f"Page Dimensions: {debug_info['pageInfo']['width']}x{debug_info['pageInfo']['height']}\n\n"
                )

                f.write(
                    f"DROPDOWN CANDIDATES: {len(debug_info['dropdownCandidates'])}\n"
                )
                f.write("-" * 40 + "\n")
                for i, candidate in enumerate(debug_info["dropdownCandidates"]):
                    f.write(f"Candidate {i + 1}:\n")
                    f.write(f"  Tag: {candidate['tagName']}\n")
                    f.write(f"  Class: {candidate['className']}\n")
                    f.write(f"  Text: {candidate['text']}\n")
                    f.write(f"  Visible: {candidate['visible']}\n")
                    f.write(f"  Position: {candidate['rect']}\n")
                    f.write(f"  Attributes: {', '.join(candidate['attributes'])}\n")
                    f.write("\n")

                f.write(f"ALL BUTTONS: {len(debug_info['allButtons'])}\n")
                f.write("-" * 40 + "\n")
                visible_buttons = [
                    btn for btn in debug_info["allButtons"] if btn["visible"]
                ]
                f.write(f"Visible buttons: {len(visible_buttons)}\n")
                for i, btn in enumerate(visible_buttons[:10]):  # Show first 10
                    f.write(
                        f"  Button {i + 1}: '{btn['text']}' (class: {btn['className']})\n"
                    )

                f.write(f"\nCOMBOBOX ELEMENTS: {len(debug_info['comboboxElements'])}\n")
                f.write("-" * 40 + "\n")
                for i, combo in enumerate(debug_info["comboboxElements"]):
                    f.write(
                        f"  Combobox {i + 1}: {combo['tagName']} - '{combo['text']}'\n"
                    )

                f.write(
                    f"\nELEMENTS WITH 'CATEGORY': {len(debug_info['elementsWithCategory'])}\n"
                )
                f.write("-" * 40 + "\n")
                for i, elem in enumerate(debug_info["elementsWithCategory"]):
                    f.write(
                        f"  Element {i + 1}: {elem['tagName']} - '{elem['text']}'\n"
                    )

            logger.info(f"🐛 Enhanced debug saved: {debug_path}")
            logger.info(f"🐛 Debug summary saved: {summary_path}")

        except Exception as e:
            logger.warning(f"Failed to save enhanced debug info: {e}")

    async def _simulate_search_activity(self, browser: BrowserManager) -> bool:
        """Simulate human search activity on the dashboard. Returns True if successful."""
        try:
            # Navigate to search page by clicking on navigation links
            logger.info("🔍 Looking for Search/Playground navigation...")

            # Try to find and click the Search link under API Playground section
            search_nav_selectors = [
                "a.command-bar-nav-search",  # Direct class selector
                'a[href="/playground/search"]',  # Direct href selector
                'a[title="Search"]',  # Title attribute
            ]

            search_link_found = False
            for selector in search_nav_selectors:
                try:
                    nav_link = await browser.page.query_selector(selector)
                    if nav_link and await nav_link.is_visible():
                        await nav_link.click()
                        logger.info(f"✅ Clicked navigation link: {selector}")
                        await asyncio.sleep(3)
                        search_link_found = True
                        break
                except:
                    continue

            if not search_link_found:
                # Fallback: Navigate directly to search page
                logger.info("⚠️ No navigation links found, using direct URL as fallback")
                search_url = "https://dashboard.exa.ai/playground/search?q=blog%20post%20about%20AI&filters=%7B%22text%22%3A%22true%22%2C%22type%22%3A%22auto%22%7D"
                await browser.goto(search_url)
                logger.info("✅ Navigated directly to Search playground")

            await asyncio.sleep(3)
            await self._debug_screenshot(browser, "05_search_page")

            # Save complete page source for dropdown inspection
            if self.debug_mode:
                try:
                    page_source = await browser.page.content()
                    debug_dir = (
                        Path("debug")
                        / "exaai"
                        / datetime.now().strftime("%Y%m%d_%H%M%S")
                    )
                    debug_dir.mkdir(parents=True, exist_ok=True)
                    source_path = debug_dir / "05_search_page_complete_source.html"
                    with open(source_path, "w", encoding="utf-8") as f:
                        f.write(page_source)
                    logger.info(f"🐛 Complete page source saved: {source_path}")
                except Exception as e:
                    logger.warning(f"Failed to save complete page source: {e}")

            # Look for category options and select one
            logger.info("🏃 Looking for search filters and Run button...")

            # Try to interact with Search Type dropdown (Auto -> Neural/Keyword/etc)
            try:
                search_type_button = await browser.page.query_selector(
                    'button[role="combobox"]:has-text("Auto")'
                )
                if search_type_button and await search_type_button.is_visible():
                    await search_type_button.click()
                    await asyncio.sleep(1)

                    # Try to select a non-default option
                    search_type_options = [
                        'text="Neural search"',
                        'text="Keyword search"',
                        'text="Similar"',
                    ]

                    for option in search_type_options:
                        try:
                            option_element = await browser.page.query_selector(option)
                            if option_element and await option_element.is_visible():
                                await option_element.click()
                                logger.info(f"✅ Selected search type: {option}")
                                await asyncio.sleep(1)
                                break
                        except:
                            continue
            except Exception as e:
                logger.debug(f"Could not interact with search type dropdown: {e}")

            # Try to interact with Result Category dropdown using enhanced multi-strategy approach
            try:
                logger.info("🎯 Looking for Result Category dropdown...")
                category_selected = await self._select_result_category_enhanced(browser)
                if not category_selected:
                    logger.warning(
                        "⚠️ Could not select any Result Category option with enhanced method"
                    )

            except Exception as e:
                logger.debug(f"Could not interact with result category dropdown: {e}")

            # Click Run button with multiple detection strategies
            logger.info("🔍 Looking for Run button...")
            run_button = await self._find_run_button(browser)
            if run_button:
                await run_button.click()
                logger.info("✅ Clicked 'Run' button to simulate search")

                # Wait at least 10 seconds for search to complete
                logger.info("⏳ Waiting for search to complete...")
                await self.randomizer.delay(10, 15)  # Wait 10-15 seconds

                await self._debug_screenshot(browser, "06_after_run")
                return True  # Search was successful
            else:
                logger.warning("⚠️ No 'Run' button found on Search page")
                await self._debug_screenshot(browser, "06_no_run_button_found")
                return False  # Search failed - no Run button

        except Exception as e:
            logger.warning(f"Could not complete search simulation: {e}")
            return False  # Search failed due to exception

    async def _extract_api_key_via_clipboard(
        self, browser: BrowserManager
    ) -> Optional[str]:
        """Extract API key using clipboard method."""
        try:
            logger.info("📋 Clicking on masked API key area to copy to clipboard...")
            await self._debug_screenshot(browser, "07a_before_copy")

            # Strategy: Click directly on the masked key area to copy to clipboard
            masked_key_selectors = [
                'tr:has-text("default") span:has-text("•")',  # Masked key span with dots
                'tr:has-text("default") [data-testid*="secret-key"]',  # Secret key element
                'tr:has-text("default") td:nth-child(2) span',  # Second column span
                'tr:has-text("default") td:nth-child(2)',  # Second column
                'tr:has-text("default") span:contains("*")',  # Any span with asterisks
            ]

            api_key = None
            copy_successful = False

            for i, selector in enumerate(masked_key_selectors):
                try:
                    masked_element = await browser.page.query_selector(selector)
                    if masked_element and await masked_element.is_visible():
                        logger.info(
                            f"✅ Found masked key element with selector #{i + 1}: {selector}"
                        )

                        # Click on the masked area to copy
                        await masked_element.click()
                        logger.info("✅ Clicked on masked key area")
                        await asyncio.sleep(2)

                        # Check clipboard immediately
                        try:
                            # Handle different browser engines for clipboard access
                            if browser.browser_engine == "firefox":
                                # Firefox: Try multiple methods
                                clipboard_content = await self._firefox_clipboard_read(
                                    browser.page
                                )
                            elif browser.browser_engine in ["chromium", "webkit"]:
                                # Chromium/WebKit: Standard clipboard API (requires HTTPS)
                                try:
                                    clipboard_content = await browser.page.evaluate(
                                        "() => navigator.clipboard.readText()"
                                    )
                                except:
                                    # Fallback for non-HTTPS pages or blocked clipboard
                                    clipboard_content = (
                                        await self._universal_clipboard_read(
                                            browser.page
                                        )
                                    )
                            else:
                                # Fallback for unknown engines
                                clipboard_content = (
                                    await self._universal_clipboard_read(browser.page)
                                )

                            if (
                                clipboard_content
                                and len(clipboard_content) > 30
                                and "•" not in clipboard_content
                                and "*" not in clipboard_content
                            ):
                                api_key = clipboard_content.strip()
                                copy_successful = True
                                logger.info(
                                    f"🎯 Successfully copied FULL API key from clipboard: {len(api_key)} chars"
                                )
                                break
                            else:
                                logger.info(
                                    f"Clipboard content not a full key: {clipboard_content[:20] if clipboard_content else 'None'}..."
                                )
                        except Exception as e:
                            logger.debug(
                                f"Could not read clipboard after clicking: {e}"
                            )
                except Exception as e:
                    logger.debug(
                        f"Could not click masked area with selector {selector}: {e}"
                    )
                    continue

            if not copy_successful:
                # Fallback: Try clicking any button in the default row
                logger.info(
                    "⚠️ Direct masked area click didn't work, trying buttons in default row..."
                )
                button_selectors = [
                    'tr:has-text("default") button',  # Any button in default row
                    'tr:has-text("default") [role="button"]',  # Any role button in default row
                    'tr:has-text("default") svg',  # Any SVG icon in default row
                ]

                for selector in button_selectors:
                    try:
                        button_element = await browser.page.query_selector(selector)
                        if button_element and await button_element.is_visible():
                            await button_element.click()
                            logger.info(f"✅ Clicked button: {selector}")
                            await asyncio.sleep(2)

                            # Handle different browser engines for clipboard access
                            if browser.browser_engine == "firefox":
                                # Firefox: Try multiple methods
                                clipboard_content = await self._firefox_clipboard_read(
                                    browser.page
                                )
                            elif browser.browser_engine in ["chromium", "webkit"]:
                                # Chromium/WebKit: Standard clipboard API (requires HTTPS)
                                try:
                                    clipboard_content = await browser.page.evaluate(
                                        "() => navigator.clipboard.readText()"
                                    )
                                except:
                                    # Fallback for non-HTTPS pages or blocked clipboard
                                    clipboard_content = (
                                        await self._universal_clipboard_read(
                                            browser.page
                                        )
                                    )
                            else:
                                # Fallback for unknown engines
                                clipboard_content = (
                                    await self._universal_clipboard_read(browser.page)
                                )

                            if (
                                clipboard_content
                                and len(clipboard_content) > 30
                                and "•" not in clipboard_content
                                and "*" not in clipboard_content
                            ):
                                api_key = clipboard_content.strip()
                                copy_successful = True
                                logger.info(
                                    f"🎯 Got FULL API key from clipboard via button: {len(api_key)} chars"
                                )
                                break
                    except:
                        continue

            if not copy_successful:
                await self._debug_screenshot(browser, "08_copy_failed")
                logger.error(
                    "Could not copy API key to clipboard - still appears to be masked"
                )
                return None

            if len(api_key) < 20:
                logger.error(
                    f"API key too short ({len(api_key)} chars) - may still be masked"
                )
                return None

            return api_key

        except Exception as e:
            logger.error(f"Error extracting API key via clipboard: {e}")
            return None

    async def _firefox_clipboard_read(self, page) -> Optional[str]:
        """Firefox-specific clipboard reading with multiple fallback methods."""
        try:
            logger.info("🦊 Starting Firefox clipboard reading...")

            # Method 1: Try the enhanced clipboard API we injected
            try:
                clipboard_content = await page.evaluate(
                    "() => navigator.clipboard.readText()"
                )
                if clipboard_content and len(clipboard_content) > 10:
                    logger.info(
                        f"🦊 Firefox Method 1 (enhanced API) success: {len(clipboard_content)} chars"
                    )
                    return clipboard_content
                else:
                    logger.debug(f"🦊 Firefox Method 1 returned: '{clipboard_content}'")
            except Exception as e:
                logger.debug(f"🦊 Firefox Method 1 failed: {e}")

            # Method 2: Try to get from the global clipboard content we capture
            try:
                global_content = await page.evaluate(
                    "() => window.clipboardContent || ''"
                )
                if global_content and len(global_content) > 10:
                    logger.info(
                        f"🦊 Firefox Method 2 (window.clipboardContent) success: {len(global_content)} chars"
                    )
                    return global_content
                else:
                    logger.debug(f"🦊 Firefox Method 2 returned: '{global_content}'")
            except Exception as e:
                logger.debug(f"🦊 Firefox Method 2 failed: {e}")

            # Method 2b: Try lastCopiedContent
            try:
                last_content = await page.evaluate(
                    "() => window.lastCopiedContent || ''"
                )
                if last_content and len(last_content) > 10:
                    logger.info(
                        f"🦊 Firefox Method 2b (lastCopiedContent) success: {len(last_content)} chars"
                    )
                    return last_content
                else:
                    logger.debug(f"🦊 Firefox Method 2b returned: '{last_content}'")
            except Exception as e:
                logger.debug(f"🦊 Firefox Method 2b failed: {e}")

            # Method 3: Try execCommand paste in a temporary textarea
            try:
                paste_content = await page.evaluate("""
                    () => {
                        try {
                            console.log('🦊 Trying execCommand paste method');
                            const textArea = document.createElement('textarea');
                            textArea.style.position = 'fixed';
                            textArea.style.left = '-999999px';
                            textArea.style.top = '-999999px';
                            document.body.appendChild(textArea);
                            textArea.focus();
                            textArea.select();

                            if (document.execCommand('paste')) {
                                const content = textArea.value;
                                document.body.removeChild(textArea);
                                console.log('🦊 execCommand paste success:', content.length, 'chars');
                                return content;
                            }

                            document.body.removeChild(textArea);
                            console.log('🦊 execCommand paste failed');
                            return '';
                        } catch (e) {
                            console.log('🦊 execCommand paste error:', e);
                            return '';
                        }
                    }
                """)

                if paste_content and len(paste_content) > 10:
                    logger.info(
                        f"🦊 Firefox Method 3 (execCommand paste) success: {len(paste_content)} chars"
                    )
                    return paste_content
                else:
                    logger.debug(f"🦊 Firefox Method 3 returned: '{paste_content}'")
            except Exception as e:
                logger.debug(f"🦊 Firefox Method 3 failed: {e}")

            # Method 4: Try to get selected text
            try:
                selected_text = await page.evaluate(
                    "() => window.getSelection().toString()"
                )
                if selected_text and len(selected_text) > 10:
                    logger.info(
                        f"🦊 Firefox Method 4 (selected text) success: {len(selected_text)} chars"
                    )
                    return selected_text
                else:
                    logger.debug(f"🦊 Firefox Method 4 returned: '{selected_text}'")
            except Exception as e:
                logger.debug(f"🦊 Firefox Method 4 failed: {e}")

            # Method 5: Try to trigger a copy event and capture it
            try:
                trigger_copy_result = await page.evaluate("""
                    () => {
                        try {
                            console.log('🦊 Trying to trigger copy event');
                            // Try to find any visible text that might be an API key
                            const elements = document.querySelectorAll('*');
                            for (let el of elements) {
                                const text = el.textContent || '';
                                if (text.length > 20 && (text.includes('-') || text.includes('_'))) {
                                    // This might be an API key, try to select and copy it
                                    const range = document.createRange();
                                    range.selectNodeContents(el);
                                    const selection = window.getSelection();
                                    selection.removeAllRanges();
                                    selection.addRange(range);

                                    if (document.execCommand('copy')) {
                                        const copiedText = selection.toString();
                                        console.log('🦊 Triggered copy success:', copiedText.length, 'chars');
                                        return copiedText;
                                    }
                                }
                            }
                            console.log('🦊 No suitable text found for copy trigger');
                            return '';
                        } catch (e) {
                            console.log('🦊 Copy trigger error:', e);
                            return '';
                        }
                    }
                """)

                if trigger_copy_result and len(trigger_copy_result) > 10:
                    logger.info(
                        f"🦊 Firefox Method 5 (trigger copy) success: {len(trigger_copy_result)} chars"
                    )
                    return trigger_copy_result
                else:
                    logger.debug(
                        f"🦊 Firefox Method 5 returned: '{trigger_copy_result}'"
                    )
            except Exception as e:
                logger.debug(f"🦊 Firefox Method 5 failed: {e}")

            logger.warning("🦊 All Firefox clipboard methods failed")
            return None

        except Exception as e:
            logger.error(f"Firefox clipboard read error: {e}")
            return None

    async def _universal_clipboard_read(self, page) -> Optional[str]:
        """Universal clipboard reading that works across all browser engines."""
        try:
            # This is essentially the same as Firefox method but works everywhere
            return await self._firefox_clipboard_read(page)
        except Exception as e:
            logger.error(f"Universal clipboard read error: {e}")
            return None

    async def _select_result_category_enhanced(self, browser: BrowserManager) -> bool:
        """Enhanced Result Category dropdown selection with multiple strategies for JavaScript-controlled dropdowns."""
        logger.info(
            "🎯 ENHANCED: Attempting to select Result Category dropdown with multiple strategies"
        )

        # Strategy 1: Try comprehensive JavaScript-based approach (most reliable for ExaAI)
        category_selected = await self._try_comprehensive_javascript_dropdown(browser)
        if category_selected:
            return True

        # Strategy 2: Try mouse coordinate-based clicking
        category_selected = await self._try_mouse_coordinate_dropdown_click(browser)
        if category_selected:
            return True

        # Strategy 3: Try force clicking approaches
        category_selected = await self._try_force_dropdown_click_methods(browser)
        if category_selected:
            return True

        # Strategy 4: Try standard Playwright dropdown methods
        category_selected = await self._try_standard_dropdown_methods(browser)
        if category_selected:
            return True

        # Strategy 5: Try alternative element targeting
        category_selected = await self._try_alternative_dropdown_methods(browser)
        if category_selected:
            return True

        logger.error("❌ All Result Category dropdown strategies failed")
        return False

    async def _try_comprehensive_javascript_dropdown(self, browser):
        """Comprehensive JavaScript-based dropdown interaction - most reliable for ExaAI."""
        logger.info("🔍 Strategy 1: Comprehensive JavaScript dropdown interaction")
        page = browser.page

        try:
            # Take debug screenshot before attempting dropdown interaction
            await self._debug_screenshot(browser, "before_dropdown_interaction")

            # Execute comprehensive JavaScript dropdown interaction
            result = await page.evaluate("""
                () => {
                    console.log('🎯 Starting comprehensive ExaAI dropdown interaction...');
                    
                    // Available Result Category options for ExaAI
                    const availableOptions = ['Company', 'Research Paper', 'News Article', 'PDF', 'Github', 'Personal Site', 'LinkedIn Profile', 'Financial Report'];
                    let selectedOption = null;
                    
                    // Step 1: Find and click the Result Category dropdown button
                    console.log('Step 1: Looking for Result Category dropdown button...');
                    
                    // ExaAI-specific selectors for the dropdown button
                    const dropdownSelectors = [
                        'button.command-bar-search-category',
                        'button[data-sentry-element="SelectTrigger"]',
                        'button[role="combobox"]',
                        '.command-bar-search-category',
                        '*[class*="search-category"] button',
                        '*:has-text("Result category") button',
                        '*:has-text("Result category") + * button'
                    ];
                    
                    let dropdownButton = null;
                    let usedSelector = null;
                    
                    for (let selector of dropdownSelectors) {
                        try {
                            const elements = document.querySelectorAll(selector);
                            console.log(`Selector "${selector}" found ${elements.length} elements`);
                            
                            for (let element of elements) {
                                if (element.offsetParent !== null) { // visible
                                    dropdownButton = element;
                                    usedSelector = selector;
                                    console.log(`✅ Found visible dropdown button with: ${selector}`);
                                    break;
                                }
                            }
                            if (dropdownButton) break;
                        } catch (e) {
                            console.log(`Selector "${selector}" failed:`, e.message);
                            continue;
                        }
                    }
                    
                    if (!dropdownButton) {
                        console.log('❌ No dropdown button found');
                        return { success: false, message: 'No dropdown button found' };
                    }
                    
                    // Step 2: Click the dropdown button to open it
                    console.log('Step 2: Clicking dropdown button to open...');
                    try {
                        // Multiple click methods for reliability
                        dropdownButton.click();
                        dropdownButton.dispatchEvent(new Event('click', { bubbles: true }));
                        dropdownButton.dispatchEvent(new Event('mousedown', { bubbles: true }));
                        dropdownButton.dispatchEvent(new Event('mouseup', { bubbles: true }));
                        
                        console.log('✅ Dropdown button clicked successfully');
                    } catch (e) {
                        console.log('❌ Failed to click dropdown button:', e.message);
                        return { success: false, message: 'Failed to click dropdown button' };
                    }
                    
                    // Step 3: Wait briefly for dropdown to open, then find and click an option
                    console.log('Step 3: Waiting for dropdown to open and looking for options...');
                    
                    // Give dropdown time to open (JavaScript timing)
                    return new Promise((resolve) => {
                        setTimeout(() => {
                            console.log('Searching for dropdown options...');
                            
                            // Try multiple approaches to find and select an option
                            let optionClicked = false;
                            
                            // Approach 1: Look for role="option" elements
                            const roleOptions = document.querySelectorAll('[role="option"]');
                            console.log(`Found ${roleOptions.length} role="option" elements`);
                            
                            for (let option of roleOptions) {
                                if (option.offsetParent !== null) { // visible
                                    const optionText = option.textContent.trim();
                                    if (availableOptions.includes(optionText)) {
                                        try {
                                            option.click();
                                            selectedOption = optionText;
                                            optionClicked = true;
                                            console.log(`✅ Selected option via role: ${optionText}`);
                                            break;
                                        } catch (e) {
                                            console.log(`Failed to click role option ${optionText}:`, e.message);
                                        }
                                    }
                                }
                            }
                            
                            // Approach 2: Look for any elements with option text (if role approach failed)
                            if (!optionClicked) {
                                console.log('Trying text-based option search...');
                                for (let optionText of availableOptions) {
                                    const elements = document.querySelectorAll('*');
                                    for (let element of elements) {
                                        if (element.textContent && 
                                            element.textContent.trim() === optionText && 
                                            element.offsetParent !== null) {
                                            try {
                                                element.click();
                                                selectedOption = optionText;
                                                optionClicked = true;
                                                console.log(`✅ Selected option via text search: ${optionText}`);
                                                break;
                                            } catch (e) {
                                                console.log(`Failed to click text option ${optionText}:`, e.message);
                                            }
                                        }
                                    }
                                    if (optionClicked) break;
                                }
                            }
                            
                            // Approach 3: Try clicking the first visible clickable element in dropdown (fallback)
                            if (!optionClicked) {
                                console.log('Trying fallback approach - clicking first available option...');
                                const clickableElements = document.querySelectorAll('div, span, li, button');
                                for (let element of clickableElements) {
                                    const text = element.textContent.trim();
                                    if (availableOptions.includes(text) && element.offsetParent !== null) {
                                        try {
                                            element.click();
                                            selectedOption = text;
                                            optionClicked = true;
                                            console.log(`✅ Selected option via fallback: ${text}`);
                                            break;
                                        } catch (e) {
                                            console.log(`Failed to click fallback option ${text}:`, e.message);
                                        }
                                    }
                                }
                            }
                            
                            if (optionClicked) {
                                resolve({ 
                                    success: true, 
                                    selectedOption: selectedOption,
                                    method: 'comprehensive-javascript',
                                    dropdownSelector: usedSelector
                                });
                            } else {
                                console.log('❌ Could not find or click any dropdown options');
                                resolve({ 
                                    success: false, 
                                    message: 'No clickable options found',
                                    foundRoleOptions: roleOptions.length
                                });
                            }
                        }, 2000); // Wait 2 seconds for dropdown to fully open
                    });
                }
            """)

            if result.get("success"):
                selected_option = result.get("selectedOption", "Unknown")
                method = result.get("method", "javascript")
                dropdown_selector = result.get("dropdownSelector", "unknown")

                logger.info(
                    f"✅ STRATEGY 1 SUCCESS: Selected '{selected_option}' using {method}"
                )
                logger.info(f"🎯 Dropdown found with selector: {dropdown_selector}")

                # Take debug screenshot after successful selection
                await self._debug_screenshot(browser, "after_dropdown_selection")
                await asyncio.sleep(1)  # Brief pause to let UI update
                return True
            else:
                error_msg = result.get("message", "Unknown error")
                found_options = result.get("foundRoleOptions", 0)
                logger.warning(
                    f"⚠️ STRATEGY 1 FAILED: {error_msg} (found {found_options} role options)"
                )

                # Take debug screenshot to understand why it failed
                await self._debug_screenshot(browser, "dropdown_selection_failed")

        except Exception as e:
            logger.error(f"JavaScript comprehensive approach failed: {e}")
            await self._debug_screenshot(browser, "dropdown_javascript_error")

        return False

    async def _try_mouse_coordinate_dropdown_click(self, browser):
        """Try mouse coordinate-based clicking for custom dropdowns."""
        logger.info("🔍 Strategy 2: Enhanced dropdown detection and clicking")
        page = browser.page

        # Method 1A: Look for the specific Result Category dropdown button
        try:
            logger.info("🔍 Method 1A: Looking for Result Category dropdown button")

            # Primary selectors for ExaAI Result Category dropdown
            dropdown_selectors = [
                "button.command-bar-search-category",  # Primary ExaAI selector
                'button[data-sentry-element="SelectTrigger"]',  # Sentry tracking selector
                'button[role="combobox"]',  # ARIA combobox role
                'div:has-text("Result category") button',  # Button near "Result category" text
                '*:has-text("Result category") + button',  # Button after category text
                '*:has-text("Result category") button',  # Button within category container
            ]

            dropdown_button = None

            for selector in dropdown_selectors:
                try:
                    dropdown_button = await page.query_selector(selector)
                    if dropdown_button and await dropdown_button.is_visible():
                        logger.info(
                            f"✅ Found Result Category dropdown with selector: {selector}"
                        )
                        break
                except:
                    continue

            if dropdown_button:
                # Click to open dropdown
                await dropdown_button.click(force=True)
                logger.info("✅ STRATEGY 1A: Successfully clicked dropdown button")
                await asyncio.sleep(2)  # Wait for dropdown to open

                # Try to select an option
                option_selected = await self._select_dropdown_option(browser)
                if option_selected:
                    return True

        except Exception as e:
            logger.debug(f"Dropdown button click failed: {e}")

        # Method 1B: JavaScript-based comprehensive dropdown detection
        try:
            logger.info("🔍 Method 1B: JavaScript comprehensive dropdown detection")

            result = await page.evaluate("""
                () => {
                    console.log('Starting comprehensive dropdown search...');

                    // Strategy 1: Look for Result Category dropdown buttons
                    const dropdownSelectors = [
                        'button.command-bar-search-category',
                        'button[data-sentry-element="SelectTrigger"]',
                        'button[role="combobox"]'
                    ];
                    
                    for (let selector of dropdownSelectors) {
                        const buttons = document.querySelectorAll(selector);
                        console.log('Found', buttons.length, 'buttons for selector:', selector);

                        for (let button of buttons) {
                            if (button.offsetParent !== null) { // visible
                                console.log('Clicking dropdown button:', selector);
                                button.click();
                                return { success: true, method: 'dropdown-button', element: selector };
                            }
                        }
                    }

                    // Strategy 2: Look for elements with "Result category" text and find nearby clickables
                    const categoryElements = Array.from(document.querySelectorAll('*')).filter(el =>
                        el.textContent && el.textContent.includes('Result category')
                    );
                    console.log('Found', categoryElements.length, 'elements with Result category text');

                    for (let categoryEl of categoryElements) {
                        // Look for clickable elements in the same container
                        const container = categoryEl.closest('div, span') || categoryEl;
                        const clickables = container.querySelectorAll(
                            'button, [role="combobox"], [role="button"], ' +
                            '*[class*="dropdown"], *[class*="select"], ' +
                            '[onclick], [data-testid*="select"]'
                        );

                        for (let clickable of clickables) {
                            if (clickable.offsetParent !== null) {
                                console.log('Clicking dropdown near Result category text');
                                clickable.click();
                                return { success: true, method: 'category-nearby', element: clickable.tagName };
                            }
                        }
                    }

                    return { success: false, message: 'No clickable dropdown found' };
                }
            """)

            if result.get("success"):
                logger.info(
                    f"✅ STRATEGY 1B: JavaScript found and clicked dropdown: {result}"
                )
                await asyncio.sleep(2)

                # Try to select an option
                option_selected = await self._select_dropdown_option(browser)
                if option_selected:
                    return True
            else:
                logger.warning(
                    f"⚠️ STRATEGY 1B: JavaScript search failed: {result.get('message')}"
                )

        except Exception as e:
            logger.debug(f"JavaScript comprehensive search failed: {e}")

        return False

    async def _try_standard_dropdown_methods(self, browser):
        """Try standard Playwright dropdown interaction methods."""
        logger.info("🔍 Strategy 2: Standard Playwright dropdown methods")
        page = browser.page

        try:
            # Method 2A: Look for select elements
            select_elements = page.locator("select")
            select_count = await select_elements.count()
            logger.info(f"Found {select_count} select elements")

            if select_count > 0:
                first_select = select_elements.first
                # Try to select the first non-default option
                await first_select.select_option(index=1)
                logger.info(
                    "✅ STRATEGY 2A: Successfully selected option from select element"
                )
                return True

        except Exception as e:
            logger.debug(f"Select element method failed: {e}")

        try:
            # Method 2B: Try combobox role elements
            comboboxes = page.locator('[role="combobox"]')
            combobox_count = await comboboxes.count()
            logger.info(f"Found {combobox_count} combobox elements")

            if combobox_count > 0:
                first_combobox = comboboxes.first
                await first_combobox.click(force=True)
                logger.info("✅ STRATEGY 2B: Successfully clicked combobox")
                await asyncio.sleep(2)

                # Try to select an option
                option_selected = await self._select_dropdown_option(browser)
                if option_selected:
                    return True

        except Exception as e:
            logger.debug(f"Combobox method failed: {e}")

        return False

    async def _try_force_dropdown_click_methods(self, browser):
        """Try force clicking methods for stubborn dropdowns."""
        logger.info("🔍 Strategy 3: Force clicking methods")
        page = browser.page

        try:
            # Method 3A: Force click any button that might be a dropdown
            dropdown_candidates = [
                "button.command-bar-search-category",
                'button[data-sentry-element="SelectTrigger"]',
                'button[role="combobox"]',
            ]

            for candidate in dropdown_candidates:
                try:
                    buttons = page.locator(candidate)
                    if await buttons.count() > 0:
                        first_button = buttons.first
                        await first_button.click(force=True)
                        logger.info(f"✅ STRATEGY 3A: Force clicked {candidate}")
                        await asyncio.sleep(2)

                        # Try to select an option
                        option_selected = await self._select_dropdown_option(browser)
                        if option_selected:
                            return True

                except Exception as e:
                    logger.debug(f"Force click {candidate} failed: {e}")
                    continue

        except Exception as e:
            logger.debug(f"Force click method failed: {e}")

        return False

    async def _try_javascript_dropdown_methods(self, browser):
        """Try JavaScript evaluation methods for programmatic dropdown control."""
        logger.info("🔍 Strategy 4: JavaScript evaluation methods")
        page = browser.page

        try:
            # Method 4A: Direct JavaScript dropdown interaction
            result = await page.evaluate("""
                () => {
                    // Try to find and click dropdown buttons
                    const selectors = [
                        'button.command-bar-search-category',
                        'button[data-sentry-element="SelectTrigger"]',
                        'button[role="combobox"]'
                    ];
                    
                    for (let selector of selectors) {
                        const button = document.querySelector(selector);
                        if (button && button.offsetParent !== null) {
                            // Trigger various events
                            button.click();
                            
                            const clickEvent = new Event('click', { bubbles: true });
                            button.dispatchEvent(clickEvent);
                            
                            const mousedownEvent = new Event('mousedown', { bubbles: true });
                            button.dispatchEvent(mousedownEvent);
                            
                            return true;
                        }
                    }
                    return false;
                }
            """)

            if result:
                logger.info(
                    "✅ STRATEGY 4A: Successfully clicked dropdown using JavaScript evaluation"
                )
                await asyncio.sleep(2)

                # Try to select an option
                option_selected = await self._select_dropdown_option(browser)
                if option_selected:
                    return True

        except Exception as e:
            logger.debug(f"JavaScript evaluation method failed: {e}")

        return False

    async def _try_alternative_dropdown_methods(self, browser):
        """Try alternative element targeting and event dispatch."""
        logger.info("🔍 Strategy 5: Alternative element targeting methods")
        page = browser.page

        try:
            # Method 5A: Try clicking elements near "Result category" text
            category_text_elements = page.locator('*:has-text("Result category")')
            if await category_text_elements.count() > 0:
                first_element = category_text_elements.first
                await first_element.click(force=True)
                logger.info("✅ STRATEGY 5A: Clicked Result category text element")
                await asyncio.sleep(2)

                # Try to select an option
                option_selected = await self._select_dropdown_option(browser)
                if option_selected:
                    return True

        except Exception as e:
            logger.debug(f"Alternative text element clicking failed: {e}")

        try:
            # Method 5B: Try using keyboard navigation
            await page.keyboard.press("Tab")  # Navigate to next focusable element
            await asyncio.sleep(0.5)
            await page.keyboard.press("Enter")  # Try to activate dropdown
            await asyncio.sleep(2)

            # Try to select an option with keyboard
            await page.keyboard.press("ArrowDown")  # Move to first option
            await asyncio.sleep(0.5)
            await page.keyboard.press("Enter")  # Select option

            logger.info("✅ STRATEGY 5B: Attempted keyboard navigation")
            return True  # Assume success with keyboard method

        except Exception as e:
            logger.debug(f"Keyboard navigation method failed: {e}")

        return False

    async def _select_dropdown_option(self, browser):
        """Helper method to select an option from an opened dropdown."""
        logger.info("🔍 Attempting to select dropdown option...")
        page = browser.page

        # Available Result Category options
        option_texts = [
            "Company",
            "Research Paper",
            "News Article",
            "PDF",
            "Github",
            "Personal Site",
            "LinkedIn Profile",
            "Financial Report",
        ]

        # Randomly select one option
        import random

        selected_option_text = random.choice(option_texts)
        logger.info(f"🎯 Targeting option: {selected_option_text}")

        # Try multiple selectors to find and click the option
        option_selectors = [
            f'div:has-text("{selected_option_text}")',
            f'span:has-text("{selected_option_text}")',
            f'[role="option"]:has-text("{selected_option_text}")',
            f'li:has-text("{selected_option_text}")',
            f'button:has-text("{selected_option_text}")',
            f'*[data-value*="{selected_option_text.lower()}"]',
            f'*[value*="{selected_option_text.lower()}"]',
        ]

        for selector in option_selectors:
            try:
                option_element = await page.query_selector(selector)
                if option_element and await option_element.is_visible():
                    await option_element.click(force=True)
                    logger.info(
                        f"✅ Selected Result Category: {selected_option_text} using {selector}"
                    )
                    await asyncio.sleep(1)
                    return True
            except:
                continue

        # Fallback: Try JavaScript-based option selection
        try:
            result = await page.evaluate(f"""
                () => {{
                    const optionTexts = {option_texts};
                    const targetOption = "{selected_option_text}";
                    
                    // Look for any element containing the target option text
                    const allElements = document.querySelectorAll('*');
                    for (let element of allElements) {{
                        if (element.textContent && 
                            element.textContent.trim() === targetOption &&
                            element.offsetParent !== null) {{
                            element.click();
                            return true;
                        }}
                    }}
                    
                    // If exact match failed, try any available option
                    for (let optionText of optionTexts) {{
                        for (let element of allElements) {{
                            if (element.textContent && 
                                element.textContent.trim() === optionText &&
                                element.offsetParent !== null) {{
                                element.click();
                                return optionText;
                            }}
                        }}
                    }}
                    
                    return false;
                }}
            """)

            if result:
                selected_text = (
                    result if isinstance(result, str) else selected_option_text
                )
                logger.info(
                    f"✅ Selected Result Category via JavaScript: {selected_text}"
                )
                return True

        except Exception as e:
            logger.debug(f"JavaScript option selection failed: {e}")

        logger.warning("⚠️ Could not find any visible dropdown options")
        return False

    def _cleanup_debug_screenshots(self):
        """Clean up debug screenshots after a full session is done."""
        if not self.debug_mode:
            return

        try:
            debug_dir = Path("debug") / "exaai"
            if debug_dir.exists():
                # Keep only the most recent session (last 10 screenshots)
                screenshots = list(debug_dir.glob("**/*.png"))
                screenshots.sort(key=lambda p: p.stat().st_mtime, reverse=True)

                # Remove old screenshots, keep last 10
                for screenshot in screenshots[10:]:
                    try:
                        screenshot.unlink()
                        logger.debug(f"Cleaned up old screenshot: {screenshot}")
                    except:
                        pass

                # Remove empty directories
                for folder in debug_dir.rglob("*"):
                    if folder.is_dir() and not any(folder.iterdir()):
                        try:
                            folder.rmdir()
                        except:
                            pass

                logger.info("🧹 Cleaned up debug screenshots")
        except Exception as e:
            logger.warning(f"Failed to clean up debug screenshots: {e}")

    async def _select_result_category_enhanced(self, browser: BrowserManager) -> bool:
        """Enhanced Result Category dropdown selection with multiple strategies for JavaScript-controlled dropdowns."""
        logger.info(
            "🎯 ENHANCED: Attempting to select Result Category dropdown with multiple strategies"
        )

        # Strategy 1: Try comprehensive JavaScript-based approach (most reliable for ExaAI)
        category_selected = await self._try_comprehensive_javascript_dropdown(browser)
        if category_selected:
            return True

        # Strategy 2: Try mouse coordinate-based clicking
        category_selected = await self._try_mouse_coordinate_dropdown_click(browser)
        if category_selected:
            return True

        # Strategy 3: Try force clicking approaches
        category_selected = await self._try_force_dropdown_click_methods(browser)
        if category_selected:
            return True

        # Strategy 4: Try standard Playwright dropdown methods
        category_selected = await self._try_standard_dropdown_methods(browser)
        if category_selected:
            return True

        # Strategy 5: Try alternative element targeting
        category_selected = await self._try_alternative_dropdown_methods(browser)
        if category_selected:
            return True

        logger.error("❌ All Result Category dropdown strategies failed")
        return False

    async def _try_comprehensive_javascript_dropdown(self, browser):
        """Comprehensive JavaScript-based dropdown interaction - most reliable for ExaAI."""
        logger.info("🔍 Strategy 1: Comprehensive JavaScript dropdown interaction")
        page = browser.page
        try:
            # Take debug screenshot before attempting dropdown interaction
            await self._debug_screenshot(browser, "before_dropdown_interaction")

            # Execute comprehensive JavaScript dropdown interaction
            result = await page.evaluate("""
                () => {
                    console.log('🎯 Starting comprehensive ExaAI dropdown interaction...');
                    
                    // Available Result Category options for ExaAI
                    const availableOptions = ['Company', 'Research Paper', 'News Article', 'PDF', 'Github', 'Personal Site', 'LinkedIn Profile', 'Financial Report'];
                    let selectedOption = null;
                    
                    // Step 1: Find and click the Result Category dropdown button
                    console.log('Step 1: Looking for Result Category dropdown button...');
                    
                    // ExaAI-specific selectors for the dropdown button
                    const dropdownSelectors = [
                        'button.command-bar-search-category',
                        'button[data-sentry-element="SelectTrigger"]',
                        'button[role="combobox"]',
                        '.command-bar-search-category',
                        '*[class*="search-category"] button',
                        '*:has-text("Result category") button',
                        '*:has-text("Result category") + * button'
                    ];
                    
                    let dropdownButton = null;
                    let usedSelector = null;
                    
                    for (let selector of dropdownSelectors) {
                        try {
                            const elements = document.querySelectorAll(selector);
                            console.log(`Selector "${selector}" found ${elements.length} elements`);
                            
                            for (let element of elements) {
                                if (element.offsetParent !== null) { // visible
                                    dropdownButton = element;
                                    usedSelector = selector;
                                    console.log(`✅ Found visible dropdown button with: ${selector}`);
                                    break;
                                }
                            }
                            if (dropdownButton) break;
                        } catch (e) {
                            console.log(`Selector failed: ${selector}`, e.message);
                            continue;
                        }
                    }
                    
                    if (!dropdownButton) {
                        console.log('❌ No dropdown button found');
                        return { success: false, message: 'No dropdown button found' };
                    }
                    
                    // Step 2: Click the dropdown button
                    console.log('Step 2: Clicking dropdown button...');
                    dropdownButton.click();
                    
                    // Wait for dropdown to open and populate options
                    return new Promise((resolve) => {
                        setTimeout(() => {
                            console.log('Step 3: Looking for dropdown options...');
                            
                            // Look for role="option" elements (standard)
                            const roleOptions = document.querySelectorAll('[role="option"]');
                            console.log(`Found ${roleOptions.length} role="option" elements`);
                            
                            let optionClicked = false;
                            
                            // Approach 1: Try clicking role="option" elements
                            if (roleOptions.length > 0) {
                                console.log('Trying role="option" elements...');
                                for (let option of roleOptions) {
                                    const text = option.textContent.trim();
                                    if (availableOptions.includes(text) && option.offsetParent !== null) {
                                        try {
                                            option.click();
                                            selectedOption = text;
                                            optionClicked = true;
                                            console.log(`✅ Selected option via role: ${text}`);
                                            break;
                                        } catch (e) {
                                            console.log(`Failed to click role option ${text}:`, e.message);
                                        }
                                    }
                                }
                            }
                            
                            // Approach 2: Try other selectors if role options didn't work
                            if (!optionClicked) {
                                console.log('Trying other option selectors...');
                                const otherSelectors = ['li', 'div', 'span', 'button'];
                                for (let tagName of otherSelectors) {
                                    const elements = document.querySelectorAll(tagName);
                                    for (let element of elements) {
                                        const text = element.textContent.trim();
                                        if (availableOptions.includes(text) && element.offsetParent !== null) {
                                            // Make sure it's not the dropdown button itself
                                            if (element !== dropdownButton) {
                                                try {
                                                    element.click();
                                                    selectedOption = text;
                                                    optionClicked = true;
                                                    console.log(`✅ Selected option via ${tagName}: ${text}`);
                                                    break;
                                                } catch (e) {
                                                    console.log(`Failed to click ${tagName} option ${text}:`, e.message);
                                                }
                                            }
                                        }
                                    }
                                    if (optionClicked) break;
                                }
                            }
                            
                            // Approach 3: Try clicking the first visible clickable element in dropdown (fallback)
                            if (!optionClicked) {
                                console.log('Trying fallback approach - clicking first available option...');
                                const clickableElements = document.querySelectorAll('div, span, li, button');
                                for (let element of clickableElements) {
                                    const text = element.textContent.trim();
                                    if (availableOptions.includes(text) && element.offsetParent !== null) {
                                        try {
                                            element.click();
                                            selectedOption = text;
                                            optionClicked = true;
                                            console.log(`✅ Selected option via fallback: ${text}`);
                                            break;
                                        } catch (e) {
                                            console.log(`Failed to click fallback option ${text}:`, e.message);
                                        }
                                    }
                                }
                            }
                            
                            if (optionClicked) {
                                resolve({ 
                                    success: true, 
                                    selectedOption: selectedOption,
                                    method: 'comprehensive-javascript',
                                    dropdownSelector: usedSelector
                                });
                            } else {
                                console.log('❌ Could not find or click any dropdown options');
                                resolve({ 
                                    success: false, 
                                    message: 'No clickable options found',
                                    foundRoleOptions: roleOptions.length
                                });
                            }
                        }, 2000); // Wait 2 seconds for dropdown to fully open
                    });
                }
            """)

            if result.get("success"):
                selected_option = result.get("selectedOption", "Unknown")
                method = result.get("method", "javascript")
                dropdown_selector = result.get("dropdownSelector", "unknown")

                logger.info(
                    f"✅ STRATEGY 1 SUCCESS: Selected '{selected_option}' using {method}"
                )
                logger.info(f"🎯 Dropdown found with selector: {dropdown_selector}")

                # Take debug screenshot after successful selection
                await self._debug_screenshot(browser, "after_dropdown_selection")
                await asyncio.sleep(1)  # Brief pause to let UI update
                return True
            else:
                error_msg = result.get("message", "Unknown error")
                found_options = result.get("foundRoleOptions", 0)
                logger.warning(
                    f"⚠️ STRATEGY 1 FAILED: {error_msg} (found {found_options} role options)"
                )

                # Take debug screenshot to understand why it failed
                await self._debug_screenshot(browser, "dropdown_selection_failed")
        except Exception as e:
            logger.error(f"JavaScript comprehensive approach failed: {e}")
            await self._debug_screenshot(browser, "dropdown_javascript_error")
        return False

    async def _try_mouse_coordinate_dropdown_click(self, browser):
        """Try mouse coordinate-based clicking for custom dropdowns."""
        logger.info("🔍 Strategy 2: Enhanced dropdown detection and clicking")
        page = browser.page

        # Method 1A: Look for the specific Result Category dropdown button
        try:
            logger.info("🔍 Method 1A: Looking for Result Category dropdown button")

            # Primary selectors for ExaAI Result Category dropdown
            dropdown_selectors = [
                "button.command-bar-search-category",  # Primary ExaAI selector
                'button[data-sentry-element="SelectTrigger"]',  # Sentry tracking selector
                'button[role="combobox"]',  # ARIA combobox role
                'div:has-text("Result category") button',  # Button near "Result category" text
                '*:has-text("Result category") + button',  # Button after category text
                '*:has-text("Result category") button',  # Button within category container
            ]

            dropdown_button = None

            for selector in dropdown_selectors:
                try:
                    dropdown_button = await page.query_selector(selector)
                    if dropdown_button and await dropdown_button.is_visible():
                        logger.info(
                            f"✅ Found Result Category dropdown with selector: {selector}"
                        )
                        break
                except:
                    continue

            if dropdown_button:
                # Click to open dropdown
                await dropdown_button.click(force=True)
                logger.info("✅ STRATEGY 2A: Successfully clicked dropdown button")
                await asyncio.sleep(2)  # Wait for dropdown to open

                # Try to select an option
                option_selected = await self._select_dropdown_option(browser)
                if option_selected:
                    return True
        except Exception as e:
            logger.debug(f"Dropdown button click failed: {e}")

        return False

    async def _try_standard_dropdown_methods(self, browser):
        """Try standard Playwright dropdown interaction methods."""
        logger.info("🔍 Strategy 3: Standard Playwright dropdown methods")
        page = browser.page

        try:
            # Method 3A: Look for select elements
            select_elements = page.locator("select")
            select_count = await select_elements.count()
            logger.info(f"Found {select_count} select elements")
            if select_count > 0:
                first_select = select_elements.first
                # Try to select the first non-default option
                await first_select.select_option(index=1)
                logger.info(
                    "✅ STRATEGY 3A: Successfully selected option from select element"
                )
                return True
        except Exception as e:
            logger.debug(f"Select element method failed: {e}")

        try:
            # Method 3B: Try combobox role elements
            comboboxes = page.locator('[role="combobox"]')
            combobox_count = await comboboxes.count()
            logger.info(f"Found {combobox_count} combobox elements")
            if combobox_count > 0:
                first_combobox = comboboxes.first
                await first_combobox.click(force=True)
                logger.info("✅ STRATEGY 3B: Successfully clicked combobox")
                await asyncio.sleep(2)

                # Try to select an option
                option_selected = await self._select_dropdown_option(browser)
                if option_selected:
                    return True
        except Exception as e:
            logger.debug(f"Combobox method failed: {e}")

        return False

    async def _try_force_dropdown_click_methods(self, browser):
        """Try force clicking methods for stubborn dropdowns."""
        logger.info("🔍 Strategy 4: Force clicking methods")
        page = browser.page

        try:
            # Method 4A: Force click any button that might be a dropdown
            dropdown_candidates = [
                "button.command-bar-search-category",
                'button[data-sentry-element="SelectTrigger"]',
                'button[role="combobox"]',
            ]

            for candidate in dropdown_candidates:
                try:
                    buttons = page.locator(candidate)
                    if await buttons.count() > 0:
                        first_button = buttons.first
                        await first_button.click(force=True)
                        logger.info(f"✅ STRATEGY 4A: Force clicked {candidate}")
                        await asyncio.sleep(2)

                        # Try to select an option
                        option_selected = await self._select_dropdown_option(browser)
                        if option_selected:
                            return True
                except Exception as e:
                    logger.debug(f"Force click {candidate} failed: {e}")
                    continue
        except Exception as e:
            logger.debug(f"Force click method failed: {e}")

        return False

    async def _try_alternative_dropdown_methods(self, browser):
        """Try alternative element targeting and event dispatch."""
        logger.info("🔍 Strategy 5: Alternative element targeting methods")
        page = browser.page

        try:
            # Method 5A: Try clicking elements near "Result category" text
            category_text_elements = page.locator('*:has-text("Result category")')
            if await category_text_elements.count() > 0:
                first_element = category_text_elements.first
                await first_element.click(force=True)
                logger.info("✅ STRATEGY 5A: Clicked Result category text element")
                await asyncio.sleep(2)

                # Try to select an option
                option_selected = await self._select_dropdown_option(browser)
                if option_selected:
                    return True
        except Exception as e:
            logger.debug(f"Alternative text element clicking failed: {e}")

        try:
            # Method 5B: Try using keyboard navigation
            await page.keyboard.press("Tab")  # Navigate to next focusable element
            await asyncio.sleep(0.5)
            await page.keyboard.press("Enter")  # Try to activate dropdown
            await asyncio.sleep(2)

            # Try to select an option with keyboard
            await page.keyboard.press("ArrowDown")  # Move to first option
            await asyncio.sleep(0.5)
            await page.keyboard.press("Enter")  # Select option

            logger.info("✅ STRATEGY 5B: Attempted keyboard navigation")
            return True  # Assume success with keyboard method
        except Exception as e:
            logger.debug(f"Keyboard navigation method failed: {e}")

        return False

    async def _select_dropdown_option(self, browser):
        """Helper method to select an option from an opened dropdown."""
        logger.info("🔍 Attempting to select dropdown option...")
        page = browser.page

        # Available Result Category options
        option_texts = [
            "Company",
            "Research Paper",
            "News Article",
            "PDF",
            "Github",
            "Personal Site",
            "LinkedIn Profile",
            "Financial Report",
        ]

        # Randomly select one option
        import random

        selected_option_text = random.choice(option_texts)
        logger.info(f"🎯 Targeting option: {selected_option_text}")

        # Try multiple selectors to find and click the option
        option_selectors = [
            f'div:has-text("{selected_option_text}")',
            f'span:has-text("{selected_option_text}")',
            f'[role="option"]:has-text("{selected_option_text}")',
            f'li:has-text("{selected_option_text}")',
            f'button:has-text("{selected_option_text}")',
            f'*[data-value*="{selected_option_text.lower()}"]',
            f'*[value*="{selected_option_text.lower()}"]',
        ]

        for selector in option_selectors:
            try:
                option_element = await page.query_selector(selector)
                if option_element and await option_element.is_visible():
                    await option_element.click(force=True)
                    logger.info(
                        f"✅ Selected Result Category: {selected_option_text} using {selector}"
                    )
                    await asyncio.sleep(1)
                    return True
            except:
                continue

        # Fallback: Try JavaScript-based option selection
        try:
            result = await page.evaluate(f"""
                () => {{
                    const optionTexts = {option_texts};
                    const targetOption = "{selected_option_text}";
                    
                    // Look for any element containing the target option text
                    const allElements = document.querySelectorAll('*');
                    for (let element of allElements) {{
                        if (element.textContent && 
                            element.textContent.trim() === targetOption &&
                            element.offsetParent !== null) {{
                            element.click();
                            return true;
                        }}
                    }}
                    return false;
                }}
            """)

            if result:
                logger.info(
                    f"✅ Selected Result Category: {selected_option_text} using JavaScript fallback"
                )
                return True
        except Exception as e:
            logger.debug(f"JavaScript option selection failed: {e}")

        logger.warning(f"⚠️ Could not select option: {selected_option_text}")
        return False
