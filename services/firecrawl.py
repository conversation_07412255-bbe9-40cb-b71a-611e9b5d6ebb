import asyncio
import logging
import random
import re
from typing import Optional, List
from datetime import datetime
from pathlib import Path

from playwright.async_api import Page
from core.browser import BrowserManager
from core.models import RegistrationResult, ServiceCredentials
from core.config import ServiceConfig, Config
from core.email_client_factory import EmailClientFactory
from core.profile import ProfileGenerator
from core.domain_selector import DomainSelector
from core.utils import generate_strong_password
from core.randomizer import Randomizer, create_randomizer, RandomizationProfile

logger = logging.getLogger(__name__)


class FirecrawlService:
    """
    Service implementation for Firecrawl.
    """

    def __init__(self, service_config: ServiceConfig, config: Config):
        self.service_config = service_config
        self.full_config = config
        self.debug_mode = False

        # Initialize centralized randomizer
        randomizer_config = getattr(config, "randomizer", None)
        if randomizer_config:
            self.randomizer = Randomizer(randomizer_config)
        else:
            # Use default randomizer configuration
            self.randomizer = create_randomizer(
                RandomizationProfile.NORMAL, enabled=True
            )

        service_domains = self.full_config.get_domains_for_service("firecrawl")
        if not service_domains:
            raise ValueError("No email domains configured for firecrawl")

        self.domain_selector = DomainSelector(
            service_domains, randomizer=self.randomizer
        )
        self.selected_domain = self.domain_selector.select_domain()
        if not self.selected_domain:
            raise ValueError("No email domains configured")

        logger.info(f"Selected email domain: {self.selected_domain}")

        self.profile_generator = ProfileGenerator(
            self.selected_domain, randomizer=self.randomizer
        )
        self.email_client = EmailClientFactory.create_client()

    def _get_headless_setting(self) -> Optional[bool]:
        """Get headless setting from service config with default to global config."""
        if self.service_config.headless is not None:
            return self.service_config.headless
        # Don't override - let BrowserManager handle global engine configs
        return None

    def _get_browser_engines(self) -> List[str]:
        """Get browser engines list from service config or fall back to global default."""
        if self.service_config.browser_engines is not None:
            return self.service_config.browser_engines
        return self.full_config.browser_engines

    async def _debug_screenshot(self, browser: BrowserManager, name: str):
        """Take a debug screenshot if debug mode is enabled."""
        if not self.debug_mode:
            return

        try:
            # Create debug directory
            debug_dir = (
                Path("debug") / "firecrawl" / datetime.now().strftime("%Y%m%d_%H%M%S")
            )
            debug_dir.mkdir(parents=True, exist_ok=True)

            # Take screenshot
            screenshot_path = debug_dir / f"{name}.png"
            await browser.page.screenshot(path=str(screenshot_path))
            logger.info(f"🐛 Debug screenshot saved: {screenshot_path}")
        except Exception as e:
            logger.warning(f"Failed to save debug screenshot: {e}")

    @staticmethod
    def parse_verification_link(raw_email_content: str) -> Optional[str]:
        """
        Parses the verification link from the email content using the EXACT working method.
        """
        logger.info("🔗 Parsing verification link from email")

        # Debug: Log email content length
        logger.info(
            f"📧 Full email content length: {len(raw_email_content)} characters"
        )

        # Method that works: Look for href=3D" pattern and decode it
        href_pattern = r"href=3D\"([^\"]+)\""
        href_matches = re.findall(href_pattern, raw_email_content)
        logger.info(f"🔍 href= matches found: {len(href_matches)}")

        verification_link = None
        for match in href_matches:
            if "firecrawl.dev/auth/v1/verify" in match:
                # Decode the quoted-printable URL
                decoded_url = match.replace("=3D", "=").replace("&amp;", "&")
                # Remove line breaks (quoted-printable line continuation)
                clean_url = re.sub(r"=\s*\n", "", decoded_url)
                verification_link = clean_url
                logger.info(
                    f"🎯 Successfully extracted verification URL: {verification_link}"
                )
                break

        if not verification_link:
            logger.error("❌ Could not parse verification link")
            return None

        logger.info(f"🔗 Found verification link: {verification_link}")
        return verification_link

    async def _complete_wizard(self, browser: BrowserManager):
        """Handles the 4-step setup wizard with improved error handling and state detection."""
        logger.info("🧙 Starting setup wizard...")

        # Wait for wizard to load
        await asyncio.sleep(5)
        await self._debug_screenshot(browser, "wizard_start")

        # Check if we're actually on a wizard page
        wizard_indicators = [
            'text="Step 1 of 4"',
            'text="Step 2 of 4"',
            'text="Step 3 of 4"',
            'text="Step 4 of 4"',
            'text="Let\'s get you started"',
            'button:has-text("Next")',
        ]

        wizard_detected = False
        for indicator in wizard_indicators:
            try:
                element = await browser.page.query_selector(indicator)
                if element and await element.is_visible():
                    wizard_detected = True
                    logger.info(f"✅ Wizard detected with indicator: {indicator}")
                    break
            except:
                continue

        if not wizard_detected:
            logger.info("ℹ️ No setup wizard detected, skipping wizard completion")
            return True

        # Step 1: Handle first step (usually "Next" button or bonus credits)
        logger.info("📋 Wizard step 1: Looking for Next button or options")
        try:
            # Try to find and click Next button
            next_selectors = [
                'button:has-text("Next")',
                'button:has-text("Continue")',
                'button:has-text("Get Started")',
            ]

            step1_completed = False
            for selector in next_selectors:
                try:
                    next_button = await browser.page.query_selector(selector)
                    if next_button and await next_button.is_visible():
                        await next_button.click()
                        logger.info(
                            f"✅ Step 1: Clicked button with selector: {selector}"
                        )
                        await asyncio.sleep(3)
                        step1_completed = True
                        break
                except Exception as e:
                    logger.debug(f"Step 1 selector {selector} failed: {e}")
                    continue

            if not step1_completed:
                logger.info("ℹ️ Step 1: No Next button found, proceeding to step 2")

            await self._debug_screenshot(browser, "wizard_step1_complete")

        except Exception as e:
            logger.warning(f"⚠️ Step 1 error: {e}")

        # Step 2: Select random radio button option (1-3)
        logger.info("📋 Wizard step 2: Selecting random option (1-3)")
        try:
            await asyncio.sleep(2)  # Wait for step transition

            option_buttons = browser.page.locator('button[role="radio"]')
            button_count = await option_buttons.count()
            logger.info(f"Found {button_count} radio button options in Step 2")

            step2_completed = False
            if button_count > 0:
                # Choose random option between 1-3 (or max available)
                max_choice = min(3, button_count)
                random_choice = random.randint(0, max_choice - 1)

                selected_button = option_buttons.nth(random_choice)
                await selected_button.click()
                logger.info(
                    f"✅ Step 2: Selected random option {random_choice + 1} of {button_count} - will auto-advance"
                )
                step2_completed = True
            else:
                logger.info("ℹ️ Step 2: No radio buttons found")

            if not step2_completed:
                logger.info("ℹ️ Step 2: No radio buttons found, proceeding to step 3")

            # No need to click Next - it auto-advances after selection
            await asyncio.sleep(3)  # Wait for auto-advance
            await self._debug_screenshot(browser, "wizard_step2_complete")

        except Exception as e:
            logger.warning(f"⚠️ Step 2 error: {e}")

        # Step 3: Select random radio button option (1-3)
        logger.info("📋 Wizard step 3: Selecting random option (1-3)")
        try:
            await asyncio.sleep(2)  # Wait for step transition

            option_buttons = browser.page.locator('button[role="radio"]')
            button_count = await option_buttons.count()
            logger.info(f"Found {button_count} radio button options in Step 3")

            step3_completed = False
            if button_count > 0:
                # Choose random option between 1-3 (or max available)
                max_choice = min(3, button_count)
                random_choice = random.randint(0, max_choice - 1)

                selected_button = option_buttons.nth(random_choice)
                await selected_button.click()
                logger.info(
                    f"✅ Step 3: Selected random option {random_choice + 1} of {button_count} - will auto-advance"
                )
                step3_completed = True
            else:
                logger.info("ℹ️ Step 3: No radio buttons found")

            if not step3_completed:
                logger.info("ℹ️ Step 3: No radio buttons found, proceeding to step 4")

            # No need to click Next - it auto-advances after selection
            await asyncio.sleep(3)  # Wait for auto-advance
            await self._debug_screenshot(browser, "wizard_step3_complete")

        except Exception as e:
            logger.warning(f"⚠️ Step 3 error: {e}")

        # Step 4: Handle terms agreement and completion
        logger.info("📋 Wizard step 4: Agreeing to terms and finishing setup")
        try:
            await asyncio.sleep(2)  # Wait for step transition

            # First, try to close any popup dialogs that might be blocking
            try:
                popup_selectors = [
                    'button:has-text("×")',
                    '[data-testid="close"]',
                    'button[aria-label="Close"]',
                    ".close",
                ]
                for popup_selector in popup_selectors:
                    popup = await browser.page.query_selector(popup_selector)
                    if popup and await popup.is_visible():
                        await popup.click()
                        logger.info(f"✅ Closed popup with: {popup_selector}")
                        await asyncio.sleep(1)
                        break
            except:
                pass

            # ENHANCED: Find and check the "I agree to Terms of Service" checkbox with multiple strategies
            terms_agreement_found = await self._check_terms_checkbox_enhanced(browser)

            if not terms_agreement_found:
                logger.error(
                    "❌ CRITICAL: Could not find or check Terms of Service checkbox!"
                )
                await self._debug_screenshot(browser, "error_terms_checkbox_not_found")
                # Still try to continue, but this will likely fail

            # Verify checkbox is checked
            await self._verify_checkbox_state(browser)

            # Click completion button
            completion_selectors = [
                'button:has-text("Finish setup")',
                'button:has-text("Complete Onboarding")',
                'button:has-text("Complete")',
                'button:has-text("Finish")',
                'button:has-text("Done")',
            ]

            completion_clicked = False
            for selector in completion_selectors:
                try:
                    complete_button = await browser.page.query_selector(selector)
                    if complete_button and await complete_button.is_visible():
                        await complete_button.click()
                        logger.info(f"✅ Step 4: Clicked completion button: {selector}")
                        await asyncio.sleep(5)  # Wait longer for completion
                        completion_clicked = True
                        break
                except Exception as e:
                    logger.debug(f"Completion selector {selector} failed: {e}")
                    continue

            if not completion_clicked:
                logger.warning("⚠️ Step 4: Could not find or click completion button")

            await self._debug_screenshot(browser, "wizard_step4_complete")

        except Exception as e:
            logger.warning(f"⚠️ Step 4 error: {e}")

        # Wait for dashboard to load after wizard completion
        logger.info("⏳ Waiting for dashboard to load after wizard completion...")
        await asyncio.sleep(8)
        await self._debug_screenshot(browser, "wizard_completed")

        logger.info("✅ Setup wizard completion attempted")
        return True

    async def register_and_get_api_key(self) -> RegistrationResult:
        """
        Registers a new account on Firecrawl and retrieves the API key.
        """
        profile = self.profile_generator.generate_profile()
        password = generate_strong_password()

        browser_manager = BrowserManager(
            browser_engines=self._get_browser_engines(),
            global_engine_configs=self.full_config.browser_engines,
            service_headless_override=self._get_headless_setting(),
            randomizer=self.randomizer,  # Pass the centralized randomizer
        )
        async with browser_manager as browser:
            try:
                logger.info(f"Generated profile for {profile['email']}")

                # Log selected browser engine for debugging
                logger.info(f"🌐 Selected browser engine: {browser.browser_engine}")

                # Flexible navigation: start_url (homepage) vs signup_url (direct)
                if self.service_config.start_url:
                    # Navigate to homepage first, then click Sign Up button (natural flow)
                    logger.info(f"Navigating to homepage: {self.service_config.start_url}")
                    await browser.goto(self.service_config.start_url)
                    await browser.wait(self.service_config.delay_times.get("page_load", 3000))
                    await self._debug_screenshot(browser, "01_homepage")

                    # Click "Sign Up" button in navigation
                    logger.info("🔍 Looking for 'Sign Up' button in navigation")
                    signup_nav_selectors = [
                        'button:has-text("Sign Up")',
                        'a:has-text("Sign Up")',
                        '[href*="/signup"]',
                        '[href*="/auth"]',
                        'nav button:has-text("Sign Up")',
                        'header button:has-text("Sign Up")',
                    ]

                    button_found = False
                    for selector in signup_nav_selectors:
                        try:
                            signup_button = browser.page.locator(selector).first
                            if await signup_button.is_visible():
                                await signup_button.click()
                                logger.info(f"✅ Clicked Sign Up button with selector: {selector}")
                                button_found = True
                                break
                        except Exception as e:
                            logger.debug(f"Sign Up button selector {selector} failed: {e}")
                            continue

                    if not button_found:
                        if self.service_config.signup_url:
                            logger.info("⚠️ Could not find Sign Up button, falling back to direct signup URL")
                            await browser.goto(self.service_config.signup_url)
                        else:
                            logger.error("❌ Could not find 'Sign Up' button and no signup_url configured")
                            return RegistrationResult(success=False, error_message="Could not find Sign Up button in navigation", service="firecrawl")

                    # Wait for signup page to load after clicking
                    await asyncio.sleep(5)
                    await self._debug_screenshot(browser, "01_signup_page")

                elif self.service_config.signup_url:
                    # Direct navigation to signup page
                    logger.info(f"Navigating directly to signup page: {self.service_config.signup_url}")
                    await browser.goto(self.service_config.signup_url)
                    await browser.wait(self.service_config.delay_times.get("page_load", 3000))
                    await self._debug_screenshot(browser, "01_signup_page")

                else:
                    logger.error("❌ Neither start_url nor signup_url configured")
                    return RegistrationResult(success=False, error_message="No start_url or signup_url configured", service="firecrawl")

                logger.info(
                    f"Signing up with email '{profile['email']}' and a generated password."
                )

                # Fill in the form fields with realistic timing
                email_input = await browser.page.query_selector('input[type="email"]')
                password_input = await browser.page.query_selector(
                    'input[type="password"]'
                )

                if not email_input or not password_input:
                    return RegistrationResult(
                        success=False,
                        error_message="Could not find email or password input fields.",
                        service="firecrawl",
                    )

                # Clear fields first (use different method for ElementHandle)
                await email_input.fill("")  # Clear by setting empty value
                await password_input.fill("")  # Clear by setting empty value
                await self.randomizer.user_action_delay()

                # Type email with human-like timing
                await email_input.type(
                    profile["email"], delay=self.randomizer.randint(50, 150)
                )
                await self.randomizer.user_action_delay()

                # Type password with human-like timing
                await password_input.type(
                    password, delay=self.randomizer.randint(50, 150)
                )
                await self.randomizer.user_action_delay()

                # Find and click signup button - using exact working code from headless script
                try:
                    # Wait for signup button to be enabled
                    await browser.page.wait_for_function(
                        """() => {
                            const buttons = Array.from(document.querySelectorAll('button'));
                            const signupButton = buttons.find(btn => btn.textContent && btn.textContent.includes('Sign up'));
                            return signupButton && !signupButton.disabled;
                        }""",
                        timeout=10000,
                    )

                    # Click the signup button - EXACTLY like headless_signup_complete.py
                    signup_button = browser.page.locator(
                        'button:has-text("Sign up")'
                    ).first
                    await signup_button.click()
                    logger.info("🚀 Signup button clicked")

                    # Wait for form submission response
                    await asyncio.sleep(5)

                except Exception as e:
                    logger.error(f"❌ Failed to submit signup form: {e}")
                    return RegistrationResult(
                        success=False,
                        error_message=f"Failed to submit signup form: {e}",
                        service="firecrawl",
                    )

                await self._debug_screenshot(browser, "02_after_signup")

                logger.info(
                    f"Waiting for verification email from {self.service_config.sender_domains} for '{profile['email']}'"
                )

                # Poll for verification email using the webhook client
                verification_email_content = None
                for attempt in range(6):  # Check every 5 seconds for 30 seconds total
                    await asyncio.sleep(5)

                    # Get raw email content for this specific email
                    raw_content = await self.email_client.get_latest_email_content(
                        "firecrawl", profile["email"]
                    )
                    if raw_content:
                        verification_email_content = raw_content
                        logger.info(
                            f"Verification email received for {profile['email']} after {(attempt + 1) * 5} seconds"
                        )
                        break

                    logger.info(
                        f"Waiting for verification email for {profile['email']}... ({(attempt + 1) * 5}s)"
                    )

                if not verification_email_content:
                    return RegistrationResult(
                        success=False,
                        error_message=f"Verification email for {profile['email']} not found after 30 seconds.",
                        service="firecrawl",
                    )

                verification_link = self.parse_verification_link(
                    verification_email_content
                )
                if not verification_link:
                    return RegistrationResult(
                        success=False,
                        error_message="Could not parse verification link.",
                        service="firecrawl",
                    )

                logger.info("Navigating to verification link to confirm account.")
                # Use longer timeout and domcontentloaded instead of networkidle for verification
                await browser.page.goto(
                    verification_link, wait_until="domcontentloaded", timeout=60000
                )
                await browser.wait(5000)  # Wait for any redirects to complete
                await self._debug_screenshot(browser, "03_verification_page")

                # Check verification status - similar to ExaAI approach
                verification_success = await self._verify_dashboard_access(browser)
                if not verification_success:
                    return RegistrationResult(
                        success=False,
                        error_message="Verification failed or could not access dashboard.",
                        service="firecrawl",
                    )

                logger.info("✅ Successfully verified and on dashboard")

                # 🔑 EXTRACT API KEY IMMEDIATELY - API key is available right after dashboard access
                logger.info(
                    "🔑 Extracting API key immediately upon dashboard access..."
                )
                await asyncio.sleep(3)  # Brief wait for dashboard to settle
                await self._debug_screenshot(
                    browser, "04_dashboard_before_api_extraction"
                )

                # Extract API key using the direct and simple approach
                api_key = await self._extract_api_key_simple(browser)

                if not api_key or len(api_key) < 10:
                    return RegistrationResult(
                        success=False,
                        error_message="Failed to retrieve API key immediately after dashboard access.",
                        service="firecrawl",
                    )

                logger.info(
                    f"🔑 Successfully extracted API key: {api_key[:12]}... (total length: {len(api_key)} chars)"
                )

                # 💾 SAVE CREDENTIALS IMMEDIATELY with wizard_undone status
                from core.results_storage import ResultsStorage

                credential = ServiceCredentials(
                    service="firecrawl",
                    api_key=api_key,
                    email=profile["email"],
                    password=password,
                    created_at=datetime.now(),
                    username=profile["email"].split("@")[0],
                    metadata={
                        "wizard_status": "wizard_undone"
                    },  # Mark as wizard incomplete
                )
                storage = ResultsStorage()
                storage.store_or_update_credential(credential)
                logger.info(
                    f"💾 Credentials with API key saved (wizard_undone) for {profile['email']}"
                )

                # NOW complete the wizard to earn extra credits and avoid suspicion
                logger.info(
                    "🧙 Now completing setup wizard for extra credits and legitimacy..."
                )
                await self._complete_wizard(browser)

                # 🔄 UPDATE STATUS to wizard_done after wizard completion
                credential.metadata["wizard_status"] = "wizard_done"
                credential.updated_at = datetime.now()  # Update the timestamp
                storage.store_or_update_credential(
                    credential
                )  # Use store_or_update to update existing
                logger.info(
                    f"🎯 Updated credential status to wizard_done for {profile['email']}"
                )

                # Stay on the page based on configuration
                stay_duration = (
                    self.service_config.delay_times.get("stay_on_finish", 10000) / 1000
                )  # Convert to seconds
                logger.info(
                    f"⏳ Staying on dashboard for {stay_duration} seconds before closing session..."
                )
                await asyncio.sleep(stay_duration)

                return RegistrationResult(
                    success=True,
                    api_key=api_key,
                    email=profile["email"],
                    password=password,
                    service="firecrawl",
                )

            except Exception as e:
                error_message = f"An unexpected error occurred: {e}"
                logger.error(error_message, exc_info=True)
                if self.debug_mode:
                    path = f"debug/firecrawl_error_{profile['email']}.png"
                    await browser.page.screenshot(path=path)
                    logger.info(f"Saved error screenshot to {path}")
                return RegistrationResult(
                    success=False, error_message=error_message, service="firecrawl"
                )

    async def _extract_api_key_simple(self, browser: BrowserManager) -> Optional[str]:
        """Extract API key directly from page content - simple and reliable."""
        try:
            logger.info("🔍 Extracting API key directly from page content...")

            # Get page content and look for the masked API key pattern
            page_content = await browser.page.content()

            # Look for the specific masked pattern: fc-f***************************faaa
            # The real API key should be in a data attribute or similar
            import re

            # Method 1: Look for full API key in data attributes, values, etc
            full_key_patterns = [
                r"fc-[a-zA-Z0-9]{30,}",  # Full API key pattern
                r'value="(fc-[^"]+)"',  # In value attribute
                r'data-key="(fc-[^"]+)"',  # In data-key attribute
                r'"apiKey":\s*"(fc-[^"]+)"',  # In JSON
            ]

            for pattern in full_key_patterns:
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                for match in matches:
                    # Extract the key from capture groups or use the full match
                    key = match if isinstance(match, str) else match
                    if key.startswith("fc-") and len(key) > 30:
                        logger.info(
                            f"✅ Found full API key in page content: {key[:12]}..."
                        )
                        return key

            # Method 2: Look for the API key in JavaScript variables
            js_api_key = await browser.page.evaluate("""
                () => {
                    // Look for API key in common variable names
                    const possibleKeys = [
                        window.apiKey,
                        window.FIRECRAWL_API_KEY,
                        window.__NEXT_DATA__?.props?.pageProps?.apiKey,
                        window.__NEXT_DATA__?.props?.pageProps?.user?.apiKey
                    ];
                    
                    for (let key of possibleKeys) {
                        if (key && typeof key === 'string' && key.startsWith('fc-') && key.length > 30) {
                            return key;
                        }
                    }
                    
                    // Look in all script tags for API keys
                    const scripts = Array.from(document.scripts);
                    for (let script of scripts) {
                        if (script.textContent) {
                            const match = script.textContent.match(/fc-[a-zA-Z0-9]{30,}/);
                            if (match) {
                                return match[0];
                            }
                        }
                    }
                    
                    return null;
                }
            """)

            if js_api_key and js_api_key.startswith("fc-") and len(js_api_key) > 30:
                logger.info(f"✅ Found API key in JavaScript: {js_api_key[:12]}...")
                return js_api_key

            # Method 3: Look for the API key in specific DOM elements
            elements_with_key = await browser.page.query_selector_all(
                "[data-key], [data-value], input[value], textarea[value], [title], [aria-label]"
            )
            for element in elements_with_key:
                for attr in ["data-key", "data-value", "value", "title", "aria-label"]:
                    try:
                        attr_value = await element.get_attribute(attr)
                        if (
                            attr_value
                            and attr_value.startswith("fc-")
                            and len(attr_value) > 30
                        ):
                            logger.info(
                                f"✅ Found API key in {attr} attribute: {attr_value[:12]}..."
                            )
                            return attr_value
                    except:
                        continue

            # Method 4: Look for text content that might contain the full key
            all_text_elements = await browser.page.query_selector_all("*")
            for element in all_text_elements[
                :100
            ]:  # Limit to first 100 elements for performance
                try:
                    text_content = await element.text_content()
                    if text_content:
                        key_match = re.search(r"fc-[a-zA-Z0-9]{30,}", text_content)
                        if key_match:
                            key = key_match.group(0)
                            logger.info(
                                f"✅ Found API key in text content: {key[:12]}..."
                            )
                            return key
                except:
                    continue

            logger.warning("🔍 Could not find API key in page content")
            return None

        except Exception as e:
            logger.error(f"Error extracting API key from page content: {e}")
            return None

    async def _extract_api_key_from_page_content(
        self, browser: BrowserManager
    ) -> Optional[str]:
        """Extract API key directly from page content (most reliable method)."""
        try:
            logger.info("🔍 Searching for API key in page content...")

            # Get page content
            page_content = await browser.page.content()

            # Save page content for debugging
            debug_dir = (
                Path("debug") / "firecrawl" / datetime.now().strftime("%Y%m%d_%H%M%S")
            )
            debug_dir.mkdir(parents=True, exist_ok=True)
            with open(debug_dir / "page_content.html", "w", encoding="utf-8") as f:
                f.write(page_content)
            logger.info(
                f"📄 Page content saved for debugging: {debug_dir / 'page_content.html'}"
            )

            # Look for Firecrawl API key patterns
            api_key_patterns = [
                r"fc-[a-zA-Z0-9]{40,}",  # Full Firecrawl API key
                r'"(fc-[a-zA-Z0-9]{32,})"',  # Quoted API key
                r'value="(fc-[a-zA-Z0-9]{32,})"',  # Input value
                r'data-value="(fc-[a-zA-Z0-9]{32,})"',  # Data attribute
                r'apiKey["\']:\s*["\']([^"\']+)["\']',  # JSON apiKey field
            ]

            for pattern in api_key_patterns:
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                for match in matches:
                    key = match if isinstance(match, str) else match
                    if self._is_valid_firecrawl_api_key(key):
                        logger.info(f"🔑 Found valid API key with pattern: {pattern}")
                        return key

            # Look for API key in text content of specific elements
            api_key_element_selectors = [
                'div:has-text("API Key") + div',  # Element next to "API Key" text
                '[data-testid*="api-key"]',  # Elements with api-key in test id
                ".api-key",  # Elements with api-key class
                "code",  # Code elements
                "pre",  # Pre elements
                "input[readonly]",  # Readonly inputs
                "span.font-mono",  # Monospace spans (common for API keys)
            ]

            for selector in api_key_element_selectors:
                try:
                    elements = await browser.page.query_selector_all(selector)
                    for element in elements:
                        text_content = await element.text_content()
                        if text_content and self._is_valid_firecrawl_api_key(
                            text_content.strip()
                        ):
                            logger.info(f"🔑 Found API key in element: {selector}")
                            return text_content.strip()

                        # Also check element attributes
                        for attr in ["value", "data-value", "data-key", "title"]:
                            try:
                                attr_value = await element.get_attribute(attr)
                                if attr_value and self._is_valid_firecrawl_api_key(
                                    attr_value
                                ):
                                    logger.info(
                                        f"🔑 Found API key in {attr} attribute of {selector}"
                                    )
                                    return attr_value
                            except:
                                continue
                except Exception as e:
                    logger.debug(f"Error checking selector {selector}: {e}")
                    continue

            logger.info("🔍 No API key found in page content")
            return None

        except Exception as e:
            logger.error(f"Error extracting API key from page content: {e}")
            return None

    async def _extract_api_key_via_clipboard(
        self, browser: BrowserManager
    ) -> Optional[str]:
        """Extract API key using clipboard copy method."""
        try:
            logger.info("📋 Attempting clipboard-based API key extraction...")

            # Look for copy buttons with improved selectors
            copy_button_selectors = [
                # More specific selectors for Firecrawl
                'div:has-text("API Key") button:has(svg)',  # Button with SVG in API Key section
                'button[data-state="closed"]:has(svg.lucide-clipboard)',  # Specific clipboard button
                "div:has(p.font-mono) button:has(svg)",  # Button near monospace text (API key display)
                'button:has(span.sr-only:text("Copy"))',  # Button with "Copy" screen reader text
                # Fallback selectors
                "button:has(svg.lucide-clipboard)",  # Any clipboard icon button
                'button:has([data-icon="copy"])',  # Copy icon button
                'button:has-text("Copy")',  # Text-based copy button
                '[data-testid*="copy"]',  # Copy test ID
            ]

            for i, selector in enumerate(copy_button_selectors):
                try:
                    logger.info(f"🎯 Trying copy button selector {i + 1}: {selector}")
                    copy_button = await browser.page.query_selector(selector)

                    if copy_button and await copy_button.is_visible():
                        logger.info(f"✅ Found copy button with selector: {selector}")

                        # Click the copy button
                        await copy_button.click()
                        await asyncio.sleep(3)  # Wait for clipboard operation

                        # Try to read from clipboard
                        clipboard_content = await self._read_clipboard_content(browser)

                        if clipboard_content and self._is_valid_firecrawl_api_key(
                            clipboard_content
                        ):
                            logger.info(
                                f"🔑 Successfully extracted API key via clipboard"
                            )
                            return clipboard_content
                        elif clipboard_content:
                            logger.info(
                                f"📋 Clipboard content not an API key: {clipboard_content[:50]}..."
                            )

                except Exception as e:
                    logger.debug(f"Copy button selector {selector} failed: {e}")
                    continue

            logger.info("📋 No valid API key found via clipboard method")
            return None

        except Exception as e:
            logger.error(f"Error in clipboard-based extraction: {e}")
            return None

    async def _extract_api_key_via_patterns(
        self, browser: BrowserManager
    ) -> Optional[str]:
        """Extract API key using pattern matching as final fallback."""
        try:
            logger.info("🔍 Attempting pattern-based API key extraction...")

            # Get all text content from the page
            all_text = await browser.page.evaluate("""
                () => {
                    // Get text from all elements, including hidden ones
                    const allElements = document.querySelectorAll('*');
                    let allText = '';

                    allElements.forEach(el => {
                        // Get text content
                        if (el.textContent) {
                            allText += el.textContent + ' ';
                        }

                        // Get common attributes that might contain API keys
                        ['value', 'data-value', 'data-key', 'title', 'aria-label'].forEach(attr => {
                            const attrValue = el.getAttribute(attr);
                            if (attrValue) {
                                allText += attrValue + ' ';
                            }
                        });
                    });

                    return allText;
                }
            """)

            # Look for API key patterns in all text
            patterns = [
                r"fc-[a-zA-Z0-9]{40,}",  # Firecrawl API key pattern
                r"\b[a-zA-Z0-9]{50,}\b",  # Long alphanumeric strings
                r"\b[a-f0-9]{32,}\b",  # Hex strings
            ]

            for pattern in patterns:
                matches = re.findall(pattern, all_text)
                for match in matches:
                    if self._is_valid_firecrawl_api_key(match):
                        logger.info(f"🔑 Found API key via pattern matching: {pattern}")
                        return match

            logger.info("🔍 No API key found via pattern matching")
            return None

        except Exception as e:
            logger.error(f"Error in pattern-based extraction: {e}")
            return None

    def _is_valid_firecrawl_api_key(self, text: str) -> bool:
        """Check if text looks like a valid Firecrawl API key."""
        if not text or len(text) < 20:
            return False

        text = text.strip()

        # Firecrawl API keys start with 'fc-' and are quite long
        if text.startswith("fc-") and len(text) > 30:
            # Should contain only alphanumeric characters and hyphens after 'fc-'
            if re.match(r"^fc-[a-zA-Z0-9]+$", text):
                return True

        return False

    async def _read_clipboard_content(self, browser: BrowserManager) -> Optional[str]:
        """Read content from clipboard with multiple fallback methods."""
        try:
            # Method 1: Direct clipboard API (works in most browsers)
            clipboard_content = await browser.page.evaluate("""
                async () => {
                    try {
                        return await navigator.clipboard.readText();
                    } catch (e) {
                        return null;
                    }
                }
            """)

            if clipboard_content and len(clipboard_content) > 10:
                logger.debug(
                    f"📋 Clipboard method 1 success: {len(clipboard_content)} chars"
                )
                return clipboard_content

            # Method 2: Try execCommand paste (fallback for older browsers)
            paste_content = await browser.page.evaluate("""
                () => {
                    try {
                        const textArea = document.createElement('textarea');
                        textArea.style.position = 'fixed';
                        textArea.style.left = '-999999px';
                        textArea.style.top = '-999999px';
                        document.body.appendChild(textArea);
                        textArea.focus();
                        textArea.select();

                        if (document.execCommand('paste')) {
                            const content = textArea.value;
                            document.body.removeChild(textArea);
                            return content;
                        }

                        document.body.removeChild(textArea);
                        return null;
                    } catch (e) {
                        return null;
                    }
                }
            """)

            if paste_content and len(paste_content) > 10:
                logger.debug(
                    f"📋 Clipboard method 2 success: {len(paste_content)} chars"
                )
                return paste_content

            logger.debug("📋 All clipboard methods failed")
            return None

        except Exception as e:
            logger.error(f"Error reading clipboard: {e}")
            return None

    async def _firefox_clipboard_read(self, page) -> Optional[str]:
        """Firefox-specific clipboard reading with multiple fallback methods."""
        try:
            # Method 1: Try the enhanced clipboard API we injected
            clipboard_content = await page.evaluate(
                "() => navigator.clipboard.readText()"
            )
            if clipboard_content and len(clipboard_content) > 10:
                logger.debug(
                    f"Firefox Method 1 success: {len(clipboard_content)} chars"
                )
                return clipboard_content

            # Method 2: Try to get from the global clipboard content we capture
            global_content = await page.evaluate("() => window.clipboardContent || ''")
            if global_content and len(global_content) > 10:
                logger.debug(f"Firefox Method 2 success: {len(global_content)} chars")
                return global_content

            # Method 3: Try execCommand paste in a temporary textarea
            paste_content = await page.evaluate("""
                () => {
                    try {
                        const textArea = document.createElement('textarea');
                        textArea.style.position = 'fixed';
                        textArea.style.left = '-999999px';
                        textArea.style.top = '-999999px';
                        document.body.appendChild(textArea);
                        textArea.focus();
                        textArea.select();
                        
                        if (document.execCommand('paste')) {
                            const content = textArea.value;
                            document.body.removeChild(textArea);
                            return content;
                        }
                        
                        document.body.removeChild(textArea);
                        return '';
                    } catch (e) {
                        return '';
                    }
                }
            """)

            if paste_content and len(paste_content) > 10:
                logger.debug(f"Firefox Method 3 success: {len(paste_content)} chars")
                return paste_content

            # Method 4: Try to get selected text
            selected_text = await page.evaluate(
                "() => window.getSelection().toString()"
            )
            if selected_text and len(selected_text) > 10:
                logger.debug(f"Firefox Method 4 success: {len(selected_text)} chars")
                return selected_text

            logger.warning("All Firefox clipboard methods failed")
            return None

        except Exception as e:
            logger.error(f"Firefox clipboard read error: {e}")
            return None

    async def _universal_clipboard_read(self, page) -> Optional[str]:
        """Universal clipboard reading that works across all browser engines."""
        try:
            # This is essentially the same as Firefox method but works everywhere
            return await self._firefox_clipboard_read(page)
        except Exception as e:
            logger.error(f"Universal clipboard read error: {e}")
            return None

    async def _check_terms_checkbox_enhanced(self, browser):
        """Enhanced checkbox checking with multiple strategies for JavaScript-controlled checkboxes."""
        logger.info(
            "🎯 ENHANCED: Attempting to check Terms of Service checkbox with multiple strategies"
        )

        # Strategy 1: Try mouse coordinate-based clicking (most reliable for custom checkboxes)
        terms_found = await self._try_mouse_coordinate_click(browser)
        if terms_found:
            return True

        # Strategy 2: Try standard Playwright checkbox methods
        terms_found = await self._try_standard_checkbox_methods(browser)
        if terms_found:
            return True

        # Strategy 3: Try force clicking approaches
        terms_found = await self._try_force_click_methods(browser)
        if terms_found:
            return True

        # Strategy 4: Try JavaScript evaluation (programmatic)
        terms_found = await self._try_javascript_checkbox_methods(browser)
        if terms_found:
            return True

        # Strategy 5: Try clicking labels and parent elements
        terms_found = await self._try_label_click_methods(browser)
        if terms_found:
            return True

        logger.error("❌ All checkbox strategies failed")
        return False

    async def _try_mouse_coordinate_click(self, browser):
        """Try mouse coordinate-based clicking for custom checkboxes."""
        logger.info("🔍 Strategy 1: Enhanced checkbox detection and clicking")
        page = browser.page

        # Method 1: Look for ARIA role checkboxes (modern custom checkboxes)
        try:
            logger.info("🔍 Method 1A: Looking for ARIA role checkboxes")
            aria_checkboxes = page.locator('[role="checkbox"]')
            checkbox_count = await aria_checkboxes.count()
            logger.info(f"Found {checkbox_count} ARIA checkboxes")

            if checkbox_count > 0:
                first_checkbox = aria_checkboxes.first
                await first_checkbox.click(force=True)
                logger.info("✅ STRATEGY 1A: Successfully clicked ARIA checkbox")
                await asyncio.sleep(1)
                return True

        except Exception as e:
            logger.debug(f"ARIA checkbox click failed: {e}")

        # Method 1B: Look for nested checkboxes in custom components
        try:
            logger.info(
                "🔍 Method 1B: Looking for nested checkboxes in custom components"
            )

            # Look for common custom checkbox patterns
            custom_patterns = [
                '*:has-text("Terms") [role="checkbox"]',
                '*:has-text("I agree") [role="checkbox"]',
                'div:has-text("Terms") input[type="checkbox"]',
                'label:has-text("Terms") input[type="checkbox"]',
                '*[class*="checkbox"]:has-text("Terms")',
                '*[class*="check"]:has-text("Terms")',
            ]

            for pattern in custom_patterns:
                try:
                    elements = page.locator(pattern)
                    if await elements.count() > 0:
                        await elements.first.click(force=True)
                        logger.info(
                            f"✅ STRATEGY 1B: Successfully clicked using pattern: {pattern}"
                        )
                        await asyncio.sleep(1)
                        return True
                except:
                    continue

        except Exception as e:
            logger.debug(f"Nested checkbox search failed: {e}")

        # Method 1C: JavaScript-based comprehensive checkbox detection
        try:
            logger.info("🔍 Method 1C: JavaScript comprehensive checkbox detection")

            result = await page.evaluate("""
                () => {
                    console.log('Starting comprehensive checkbox search...');

                    // Strategy 1: Look for hidden input checkboxes and click their labels
                    const hiddenInputs = document.querySelectorAll('input[type="checkbox"]');
                    console.log('Found', hiddenInputs.length, 'input checkboxes');

                    for (let input of hiddenInputs) {
                        // Find associated label
                        let label = input.closest('label') ||
                                   document.querySelector(`label[for="${input.id}"]`) ||
                                   input.parentElement;

                        if (label) {
                            console.log('Clicking label for hidden checkbox');
                            label.click();
                            return { success: true, method: 'hidden-input-label', element: label.tagName };
                        }
                    }

                    // Strategy 2: Look for ARIA checkboxes
                    const ariaCheckboxes = document.querySelectorAll('[role="checkbox"]');
                    console.log('Found', ariaCheckboxes.length, 'ARIA checkboxes');

                    for (let checkbox of ariaCheckboxes) {
                        if (checkbox.offsetParent !== null) { // visible
                            console.log('Clicking ARIA checkbox');
                            checkbox.click();
                            return { success: true, method: 'aria-checkbox', element: checkbox.tagName };
                        }
                    }

                    // Strategy 3: Look for elements with checkbox-like classes near Terms text
                    const termsElements = Array.from(document.querySelectorAll('*')).filter(el =>
                        el.textContent && el.textContent.includes('Terms')
                    );
                    console.log('Found', termsElements.length, 'elements with Terms text');

                    for (let termsEl of termsElements) {
                        // Look for clickable elements in the same container
                        const container = termsEl.closest('div, label, span') || termsEl;
                        const clickables = container.querySelectorAll(
                            '[role="checkbox"], input[type="checkbox"], ' +
                            '*[class*="checkbox"], *[class*="check"], ' +
                            'button, [onclick], [data-testid*="check"]'
                        );

                        for (let clickable of clickables) {
                            if (clickable.offsetParent !== null) {
                                console.log('Clicking checkbox near Terms text');
                                clickable.click();
                                return { success: true, method: 'terms-nearby', element: clickable.tagName };
                            }
                        }
                    }

                    // Strategy 4: Brute force - click anything that looks like a checkbox
                    const allCheckboxLike = document.querySelectorAll(
                        '*[class*="checkbox"], *[class*="check"], *[data-testid*="check"], ' +
                        '*[id*="checkbox"], *[id*="check"], *[id*="terms"], *[id*="agree"]'
                    );
                    console.log('Found', allCheckboxLike.length, 'checkbox-like elements');

                    for (let element of allCheckboxLike) {
                        if (element.offsetParent !== null) {
                            console.log('Clicking checkbox-like element');
                            element.click();
                            return { success: true, method: 'checkbox-like', element: element.tagName };
                        }
                    }

                    return { success: false, message: 'No clickable checkbox found' };
                }
            """)

            if result.get("success"):
                logger.info(
                    f"✅ STRATEGY 1C: JavaScript found and clicked checkbox: {result}"
                )
                await asyncio.sleep(1)
                return True
            else:
                logger.warning(
                    f"⚠️ STRATEGY 1C: JavaScript search failed: {result.get('message')}"
                )

        except Exception as e:
            logger.debug(f"JavaScript comprehensive search failed: {e}")

        # Method 1D: Coordinate-based clicking as last resort
        try:
            logger.info("🔍 Method 1D: Coordinate-based clicking near Terms text")

            terms_elements = page.locator(
                '*:has-text("Terms of Service"), *:has-text("I agree")'
            )
            if await terms_elements.count() > 0:
                first_element = terms_elements.first
                bbox = await first_element.bounding_box()
                if bbox:
                    # Try multiple click positions
                    click_positions = [
                        (bbox["x"] + 20, bbox["y"] + bbox["height"] / 2),  # Left side
                        (
                            bbox["x"] - 30,
                            bbox["y"] + bbox["height"] / 2,
                        ),  # Further left
                        (bbox["x"] + 10, bbox["y"] + bbox["height"] / 2),  # Close left
                    ]

                    for i, (click_x, click_y) in enumerate(click_positions):
                        logger.info(
                            f"🎯 Trying click position {i + 1}: ({click_x}, {click_y})"
                        )
                        await page.mouse.click(click_x, click_y)
                        await asyncio.sleep(0.5)

                    logger.info("✅ STRATEGY 1D: Completed coordinate-based clicking")
                    return True

        except Exception as e:
            logger.debug(f"Coordinate-based clicking failed: {e}")

        return False

    async def _try_standard_checkbox_methods(self, browser):
        """Try standard Playwright checkbox interaction methods."""
        logger.info("🔍 Strategy 2: Standard Playwright checkbox methods")
        page = browser.page

        try:
            # Method 2A: Look for visible checkboxes first
            visible_checkboxes = page.locator('input[type="checkbox"]:visible')
            checkbox_count = await visible_checkboxes.count()
            logger.info(f"Found {checkbox_count} visible checkboxes")

            if checkbox_count > 0:
                first_checkbox = visible_checkboxes.first
                await first_checkbox.set_checked(True, force=True)
                logger.info(
                    "✅ STRATEGY 2A: Successfully checked visible checkbox using setChecked"
                )
                return True

        except Exception as e:
            logger.debug(f"Visible checkbox method failed: {e}")

        try:
            # Method 2B: Try all checkboxes (including hidden) with force
            all_checkboxes = page.locator('input[type="checkbox"]')
            checkbox_count = await all_checkboxes.count()
            logger.info(f"Found {checkbox_count} total checkboxes (including hidden)")

            if checkbox_count > 0:
                first_checkbox = all_checkboxes.first
                await first_checkbox.check(force=True)
                logger.info(
                    "✅ STRATEGY 2B: Successfully checked checkbox using check() with force"
                )
                return True

        except Exception as e:
            logger.debug(f"Force check method failed: {e}")

        try:
            # Method 2C: Use dispatchEvent on checkboxes (from research)
            checkboxes = page.locator('input[type="checkbox"]')
            if await checkboxes.count() > 0:
                first_checkbox = checkboxes.first
                await first_checkbox.dispatch_event("click")
                logger.info(
                    "✅ STRATEGY 2C: Successfully dispatched click event on checkbox"
                )
                return True

        except Exception as e:
            logger.debug(f"Dispatch event method failed: {e}")

        return False

    async def _try_force_click_methods(self, browser):
        """Try force clicking methods for stubborn checkboxes."""
        logger.info("🔍 Strategy 3: Force clicking methods")
        page = browser.page

        try:
            # Method 1: Force click the checkbox input
            checkboxes = page.locator('input[type="checkbox"]')
            if await checkboxes.count() > 0:
                first_checkbox = checkboxes.first
                await first_checkbox.click(force=True)
                logger.info(
                    "✅ STRATEGY 3: Successfully clicked checkbox with force=True"
                )
                return True

        except Exception as e:
            logger.debug(f"Force click method failed: {e}")

        try:
            # Method 2: Dispatch click event
            checkboxes = page.locator('input[type="checkbox"]')
            if await checkboxes.count() > 0:
                first_checkbox = checkboxes.first
                await first_checkbox.dispatch_event("click")
                logger.info(
                    "✅ STRATEGY 3: Successfully dispatched click event on checkbox"
                )
                return True

        except Exception as e:
            logger.debug(f"Dispatch event method failed: {e}")

        return False

    async def _try_javascript_checkbox_methods(self, browser):
        """Try JavaScript evaluation methods for programmatic checkbox control."""
        logger.info("🔍 Strategy 4: JavaScript evaluation methods")
        page = browser.page

        try:
            # Method 1: Direct JavaScript checkbox manipulation
            result = await page.evaluate("""
                () => {
                    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    if (checkboxes.length > 0) {
                        const checkbox = checkboxes[0]; // First checkbox (Terms of Service)
                        checkbox.checked = true;

                        // Trigger change event to notify JavaScript frameworks
                        const changeEvent = new Event('change', { bubbles: true });
                        checkbox.dispatchEvent(changeEvent);

                        // Also trigger click event
                        const clickEvent = new Event('click', { bubbles: true });
                        checkbox.dispatchEvent(clickEvent);

                        return checkbox.checked;
                    }
                    return false;
                }
            """)

            if result:
                logger.info(
                    "✅ STRATEGY 4: Successfully checked checkbox using JavaScript evaluation"
                )
                return True

        except Exception as e:
            logger.debug(f"JavaScript evaluation method failed: {e}")

        try:
            # Method 2: Find checkbox by text content and check it
            result = await page.evaluate("""
                () => {
                    // Look for checkboxes near "Terms of Service" text
                    const allElements = document.querySelectorAll('*');
                    for (let element of allElements) {
                        if (element.textContent && element.textContent.includes('Terms of Service')) {
                            // Look for checkbox in this element or nearby
                            const checkbox = element.querySelector('input[type="checkbox"]') ||
                                           element.parentElement?.querySelector('input[type="checkbox"]') ||
                                           element.previousElementSibling?.querySelector('input[type="checkbox"]') ||
                                           element.nextElementSibling?.querySelector('input[type="checkbox"]');

                            if (checkbox) {
                                checkbox.checked = true;
                                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                checkbox.dispatchEvent(new Event('click', { bubbles: true }));
                                return true;
                            }
                        }
                    }
                    return false;
                }
            """)

            if result:
                logger.info(
                    "✅ STRATEGY 4: Successfully found and checked Terms checkbox using text search"
                )
                return True

        except Exception as e:
            logger.debug(f"JavaScript text search method failed: {e}")

        return False

    async def _try_label_click_methods(self, browser):
        """Try clicking labels and parent elements to activate checkboxes."""
        logger.info("🔍 Strategy 5: Label and parent element clicking methods")
        page = browser.page

        try:
            # Method 1: Click labels that contain "Terms of Service"
            labels = page.locator(
                'label:has-text("Terms of Service"), label:has-text("I agree"), label:has-text("terms")'
            )
            label_count = await labels.count()
            logger.info(f"Found {label_count} potential terms labels")

            if label_count > 0:
                first_label = labels.first
                await first_label.click(force=True)
                logger.info(
                    "✅ STRATEGY 5: Successfully clicked Terms of Service label"
                )
                return True

        except Exception as e:
            logger.debug(f"Label clicking method failed: {e}")

        try:
            # Method 2: Click parent elements of checkboxes
            checkboxes = page.locator('input[type="checkbox"]')
            if await checkboxes.count() > 0:
                first_checkbox = checkboxes.first
                parent = first_checkbox.locator("..")
                await parent.click(force=True)
                logger.info(
                    "✅ STRATEGY 5: Successfully clicked checkbox parent element"
                )
                return True

        except Exception as e:
            logger.debug(f"Parent element clicking method failed: {e}")

        try:
            # Method 3: Look for clickable elements with Terms text
            terms_elements = page.locator(
                '*:has-text("Terms of Service"), *:has-text("I agree")'
            )
            if await terms_elements.count() > 0:
                first_element = terms_elements.first
                await first_element.click(force=True)
                logger.info("✅ STRATEGY 5: Successfully clicked Terms text element")
                return True

        except Exception as e:
            logger.debug(f"Terms text element clicking failed: {e}")

        return False

    async def _verify_checkbox_state(self, browser):
        """Verify that checkboxes are properly checked."""
        page = browser.page
        try:
            checked_checkbox = page.locator('input[type="checkbox"]:checked')
            checked_count = await checked_checkbox.count()
            logger.info(f"✅ Total checked checkboxes: {checked_count}")

            # Also verify using JavaScript
            js_checked_count = await page.evaluate("""
                () => {
                    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    let checkedCount = 0;
                    checkboxes.forEach(cb => {
                        if (cb.checked) checkedCount++;
                    });
                    return checkedCount;
                }
            """)
            logger.info(
                f"✅ JavaScript verified checked checkboxes: {js_checked_count}"
            )

        except Exception as e:
            logger.warning(f"Could not verify checkbox state: {e}")

        # Focus on clicking Complete Onboarding button
        return await self._click_complete_onboarding_button(browser)

    async def _click_complete_onboarding_button(self, browser):
        """Enhanced Complete Onboarding button clicking with multiple strategies."""
        logger.info("🎯 Attempting to click Complete Onboarding button")
        page = browser.page

        # Strategy 1: Standard button clicking
        button_clicked = await self._try_standard_button_click(browser)
        if button_clicked:
            return await self._verify_wizard_completion(browser)

        # Strategy 2: Force clicking methods
        button_clicked = await self._try_force_button_click(browser)
        if button_clicked:
            return await self._verify_wizard_completion(browser)

        # Strategy 3: JavaScript button clicking
        button_clicked = await self._try_javascript_button_click(browser)
        if button_clicked:
            return await self._verify_wizard_completion(browser)

        logger.error("❌ CRITICAL: Could not click any Complete Onboarding button!")
        await self._debug_screenshot(browser, "error_no_complete_button")
        return False

    async def _try_standard_button_click(self, browser):
        """Try standard button clicking methods."""
        logger.info("🔍 Strategy 1: Standard button clicking")
        page = browser.page

        complete_buttons = [
            'button:has-text("Complete Onboarding")',
            'button:has-text("Complete")',
            'button:has-text("Finish")',
            'button:has-text("Done")',
            '[data-testid*="complete"]',
            '[data-testid*="finish"]',
        ]

        for button_selector in complete_buttons:
            try:
                logger.info(f"🔍 Looking for button: {button_selector}")
                finish_button = page.locator(button_selector).first

                # Check if button exists and is visible
                if await finish_button.is_visible():
                    logger.info(f"✅ Found visible button: {button_selector}")

                    # Wait for button to be enabled (not disabled)
                    try:
                        await page.wait_for_function(
                            f"""() => {{
                                const button = document.querySelector('{button_selector.replace(":", "\\:")}');
                                return button && !button.disabled && button.offsetParent !== null;
                            }}""",
                            timeout=10000,
                        )
                        logger.info(
                            f"✅ Button is enabled and clickable: {button_selector}"
                        )
                    except:
                        logger.warning(
                            f"⚠️ Button may not be enabled yet: {button_selector}"
                        )

                    # Try to click the button
                    await finish_button.click()
                    logger.info(
                        f"✅ STRATEGY 1: Successfully clicked {button_selector}"
                    )
                    await asyncio.sleep(3)  # Wait for action to complete
                    return True
                else:
                    logger.info(f"Button not visible: {button_selector}")

            except Exception as e:
                logger.debug(f"Standard button {button_selector} failed: {e}")
                continue

        return False

    async def _try_force_button_click(self, browser):
        """Try force clicking methods for Complete Onboarding button."""
        logger.info("🔍 Strategy 2: Force button clicking")
        page = browser.page

        complete_buttons = [
            'button:has-text("Complete Onboarding")',
            'button:has-text("Complete")',
            'button:has-text("Finish")',
        ]

        for button_selector in complete_buttons:
            try:
                finish_button = page.locator(button_selector).first
                if await finish_button.count() > 0:
                    await finish_button.click(force=True)
                    logger.info(f"✅ STRATEGY 2: Force clicked {button_selector}")
                    await asyncio.sleep(3)
                    return True

            except Exception as e:
                logger.debug(f"Force button click {button_selector} failed: {e}")
                continue

        return False

    async def _try_javascript_button_click(self, browser):
        """Try JavaScript methods to click Complete Onboarding button."""
        logger.info("🔍 Strategy 3: JavaScript button clicking")
        page = browser.page

        try:
            # Method 1: Find and click button by text content
            result = await page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button');
                    for (let button of buttons) {
                        if (button.textContent &&
                            (button.textContent.includes('Complete Onboarding') ||
                             button.textContent.includes('Complete') ||
                             button.textContent.includes('Finish'))) {
                            button.click();
                            return true;
                        }
                    }
                    return false;
                }
            """)

            if result:
                logger.info(
                    "✅ STRATEGY 3: Successfully clicked button using JavaScript"
                )
                await asyncio.sleep(3)
                return True

        except Exception as e:
            logger.debug(f"JavaScript button click failed: {e}")

        return False

    async def _verify_wizard_completion(self, browser):
        """Verify that the setup wizard has been completed."""
        logger.info("🔍 Verifying wizard completion...")
        page = browser.page

        # Wait for wizard completion
        await asyncio.sleep(5)
        await self._debug_screenshot(browser, "wizard_complete")

        try:
            # Wait a bit more for any transitions
            await asyncio.sleep(3)

            # Check multiple indicators that wizard is closed
            wizard_indicators = [
                'text="Step 4 of 4"',  # Step indicator should be gone
                'text="Welcome to Firecrawl!"',  # Welcome text should be gone
                'button:has-text("Complete Onboarding")',  # Complete button should be gone
            ]

            wizard_still_open = False
            for indicator in wizard_indicators:
                try:
                    if await page.locator(indicator).is_visible(timeout=2000):
                        logger.warning(f"⚠️ Wizard indicator still visible: {indicator}")
                        wizard_still_open = True
                        break
                except:
                    # If locator times out, that's good - element is not visible
                    continue

            if wizard_still_open:
                logger.warning("⚠️ Wizard still appears to be open")
                await self._debug_screenshot(browser, "error_wizard_still_open")
                return False
            else:
                logger.info(
                    "✅ Setup wizard completed successfully - all wizard elements are gone"
                )
                return True

        except Exception as e:
            logger.error(f"Error verifying wizard completion: {e}")
            # If we can't verify, assume it worked if we got this far
            logger.info(
                "✅ Setup wizard appears to have completed (verification failed but assuming success)"
            )
            return True

    async def _verify_dashboard_access(self, browser) -> bool:
        """Verify that we successfully accessed the Firecrawl dashboard after verification."""
        try:
            logger.info("🔍 Verifying dashboard access...")
            await asyncio.sleep(5)  # Wait for page to settle and redirects

            current_url = browser.page.url
            logger.info(f"Current URL after verification: {current_url}")

            # Simple and direct: Check if we're on https://www.firecrawl.dev/app (the dashboard)
            if "firecrawl.dev/app" in current_url:
                logger.info(
                    "✅ Successfully verified dashboard access! On firecrawl.dev/app"
                )
                return True

            # Also check for other dashboard URLs
            dashboard_urls = ["/dashboard", "/app", "/setup"]
            for url_pattern in dashboard_urls:
                if url_pattern in current_url:
                    logger.info(f"✅ Dashboard URL detected: {url_pattern}")
                    return True

            # Look for basic dashboard indicators (simplified)
            dashboard_elements = [
                'text="Welcome to Firecrawl"',
                'text="API Key"',
                'text="Step 1 of 4"',  # Setup wizard
                'button:has-text("Next")',
            ]

            for selector in dashboard_elements:
                try:
                    element = await browser.page.query_selector(selector)
                    if element and await element.is_visible():
                        logger.info(f"✅ Found dashboard element: {selector}")
                        return True
                except:
                    continue

            # If no clear indicators, assume success (like the test script does)
            logger.info(
                "✅ Assuming dashboard access success (no error indicators found)"
            )
            return True

        except Exception as e:
            logger.error(f"Error verifying dashboard access: {e}")
            # On error, assume success to avoid false negatives
            return True
