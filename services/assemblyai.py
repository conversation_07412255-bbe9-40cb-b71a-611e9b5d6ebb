"""AssemblyAI service automation."""

import asyncio
import re
from typing import Optional, Dict, Any
from core.browser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from core.models import RegistrationR<PERSON>ult, ServiceCredentials
from core.config import ServiceConfig, Config
from core.profile import ProfileGenerator
from core.email_client_factory import EmailClientFactory
from core.domain_selector import DomainSelector, EmailDomain
from core.randomizer import Randomizer, create_randomizer, RandomizationProfile
import logging

logger = logging.getLogger(__name__)


class AssemblyAIService:
    """Automate AssemblyAI registration and API key extraction."""

    def __init__(self, config: ServiceConfig, full_config: Config):
        self.config = config
        self.full_config = full_config

        # Initialize centralized randomizer
        randomizer_config = getattr(full_config, "randomizer", None)
        if randomizer_config:
            self.randomizer = Randomizer(randomizer_config)
        else:
            # Use default randomizer configuration
            self.randomizer = create_randomizer(
                RandomizationProfile.NORMAL, enabled=True
            )

        # Get email domains for this service
        service_domains = full_config.get_domains_for_service("assemblyai")
        if not service_domains:
            raise ValueError("No email domains configured for AssemblyAI")

        # Initialize domain selector for load balancing
        self.domain_selector = DomainSelector(
            service_domains, randomizer=self.randomizer
        )

        # Select a domain for this registration
        self.selected_domain = self.domain_selector.select_domain()
        if not self.selected_domain:
            raise ValueError("No email domains configured")

        logger.info(f"Selected email domain: {self.selected_domain}")

        self.profile_generator = ProfileGenerator(
            self.selected_domain, randomizer=self.randomizer
        )
        self.browser_manager: Optional[BrowserManager] = None
        self.profile = None

        # Initialize email client based on configuration
        self.email_client = EmailClientFactory.create_client()

    def _get_headless_setting(self) -> Optional[bool]:
        """Get headless setting from service config with default to global config."""
        if self.config.headless is not None:
            return self.config.headless
        # Don't override - let BrowserManager handle global engine configs
        return None

    def _get_browser_engines(self) -> list:
        """Get browser engines list from service config or fall back to global default."""
        if self.config.browser_engines is not None:
            return self.config.browser_engines
        return self.full_config.browser_engines

    async def _check_for_error_messages(self, page) -> Optional[str]:
        """Check for error messages that indicate anti-bot detection or other issues."""
        try:
            # Common error message selectors for AssemblyAI
            error_selectors = [
                '[data-testid*="error"]',
                ".error",
                ".error-message",
                '[class*="error"]',
                '[role="alert"]',
                ".alert-error",
                ".text-red-500",  # Tailwind CSS red text
                ".text-red-600",
                ".text-red-700",
                '[style*="color: red"]',
                '[style*="color:red"]',
                '[style*="color:#ff"]',  # Various red hex codes
                '[style*="color: #ff"]',
            ]

            for selector in error_selectors:
                try:
                    error_elements = await page.query_selector_all(selector)
                    for element in error_elements:
                        if element:
                            # Check if element is visible
                            is_visible = await element.is_visible()
                            if is_visible:
                                text_content = await element.text_content()
                                if text_content and text_content.strip():
                                    logger.warning(
                                        f"Error message detected: {text_content.strip()}"
                                    )
                                    return text_content.strip()
                except Exception:
                    continue  # Try next selector

            # Also check for any text that might indicate an error
            page_content = await page.content()
            error_keywords = [
                "error occurred",
                "something went wrong",
                "please try again",
                "invalid email",
                "blocked",
                "suspicious activity",
                "verification failed",
            ]

            for keyword in error_keywords:
                if keyword.lower() in page_content.lower():
                    logger.warning(f"Error keyword detected in page: {keyword}")
                    return f"Page contains error keyword: {keyword}"

        except Exception as e:
            logger.debug(f"Error checking for error messages: {e}")

        return None

    async def _simulate_human_form_interaction(self, browser) -> None:
        """Simulate human-like behavior before interacting with forms."""
        try:
            # Simulate reading the page
            await asyncio.sleep(self.randomizer.uniform(1.0, 2.5))

            # Random mouse movements
            viewport = await browser.page.evaluate("""
                () => ({
                    width: window.innerWidth,
                    height: window.innerHeight
                })
            """)

            # Move mouse to a few random positions
            for _ in range(self.randomizer.randint(2, 4)):
                x = self.randomizer.randint(100, viewport["width"] - 100)
                y = self.randomizer.randint(100, viewport["height"] - 100)
                await browser.page.mouse.move(x, y)
                await asyncio.sleep(self.randomizer.uniform(0.3, 0.8))

            # Small scroll to simulate reading
            scroll_amount = self.randomizer.randint(50, 150)
            await browser.page.evaluate(f"window.scrollBy(0, {scroll_amount})")
            await asyncio.sleep(self.randomizer.uniform(0.5, 1.0))

        except Exception as e:
            logger.debug(f"Error in human simulation: {e}")

    async def _type_like_human(self, element, text: str) -> None:
        """Type text with human-like delays and occasional corrections."""
        try:
            # Clear any existing content first
            await element.fill("")
            await asyncio.sleep(self.randomizer.uniform(0.1, 0.3))

            # Type character by character with human-like delays
            for i, char in enumerate(text):
                # Occasional typo simulation (very rare)
                if self.randomizer.random() < 0.02 and i > 0:  # 2% chance of typo
                    # Type wrong character then backspace
                    wrong_char = self.randomizer.choice("abcdefghijklmnopqrstuvwxyz")
                    await element.type(wrong_char)
                    await asyncio.sleep(self.randomizer.uniform(0.1, 0.3))
                    await element.press("Backspace")
                    await asyncio.sleep(self.randomizer.uniform(0.1, 0.2))

                # Type the correct character
                await element.type(char)

                # Human-like delay between keystrokes
                delay = self.randomizer.gauss(
                    0.12, 0.04
                )  # Normal distribution around 120ms
                delay = max(0.05, min(0.3, delay))  # Clamp between 50ms and 300ms
                await asyncio.sleep(delay)

        except Exception as e:
            logger.debug(f"Error in human typing: {e}")
            # Fallback to regular fill
            await element.fill(text)

    @staticmethod
    def parse_magic_link(raw_email_content: str) -> Optional[str]:
        """Parse AssemblyAI magic link (Stytch-based) from email content.

        This method can be used by both webhook server and automation scripts.
        It handles the complex quoted-printable encoding that Stytch emails use.
        """
        if not raw_email_content:
            return None

        # AssemblyAI uses Stytch for magic links
        # Handle quoted-printable encoding where URLs are broken across lines with = at end

        # First, try to find the URL in HTML content (more reliable)
        # Use dotall flag to handle multiline content - look for href=3D"https://stytch pattern
        html_match = re.search(
            r'href=3D"(https://stytch[^"]*)', raw_email_content, re.DOTALL
        )
        if html_match:
            # Decode the HTML entities and URL encoding, handle line breaks
            url = html_match.group(1)
            url = url.replace("=\r\n", "").replace(
                "=\n", ""
            )  # Remove quoted-printable line breaks first
            url = url.replace("3D", "=")  # Decode 3D to = (key fix!)
            url = url.replace("&amp;", "&")  # Decode &amp; to &

            # Fix specific formatting issues from quoted-printable encoding
            url = url.replace(
                "public-token-live=-", "public-token-live-"
            )  # Fix live=- issue
            url = url.replace("==", "=")  # Fix any double equals from over-decoding

            logger.info(f"Extracted AssemblyAI magic link from HTML: {url[:50]}...")
            return url

        # Second, try plain text version (handle line breaks with =)
        text_pattern = r"https://stytch\.com/v1/magic_links/redirect\?[^\s]+"
        text_match = re.search(text_pattern, raw_email_content)
        if text_match:
            url = text_match.group(0)

            # Look for continuation lines after the initial match
            lines = raw_email_content.split("\n")
            found_index = -1

            for i, line in enumerate(lines):
                if url in line:
                    found_index = i
                    break

            if found_index >= 0:
                # Reconstruct URL from multiple lines
                full_url = lines[found_index]
                for i in range(found_index + 1, len(lines)):
                    line = lines[i]
                    if line.startswith("=") or line.startswith("-") or "&" in line:
                        full_url += line
                    else:
                        break

                # Clean up the URL
                cleaned_url = full_url
                cleaned_url = cleaned_url.replace("=\r\n", "").replace(
                    "=\n", ""
                )  # Remove line breaks
                cleaned_url = cleaned_url.replace("=3D", "=")  # Decode =3D to =

                # Extract just the URL part
                final_match = re.search(text_pattern, cleaned_url)
                if final_match:
                    logger.info(
                        f"Extracted AssemblyAI magic link from text: {final_match.group(0)[:50]}..."
                    )
                    return final_match.group(0)

            logger.info(f"Extracted AssemblyAI magic link (simple): {url[:50]}...")
            return url

        logger.warning("No AssemblyAI magic link found in email content")
        return None

    @staticmethod
    def parse_email_data(email_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse email data and extract magic link for AssemblyAI.

        This method can be used by both webhook server and automation scripts.
        """
        raw_content = email_data.get("raw", "")
        if not raw_content:
            logger.warning("No raw content for AssemblyAI email parsing")
            return email_data

        # Parse magic link
        magic_link = AssemblyAIService.parse_magic_link(raw_content)
        if magic_link:
            email_data["magicLink"] = magic_link

        # Add parsing metadata
        email_data["parsed_by"] = "assemblyai_service"
        email_data["parser_version"] = "1.0"

        return email_data

    async def register_and_get_api_key(self) -> RegistrationResult:
        """Main workflow: go to login page, enter email, wait for magic link, then process it."""
        try:
            # Generate profile data
            self.profile = self.profile_generator.generate_profile()
            logger.info(f"Generated profile for {self.profile['email']}")

            async with BrowserManager(
                browser_engines=self._get_browser_engines(),
                global_engine_configs=self.full_config.browser_engines,
                service_headless_override=self._get_headless_setting(),
                randomizer=self.randomizer,  # Pass the centralized randomizer
                service_name="assemblyai",  # Pass service name for BitBrowser optimization
            ) as browser:
                self.browser_manager = browser

                # Log selected browser engine for debugging
                logger.info(f"🌐 Selected browser engine: {browser.browser_engine}")

                # Flexible navigation: start_url (homepage) vs signup_url (direct)
                if self.config.start_url:
                    # Navigate to homepage first, then click Sign Up button (natural flow)
                    logger.info(f"Navigating to homepage: {self.config.start_url}")
                    try:
                        await browser.goto(self.config.start_url)
                        await browser.wait(3000)  # Wait for initial page load
                    except Exception as e:
                        # If networkidle fails, try with domcontentloaded
                        logger.warning(f"Initial navigation failed: {e}, trying with domcontentloaded")
                        await browser.page.goto(self.config.start_url, wait_until="domcontentloaded", timeout=30000)
                        await browser.wait(5000)

                    # Look for Sign Up / Get Started button
                    logger.info("🔍 Looking for 'Sign Up' or 'Get Started' button")
                    signup_nav_selectors = [
                        'a:has-text("Sign up")',
                        'a:has-text("Get started")', 
                        'button:has-text("Sign up")',
                        'button:has-text("Get started")',
                        '[href*="/signup"]',
                        '[href*="/register"]', 
                        '[href*="/dashboard"]',
                        'nav a:has-text("Sign up")',
                        'header a:has-text("Sign up")',
                    ]

                    button_found = False
                    for selector in signup_nav_selectors:
                        try:
                            signup_button = browser.page.locator(selector).first
                            if await signup_button.is_visible():
                                await signup_button.click()
                                logger.info(f"✅ Clicked Sign Up button with selector: {selector}")
                                button_found = True
                                await browser.wait(3000)  # Wait for signup page to load
                                break
                        except Exception as e:
                            logger.debug(f"Sign Up button selector {selector} failed: {e}")
                            continue

                    if not button_found:
                        if self.config.signup_url:
                            logger.info("⚠️ Could not find Sign Up button, falling back to direct signup URL")
                            await browser.goto(self.config.signup_url)
                            await browser.wait(3000)
                        else:
                            logger.error("❌ Could not find 'Sign Up' button and no signup_url configured")
                            return RegistrationResult(success=False, error_message="Could not find Sign Up button", service="assemblyai")

                elif self.config.signup_url:
                    # Direct navigation to signup page
                    logger.info(f"Navigating directly to signup page: {self.config.signup_url}")
                    try:
                        await browser.goto(self.config.signup_url)
                        await browser.wait(3000)  # Wait for initial page load
                    except Exception as e:
                        # If networkidle fails, try with domcontentloaded
                        logger.warning(f"Initial navigation failed: {e}, trying with domcontentloaded")
                        await browser.page.goto(self.config.signup_url, wait_until="domcontentloaded", timeout=30000)
                        await browser.wait(5000)

                else:
                    logger.error("❌ Neither start_url nor signup_url configured")
                    return RegistrationResult(success=False, error_message="No start_url or signup_url configured", service="assemblyai")

                # Handle OneTrust cookie consent that may appear
                try:
                    # Wait for page to load and check for cookie popup
                    await browser.wait(3000)

                    # Look for OneTrust consent popup and dismiss it
                    onetrust_accept = await browser.page.query_selector(
                        "#onetrust-accept-btn-handler"
                    )
                    if onetrust_accept and await onetrust_accept.is_visible():
                        await onetrust_accept.click()
                        logger.info("Clicked OneTrust Accept All button")
                        await browser.wait(2000)  # Wait for popup to disappear
                except Exception as e:
                    logger.debug(f"Cookie handling: {e}")
                    # Continue anyway

                # Step 2: Enter email and submit
                logger.info(f"Entering email: {self.profile['email']}")

                # Add longer initial delay to appear more human
                await asyncio.sleep(self.randomizer.uniform(2.0, 4.0))

                # Add human-like behavior before interacting with form
                await self._simulate_human_form_interaction(browser)

                # Additional delay after simulation
                await asyncio.sleep(self.randomizer.uniform(1.0, 2.0))

                # Fill email input using the correct test-id selector with human-like typing
                email_input = browser.page.get_by_test_id("email")
                await email_input.click()  # Focus the input first
                await asyncio.sleep(self.randomizer.uniform(0.3, 0.8))  # Longer pause

                # Type email with human-like delays
                await self._type_like_human(email_input, self.profile["email"])

                # Longer pause before clicking submit (more human-like)
                await asyncio.sleep(self.randomizer.uniform(1.5, 3.0))

                # Click continue/submit button using the correct test-id selector
                submit_button = browser.page.get_by_test_id("continue-with-email")
                await submit_button.click()

                # Wait a moment for any error messages to appear
                await asyncio.sleep(2)

                # Check for error messages that indicate anti-bot detection
                error_detected = await self._check_for_error_messages(browser.page)
                if error_detected:
                    return RegistrationResult(
                        service="assemblyai",
                        success=False,
                        error_message=f"Anti-bot detection triggered: {error_detected}",
                    )

                logger.info("Email submitted, waiting for magic link...")

                # Step 3: Wait for magic link (10-30 seconds) - match to current email
                magic_link = None
                for attempt in range(6):  # Check every 5 seconds for 30 seconds total
                    await asyncio.sleep(5)

                    # Get raw email content for this specific email
                    raw_content = await self.email_client.get_latest_email_content(
                        "assemblyai", self.profile["email"]
                    )
                    if raw_content:
                        # Parse magic link from raw content
                        magic_link = self.parse_magic_link(raw_content)
                        if magic_link:
                            logger.info(
                                f"Magic link received for {self.profile['email']} after {(attempt + 1) * 5} seconds"
                            )
                            break

                    logger.info(
                        f"Waiting for magic link for {self.profile['email']}... ({(attempt + 1) * 5}s)"
                    )

                if not magic_link:
                    # Fallback: Try to parse the magic link locally from raw email content for this specific email
                    logger.info(
                        f"Attempting local magic link parsing as fallback for {self.profile['email']}..."
                    )
                    try:
                        raw_email_data = await self._get_raw_email_content_for_email(
                            self.profile["email"]
                        )
                        if raw_email_data:
                            magic_link = self._parse_magic_link_locally(raw_email_data)
                            if magic_link:
                                logger.info(
                                    f"Successfully parsed magic link locally for {self.profile['email']}: {magic_link[:50]}..."
                                )
                            else:
                                logger.error(
                                    f"Local parsing also failed to extract magic link for {self.profile['email']}"
                                )
                    except Exception as e:
                        logger.error(
                            f"Local parsing failed for {self.profile['email']}: {e}"
                        )

                if not magic_link:
                    return RegistrationResult(
                        service="assemblyai",
                        success=False,
                        error_message=f"No magic link received after 30 seconds for {self.profile['email']}",
                    )

                # Step 4: Process the magic link in the same browser window
                return await self._process_magic_link(magic_link, browser)

        except Exception as e:
            logger.error(f"Error during AssemblyAI automation: {e}")
            return RegistrationResult(
                service="assemblyai", success=False, error_message=str(e)
            )

    async def _process_magic_link(
        self, magic_link: str, browser: BrowserManager
    ) -> RegistrationResult:
        """Process the magic link in the existing browser window."""
        try:
            # Navigate to magic link in the same browser window
            logger.info(f"Navigating to magic link: {magic_link}")
            await browser.page.goto(
                magic_link, wait_until="domcontentloaded", timeout=30000
            )

            # Wait for page to load and redirect to AssemblyAI dashboard
            # The magic link redirects through Stytch to AssemblyAI, so we need to wait longer
            logger.info("Waiting for page to fully load and redirect...")
            await browser.wait(5000)  # Initial wait for redirect

            # Wait for the page to stabilize (up to 25 seconds)
            for attempt in range(5):  # 5 attempts * 5 seconds = 25 seconds max
                await browser.wait(5000)

                # Check if we're on AssemblyAI domain and page has loaded
                current_url = browser.page.url
                logger.info(
                    f"Current URL after {(attempt + 1) * 5} seconds: {current_url}"
                )

                # Look for API key early to break out if found
                api_key_element = await browser.page.query_selector(
                    'input[type="text"][readonly]'
                )
                if api_key_element:
                    logger.info(
                        f"Found API key element after {(attempt + 1) * 5} seconds"
                    )
                    break

                # Also check for text-based API key display
                page_text = await browser.page.text_content("body") or ""
                if (
                    "sk-" in page_text
                    and len(
                        [
                            line
                            for line in page_text.split()
                            if line.startswith("sk-") and len(line) > 20
                        ]
                    )
                    > 0
                ):
                    logger.info(
                        f"Found potential API key in text after {(attempt + 1) * 5} seconds"
                    )
                    break

            logger.info(
                "Finished waiting for page load, proceeding with API key extraction"
            )

            # The page should redirect to AssemblyAI welcome page with API key
            # Wait for API key element - adjust selector based on actual page
            try:
                # Look for API key input field with multiple strategies
                api_key_element = None
                api_key = None

                # Strategy 1: Look for the API key text directly (it's displayed as text, not input)
                # The API key appears to be in a div or span element
                api_key_elements = await browser.page.query_selector_all("*")
                for elem in api_key_elements:
                    text = await elem.text_content() or ""
                    # API keys are exactly 32 characters of hex
                    if len(text) == 32 and all(
                        c in "0123456789abcdef" for c in text.lower()
                    ):
                        api_key = text
                        api_key_element = elem
                        logger.info(f"Found API key as text: {api_key}")
                        break

                # Strategy 2: Look for readonly text input if Strategy 1 fails
                if not api_key:
                    api_key_element = await browser.page.query_selector(
                        'input[type="text"][readonly]'
                    )
                    if api_key_element:
                        api_key = await api_key_element.get_attribute("value")

                # Strategy 3: Look for any text input with a value that looks like an API key
                if not api_key:
                    all_inputs = await browser.page.query_selector_all(
                        'input[type="text"]'
                    )
                    for input_elem in all_inputs:
                        value = await input_elem.get_attribute("value") or ""
                        if len(value) == 32 and all(
                            c in "0123456789abcdef" for c in value.lower()
                        ):
                            api_key = value
                            api_key_element = input_elem
                            break

                if api_key:
                    logger.info(f"Found API key: {api_key}")

                    # Click the Copy button - it's a blue button with "Copy" text
                    copy_button = None
                    copy_selectors = [
                        'button:has-text("Copy")',
                        'button:text("Copy")',
                        '[role="button"]:has-text("Copy")',
                        'button[aria-label*="Copy"]',
                        ".copy-button",
                        '[data-testid*="copy"]',
                        'button:has-text("copy")',  # lowercase
                        # Try more specific selectors
                        'button[type="button"]:has-text("Copy")',
                    ]

                    for selector in copy_selectors:
                        try:
                            copy_button = await browser.page.query_selector(selector)
                            if copy_button:
                                logger.info(
                                    f"Found copy button with selector: {selector}"
                                )
                                break
                        except:
                            continue

                    if copy_button:
                        await copy_button.click()
                        logger.info("Clicked Copy button")
                        await browser.wait(2000)  # Wait for copy animation/feedback
                    else:
                        logger.warning("Could not find Copy button - continuing anyway")

                    # Wait before proceeding
                    wait_time = self.randomizer.randint(2, 5)
                    logger.info(
                        f"Waiting {wait_time} seconds before proceeding to dashboard..."
                    )
                    await browser.wait(wait_time * 1000)

                    # Look for "Skip and go to my dashboard" - it's in top right
                    continue_button = None
                    continue_selectors = [
                        'a:has-text("Skip and go to my dashboard")',
                        ':has-text("Skip and go to my dashboard")',
                        'button:has-text("Continue to dashboard")',
                        'a:has-text("Continue to dashboard")',
                        'button:has-text("Go to dashboard")',
                        'a:has-text("Go to dashboard")',
                        'button:has-text("Continue")',
                        'a:has-text("Continue")',
                        'button:has-text("Dashboard")',
                        'a:has-text("Dashboard")',
                        'button[data-testid*="continue"]',
                        'button[data-testid*="dashboard"]',
                        # Try more specific
                        'a[href*="dashboard"]',
                    ]

                    for selector in continue_selectors:
                        try:
                            continue_button = await browser.page.query_selector(
                                selector
                            )
                            if continue_button:
                                logger.info(
                                    f"Found continue button with selector: {selector}"
                                )
                                break
                        except:
                            continue

                    if continue_button:
                        await continue_button.click()
                        logger.info("Clicked Continue to dashboard button")
                        # Stay longer to complete the flow properly
                        stay_time = self.randomizer.randint(10, 20)
                        logger.info(
                            f"Staying for {stay_time} seconds to complete the registration flow..."
                        )
                        await browser.wait(stay_time * 1000)
                    else:
                        logger.warning(
                            "Could not find Continue to dashboard button - continuing anyway"
                        )
                        await browser.wait(8000)  # Still wait longer

                    return RegistrationResult(
                        service="assemblyai",
                        success=True,
                        api_key=api_key.strip(),
                        email=self.profile["email"],
                        username=self.profile["username"],
                    )
                else:
                    return RegistrationResult(
                        service="assemblyai",
                        success=False,
                        error_message="Could not find API key element on page",
                    )

            except Exception as inner_e:
                logger.error(f"Error in API key extraction: {inner_e}")
                return RegistrationResult(
                    service="assemblyai",
                    success=False,
                    error_message=f"Error extracting API key: {str(inner_e)}",
                )

        except Exception as e:
            logger.error(f"Error processing magic link: {e}")
            return RegistrationResult(
                service="assemblyai", success=False, error_message=str(e)
            )

    async def _get_raw_email_content(self) -> str:
        """Get raw email content using the configured client for the current profile."""
        if not self.profile:
            logger.error("No profile available for email content retrieval")
            return ""

        return await self._get_raw_email_content_for_email(self.profile["email"])

    async def _get_raw_email_content_for_email(self, email: str) -> str:
        """Get raw email content for a specific email address."""
        try:
            # Get raw email content using the configured client
            raw_content = await self.email_client.get_latest_email_content(
                "assemblyai", email
            )
            if raw_content:
                logger.info(
                    f"Retrieved raw email content for {email}: {len(raw_content)} characters"
                )
                return raw_content
            else:
                logger.warning(f"No raw email content found for {email}")
                return ""

        except Exception as e:
            logger.error(f"Error getting raw email content for {email}: {e}")
            return ""

    def _parse_magic_link_locally(self, raw_body: str) -> str:
        """Parse magic link locally from raw email content."""
        import re

        # Try HTML content first (more reliable)
        # Look for href=3D"https://stytch... pattern and handle line breaks
        html_pattern = r"href=3D\"(https://stytch[^\"]*)"
        html_match = re.search(html_pattern, raw_body, re.DOTALL)
        if html_match:
            url = html_match.group(1)
            # Decode HTML entities and quoted-printable line breaks
            url = url.replace("=\r\n", "").replace(
                "=\n", ""
            )  # Remove quoted-printable line breaks
            url = url.replace("3D", "=")  # Decode 3D to = (key fix!)
            url = url.replace("&amp;", "&")  # Decode &amp; to &

            # Fix specific formatting issues that might remain
            url = url.replace(
                "public-token-live=-", "public-token-live-"
            )  # Fix live=- issue
            url = url.replace("==", "=")  # Fix any double equals from over-decoding

            return url

        return None
