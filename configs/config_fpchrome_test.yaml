# RegBot Configuration for FPChrome Testing

# Define reusable email domain groups
email_domain_groups:
  primary:
    - domain: "ai.whatisinitfor.me"
      weight: 50
      enabled: true
    - domain: "sg.164136.xyz"
      weight: 30
      enabled: true
    - domain: "iad.164136.xyz"
      weight: 20
      enabled: true

# Email retrieval configuration
email_retrieval:
  client_type: "webhook"
  webhook:
    local_baseurl: "http://localhost:8888"
    external_baseurl: "https://your-tunnel.zrok.io"

# Email interceptor worker configuration
email_interceptor:
  worker_name: "regbot-emailparser"
  store_raw_email: true
  forward_unknown: true
  forward_email: "<EMAIL>"

# Browser settings
browser_headless: false
browser_timeout: 30000
browser_engines:
  fpchrome: {}

# Data storage path
data_path: "data"

# Randomization settings
randomizer:
  enabled: true
  profile: "normal"
  min_delay: 1.0
  max_delay: 3.0
  typing_delay_min: 50
  typing_delay_max: 150

services:
  assemblyai:
    name: "AssemblyAI"
    signup_url: "https://www.assemblyai.com/dashboard/signup"
    email_domain_groups: ["primary"]
    sender_domains: ["assemblyai.com"]
    email_patterns:
      - "<EMAIL>"
      - "assemblyai.com"
    browser_engines:
      - fpchrome
    headless: false
    timeout: 30000
    enabled: true

log_level: INFO