"""Browser-isolated tests that test actual browser functionality without external dependencies."""

import pytest
import asyncio
from core.browser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from core.randomizer import create_randomizer, RandomizationProfile


@pytest.mark.browser
@pytest.mark.slow
class TestBrowserIsolated:
    """Browser tests that use real browsers but avoid external network calls."""

    @pytest.fixture
    def randomizer(self):
        """Create test randomizer."""
        return create_randomizer(RandomizationProfile.TESTING, enabled=True)

    @pytest.fixture
    async def browser_manager(self, randomizer):
        """Create browser manager with real browser for isolated testing."""
        manager = BrowserManager(
            browser_engines=["chromium"],  # Use only chromium for reliable testing
            enable_randomization=False,  # Disable for consistent testing
            randomizer=randomizer,
            timeout=10000,
        )
        await manager.start()
        yield manager
        await manager.stop()

    @pytest.mark.asyncio
    async def test_browser_startup_and_shutdown(self, randomizer):
        """Test basic browser startup and shutdown."""
        manager = BrowserManager(browser_engines=["chromium"], randomizer=randomizer)

        await manager.start()
        assert manager.browser is not None
        assert manager.context is not None
        assert manager.page is not None

        await manager.stop()

    @pytest.mark.asyncio
    async def test_page_navigation_to_data_url(self, browser_manager):
        """Test page navigation using data URLs (no external network)."""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head><title>Test Page</title></head>
        <body>
            <h1>Test Page</h1>
            <input type="text" id="test-input" placeholder="Enter text">
            <button id="test-button">Click me</button>
            <div id="output"></div>
        </body>
        </html>
        """

        data_url = f"data:text/html;base64,{html_content.encode('utf-8').hex()}"
        await browser_manager.goto(data_url)

        # Verify navigation worked
        page_title = await browser_manager.page.title()
        assert "Test Page" in page_title

    @pytest.mark.asyncio
    async def test_element_interaction_basic(self, browser_manager):
        """Test basic element interaction."""
        html_content = """
        <!DOCTYPE html>
        <html>
        <body>
            <input type="text" id="test-input" value="">
            <button id="test-button">Click me</button>
            <div id="output"></div>
            <script>
                document.getElementById('test-button').onclick = function() {
                    const input = document.getElementById('test-input');
                    document.getElementById('output').textContent = 'Clicked: ' + input.value;
                };
            </script>
        </body>
        </html>
        """

        await browser_manager.page.set_content(html_content)

        # Test filling input
        await browser_manager.fill("#test-input", "test value", human_like=False)

        # Test clicking button
        await browser_manager.click("#test-button", human_like=False)

        # Verify interaction worked
        output_text = await browser_manager.get_text("#output")
        assert "Clicked: test value" in output_text

    @pytest.mark.asyncio
    async def test_form_submission_simulation(self, browser_manager):
        """Test form submission simulation."""
        html_content = """
        <!DOCTYPE html>
        <html>
        <body>
            <form id="test-form">
                <input type="email" id="email" name="email" required>
                <input type="password" id="password" name="password" required>
                <button type="submit" id="submit">Submit</button>
            </form>
            <div id="result"></div>
            <script>
                document.getElementById('test-form').onsubmit = function(e) {
                    e.preventDefault();
                    const email = document.getElementById('email').value;
                    const password = document.getElementById('password').value;
                    document.getElementById('result').textContent = 'Form submitted: ' + email;
                };
            </script>
        </body>
        </html>
        """

        await browser_manager.page.set_content(html_content)

        # Fill form fields
        await browser_manager.fill("#email", "<EMAIL>", human_like=False)
        await browser_manager.fill("#password", "testpassword", human_like=False)

        # Submit form
        await browser_manager.click("#submit", human_like=False)

        # Verify form submission
        result_text = await browser_manager.get_text("#result")
        assert "Form submitted: <EMAIL>" in result_text

    @pytest.mark.asyncio
    async def test_javascript_execution(self, browser_manager):
        """Test JavaScript execution in browser."""
        html_content = """
        <!DOCTYPE html>
        <html>
        <body>
            <div id="test-div">Original</div>
            <script>
                function updateText(newText) {
                    document.getElementById('test-div').textContent = newText;
                    return 'Updated to: ' + newText;
                }
            </script>
        </body>
        </html>
        """

        await browser_manager.page.set_content(html_content)

        # Execute JavaScript
        result = await browser_manager.page.evaluate('updateText("Modified by JS")')
        assert result == "Updated to: Modified by JS"

        # Verify DOM was updated
        div_text = await browser_manager.get_text("#test-div")
        assert div_text == "Modified by JS"

    @pytest.mark.asyncio
    async def test_element_visibility_and_waiting(self, browser_manager):
        """Test element visibility and waiting functionality."""
        html_content = """
        <!DOCTYPE html>
        <html>
        <body>
            <button id="show-button">Show Element</button>
            <div id="hidden-element" style="display: none;">Hidden Element</div>
            <script>
                document.getElementById('show-button').onclick = function() {
                    setTimeout(() => {
                        document.getElementById('hidden-element').style.display = 'block';
                    }, 100);
                };
            </script>
        </body>
        </html>
        """

        await browser_manager.page.set_content(html_content)

        # Click button to trigger element appearance
        await browser_manager.click("#show-button", human_like=False)

        # Wait for element to become visible
        element = await browser_manager.wait_for_selector("#hidden-element")
        assert element is not None

        # Verify element is visible
        is_visible = await element.is_visible()
        assert is_visible

    @pytest.mark.asyncio
    async def test_screenshot_functionality(self, browser_manager):
        """Test screenshot functionality."""
        html_content = """
        <!DOCTYPE html>
        <html>
        <body style="background-color: lightblue;">
            <h1>Screenshot Test Page</h1>
            <p>This page is for testing screenshots.</p>
        </body>
        </html>
        """

        await browser_manager.page.set_content(html_content)

        # Take screenshot (should not raise errors)
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
            await browser_manager.screenshot(tmp_file.name)

            # Verify file was created and has content
            assert os.path.exists(tmp_file.name)
            assert os.path.getsize(tmp_file.name) > 0

            # Clean up
            os.unlink(tmp_file.name)

    @pytest.mark.asyncio
    async def test_cookie_handling(self, browser_manager):
        """Test cookie handling functionality."""
        html_content = """
        <!DOCTYPE html>
        <html>
        <body>
            <button id="set-cookie">Set Cookie</button>
            <button id="get-cookie">Get Cookie</button>
            <div id="cookie-output"></div>
            <script>
                document.getElementById('set-cookie').onclick = function() {
                    document.cookie = 'test_cookie=test_value; path=/';
                    document.getElementById('cookie-output').textContent = 'Cookie set';
                };
                
                document.getElementById('get-cookie').onclick = function() {
                    const cookies = document.cookie;
                    document.getElementById('cookie-output').textContent = 'Cookies: ' + cookies;
                };
            </script>
        </body>
        </html>
        """

        await browser_manager.page.set_content(html_content)

        # Set cookie
        await browser_manager.click("#set-cookie", human_like=False)

        # Get cookie
        await browser_manager.click("#get-cookie", human_like=False)

        # Verify cookie was set and retrieved
        output_text = await browser_manager.get_text("#cookie-output")
        assert "test_cookie=test_value" in output_text

    @pytest.mark.asyncio
    async def test_browser_context_isolation(self, randomizer):
        """Test that browser contexts are properly isolated."""
        # Create two separate browser managers
        manager1 = BrowserManager(browser_engines=["chromium"], randomizer=randomizer)
        manager2 = BrowserManager(browser_engines=["chromium"], randomizer=randomizer)

        try:
            await manager1.start()
            await manager2.start()

            # Set different content in each
            await manager1.page.set_content('<div id="test">Manager 1</div>')
            await manager2.page.set_content('<div id="test">Manager 2</div>')

            # Verify isolation
            text1 = await manager1.get_text("#test")
            text2 = await manager2.get_text("#test")

            assert text1 == "Manager 1"
            assert text2 == "Manager 2"

        finally:
            await manager1.stop()
            await manager2.stop()

    @pytest.mark.asyncio
    async def test_local_storage_functionality(self, browser_manager):
        """Test local storage functionality."""
        html_content = """
        <!DOCTYPE html>
        <html>
        <body>
            <button id="set-storage">Set Storage</button>
            <button id="get-storage">Get Storage</button>
            <div id="storage-output"></div>
            <script>
                document.getElementById('set-storage').onclick = function() {
                    localStorage.setItem('test_key', 'test_value');
                    document.getElementById('storage-output').textContent = 'Storage set';
                };
                
                document.getElementById('get-storage').onclick = function() {
                    const value = localStorage.getItem('test_key');
                    document.getElementById('storage-output').textContent = 'Storage value: ' + value;
                };
            </script>
        </body>
        </html>
        """

        await browser_manager.page.set_content(html_content)

        # Set local storage
        await browser_manager.click("#set-storage", human_like=False)

        # Get local storage
        await browser_manager.click("#get-storage", human_like=False)

        # Verify local storage functionality
        output_text = await browser_manager.get_text("#storage-output")
        assert "Storage value: test_value" in output_text

    @pytest.mark.asyncio
    async def test_multiple_tabs_handling(self, browser_manager):
        """Test multiple tabs/pages handling."""
        # Create a new page
        new_page = await browser_manager.context.new_page()

        # Set different content in each page
        await browser_manager.page.set_content('<div id="test">Original Page</div>')
        await new_page.set_content('<div id="test">New Page</div>')

        # Verify each page has different content
        original_text = await browser_manager.page.text_content("#test")
        new_text = await new_page.text_content("#test")

        assert original_text == "Original Page"
        assert new_text == "New Page"

        # Clean up
        await new_page.close()

    @pytest.mark.asyncio
    async def test_browser_timeout_handling(self, randomizer):
        """Test browser timeout handling."""
        # Create browser with very short timeout
        manager = BrowserManager(
            browser_engines=["chromium"],
            randomizer=randomizer,
            timeout=1000,  # 1 second
        )

        await manager.start()

        try:
            # This should timeout quickly if element doesn't exist
            with pytest.raises(Exception):  # Could be TimeoutError or similar
                await manager.wait_for_selector("#non-existent-element")

        finally:
            await manager.stop()

    @pytest.mark.asyncio
    async def test_browser_error_handling(self, browser_manager):
        """Test browser error handling with invalid operations."""
        # Test clicking non-existent element
        with pytest.raises(Exception):
            await browser_manager.click("#non-existent-button", human_like=False)

        # Test filling non-existent input
        with pytest.raises(Exception):
            await browser_manager.fill("#non-existent-input", "value", human_like=False)

    @pytest.mark.asyncio
    async def test_viewport_and_user_agent(self, browser_manager):
        """Test viewport and user agent settings."""
        html_content = """
        <!DOCTYPE html>
        <html>
        <body>
            <div id="viewport-info"></div>
            <div id="user-agent-info"></div>
            <script>
                document.getElementById('viewport-info').textContent = 
                    'Viewport: ' + window.innerWidth + 'x' + window.innerHeight;
                document.getElementById('user-agent-info').textContent = 
                    'User Agent: ' + navigator.userAgent;
            </script>
        </body>
        </html>
        """

        await browser_manager.page.set_content(html_content)

        # Get viewport info
        viewport_text = await browser_manager.get_text("#viewport-info")
        assert "Viewport:" in viewport_text
        assert "x" in viewport_text

        # Get user agent info
        ua_text = await browser_manager.get_text("#user-agent-info")
        assert "User Agent:" in ua_text
        assert len(ua_text) > 20  # Should have substantial user agent string

    @pytest.mark.asyncio
    async def test_randomizer_delay_functionality(self, browser_manager):
        """Test randomizer delay functionality."""
        import time

        # Test that delays actually delay
        start_time = time.time()
        await browser_manager.random_delay(0.1, 0.2)
        end_time = time.time()

        elapsed = end_time - start_time
        assert elapsed >= 0.1  # Should have delayed at least minimum time
        assert elapsed <= 0.5  # Should not take too long

    @pytest.mark.asyncio
    async def test_browser_cleanup_on_error(self, randomizer):
        """Test browser cleanup when errors occur."""
        manager = BrowserManager(browser_engines=["chromium"], randomizer=randomizer)

        await manager.start()

        # Simulate an error condition
        original_page = manager.page

        # Even if an error occurs, cleanup should work
        await manager.stop()

        # Verify cleanup occurred
        assert manager.page is None or manager.page != original_page
