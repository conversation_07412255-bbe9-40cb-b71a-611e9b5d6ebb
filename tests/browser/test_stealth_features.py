#!/usr/bin/env python3
"""Test fpchrome engine stealth capabilities using FingerprintJS."""

import asyncio
import logging
import sys
import json
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.browser import BrowserManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)

logger = logging.getLogger(__name__)


async def test_fingerprintjs_stealth():
    """Test stealth capabilities using FingerprintJS demo site."""
    logger.info("🧪 Testing fpchrome stealth with FingerprintJS")

    try:
        async with BrowserManager(
            headless=False, browser_engines=["fpchrome"], enable_randomization=False
        ) as browser:
            logger.info(f"✅ Browser engine started: {browser.browser_engine}")

            # Navigate to FingerprintJS demo
            logger.info("🌐 Navigating to FingerprintJS demo...")
            await browser.goto("https://fingerprintjs.github.io/fingerprintjs/")
            await browser.wait(3000)

            # Wait for the page to load and run fingerprinting
            logger.info("⏳ Waiting for fingerprinting to complete...")
            await browser.wait(5000)

            # Look for the fingerprint result
            try:
                # Wait for the "Get my visitor identifier" button and click it
                get_id_button = await browser.page.wait_for_selector(
                    'button:has-text("Get my visitor identifier")', timeout=10000
                )
                if get_id_button:
                    await get_id_button.click()
                    logger.info("🔍 Clicked 'Get my visitor identifier' button")
                    await browser.wait(3000)
            except:
                logger.info(
                    "ℹ️ Button not found, fingerprinting may have run automatically"
                )

            # Extract fingerprint data
            fingerprint_data = await browser.page.evaluate("""
                async () => {
                    // Try to get FingerprintJS data
                    const fpPromise = import('https://openfpcdn.io/fingerprintjs/v4')
                      .then(FingerprintJS => FingerprintJS.load())
                      .then(fp => fp.get());
                    
                    try {
                        const result = await fpPromise;
                        return {
                            visitorId: result.visitorId,
                            confidence: result.confidence?.score || 'unknown',
                            components: Object.keys(result.components || {}),
                            timestamp: new Date().toISOString()
                        };
                    } catch (e) {
                        return {
                            error: e.message,
                            fallback: {
                                userAgent: navigator.userAgent,
                                webdriver: navigator.webdriver,
                                plugins: navigator.plugins.length,
                                languages: navigator.languages,
                                platform: navigator.platform,
                                hardwareConcurrency: navigator.hardwareConcurrency,
                                deviceMemory: navigator.deviceMemory || 'unknown',
                                timestamp: new Date().toISOString()
                            }
                        };
                    }
                }
            """)

            logger.info("🔍 Fingerprint Results:")
            logger.info(f"📊 Data: {json.dumps(fingerprint_data, indent=2)}")

            # Check specific stealth indicators
            stealth_checks = await browser.page.evaluate("""
                () => {
                    const checks = {
                        // Automation detection
                        webdriver: navigator.webdriver,
                        automation_controlled: window.chrome?.runtime?.onConnect === undefined,
                        
                        // Canvas fingerprinting
                        canvas_noise: (() => {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');
                            ctx.textBaseline = 'top';
                            ctx.font = '14px Arial';
                            ctx.fillText('BrowserLeaks.com', 2, 2);
                            return canvas.toDataURL().slice(-20);
                        })(),
                        
                        // WebGL fingerprinting
                        webgl_vendor: (() => {
                            const canvas = document.createElement('canvas');
                            const gl = canvas.getContext('webgl');
                            return gl ? gl.getParameter(gl.VENDOR) : 'none';
                        })(),
                        
                        webgl_renderer: (() => {
                            const canvas = document.createElement('canvas');
                            const gl = canvas.getContext('webgl');
                            return gl ? gl.getParameter(gl.RENDERER) : 'none';
                        })(),
                        
                        // Screen and viewport
                        screen_resolution: `${screen.width}x${screen.height}`,
                        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
                        
                        // Browser features
                        plugins_count: navigator.plugins.length,
                        languages: navigator.languages,
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                        
                        // Additional detection vectors
                        permissions_api: !!navigator.permissions,
                        notification_permission: Notification.permission,
                        cookie_enabled: navigator.cookieEnabled,
                        do_not_track: navigator.doNotTrack
                    };
                    return checks;
                }
            """)

            logger.info("🛡️ Stealth Check Results:")
            logger.info(f"📋 Details: {json.dumps(stealth_checks, indent=2)}")

            # Evaluate stealth score based on key indicators
            stealth_score = 0
            max_score = 10

            # Check webdriver (most important)
            if stealth_checks.get("webdriver") in [False, None]:
                stealth_score += 3
                logger.info("✅ WebDriver detection: PASSED")
            else:
                logger.warning("❌ WebDriver detection: FAILED")

            # Check plugins (natural number)
            plugins_count = stealth_checks.get("plugins_count", 0)
            if plugins_count >= 3:
                stealth_score += 2
                logger.info(f"✅ Plugins count ({plugins_count}): NATURAL")
            else:
                logger.warning(f"⚠️ Plugins count ({plugins_count}): SUSPICIOUS")

            # Check languages
            languages = stealth_checks.get("languages", [])
            if len(languages) >= 2:
                stealth_score += 1
                logger.info(f"✅ Languages ({len(languages)}): NATURAL")
            else:
                logger.warning(f"⚠️ Languages ({len(languages)}): SUSPICIOUS")

            # Check WebGL vendor
            webgl_vendor = stealth_checks.get("webgl_vendor", "")
            if webgl_vendor and "Google" not in webgl_vendor:
                stealth_score += 2
                logger.info(f"✅ WebGL vendor ({webgl_vendor}): GOOD")
            else:
                logger.info(f"ℹ️ WebGL vendor ({webgl_vendor}): STANDARD")
                stealth_score += 1

            # Check timezone
            timezone = stealth_checks.get("timezone", "")
            if "Los_Angeles" in timezone:
                stealth_score += 1
                logger.info(f"✅ Timezone ({timezone}): MATCHES CONFIG")
            else:
                logger.info(f"ℹ️ Timezone ({timezone}): DIFFERENT FROM CONFIG")

            # Check permissions API
            if stealth_checks.get("permissions_api"):
                stealth_score += 1
                logger.info("✅ Permissions API: AVAILABLE")
            else:
                logger.warning("⚠️ Permissions API: MISSING")

            logger.info(
                f"🏆 Stealth Score: {stealth_score}/{max_score} ({(stealth_score / max_score) * 100:.1f}%)"
            )

            if stealth_score >= 8:
                logger.info("🎉 EXCELLENT stealth capabilities!")
                return True
            elif stealth_score >= 6:
                logger.info("✅ GOOD stealth capabilities")
                return True
            elif stealth_score >= 4:
                logger.info("⚠️ MODERATE stealth capabilities")
                return True
            else:
                logger.warning("❌ POOR stealth capabilities")
                return False

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False


async def main():
    """Main function."""
    logger.info("🚀 Starting FingerprintJS stealth test")

    success = await test_fingerprintjs_stealth()

    if success:
        logger.info("🎉 Stealth test completed successfully!")
        sys.exit(0)
    else:
        logger.error("💥 Stealth test failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
