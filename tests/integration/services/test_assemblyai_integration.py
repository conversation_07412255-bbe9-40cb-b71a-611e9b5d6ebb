"""Integration tests for AssemblyAI service with mock browser."""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from tests.mocks.mock_browser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MockPage
from core.config import Config, ServiceConfig
from core.models import RegistrationResult
from services.assemblyai import AssemblyAIService


@pytest.mark.integration
class TestAssemblyAIServiceIntegration:
    """Integration tests for AssemblyAI service with mock browser."""

    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return Config.load()

    @pytest.fixture
    def service_config(self):
        """Create AssemblyAI service configuration."""
        return ServiceConfig(
            name="AssemblyAI",
            start_url="https://www.assemblyai.com/dashboard/login",
            email_domain_groups=["primary"],
            sender_domains=["assemblyai.com"],
            delay_times={"page_load": 3000, "stay_on_finish": 10000},
        )

    @pytest.fixture
    def mock_email_client(self):
        """Create mock email client."""
        mock_client = AsyncMock()
        mock_client.get_latest_email_content.return_value = None
        return mock_client

    @pytest.fixture
    def assemblyai_service(self, config, service_config, mock_email_client):
        """Create AssemblyAI service instance with mocked dependencies."""
        service = AssemblyAIService(service_config, config)
        service.email_client = mock_email_client
        return service

    @pytest.mark.asyncio
    async def test_service_initialization(self, assemblyai_service):
        """Test that AssemblyAI service initializes correctly."""
        assert assemblyai_service.config.name == "AssemblyAI"
        assert assemblyai_service.selected_domain is not None
        assert assemblyai_service.profile_generator is not None
        assert assemblyai_service.randomizer is not None

    @pytest.mark.asyncio
    async def test_magic_link_parsing_html(self, assemblyai_service):
        """Test magic link parsing from HTML email content."""
        html_email = """
        Content-Type: text/html; charset=utf-8
        
        <html>
        <body>
        <a href=3D"https://stytch.com/v1/magic_links/redirect?token=abc123&amp;public_token=public-token-live-12345">
        Click here to log in
        </a>
        </body>
        </html>
        """

        magic_link = AssemblyAIService.parse_magic_link(html_email)
        assert magic_link is not None
        assert "stytch.com/v1/magic_links/redirect" in magic_link
        assert "token=abc123" in magic_link

    @pytest.mark.asyncio
    async def test_magic_link_parsing_text(self, assemblyai_service):
        """Test magic link parsing from plain text email content."""
        text_email = """
        Subject: Your login link
        
        Click this link to log in:
        https://stytch.com/v1/magic_links/redirect?token=xyz789&public_token=public-token-live-67890
        
        Best regards,
        AssemblyAI Team
        """

        magic_link = AssemblyAIService.parse_magic_link(text_email)
        assert magic_link is not None
        assert "stytch.com/v1/magic_links/redirect" in magic_link

    @pytest.mark.asyncio
    async def test_magic_link_quoted_printable(self, assemblyai_service):
        """Test magic link parsing with quoted-printable encoding."""
        quoted_email = """
        href=3D"https://stytch.com/v1/magic_links/redirect?token=3Dabc123=
        &amp;public_token=3Dpublic-token-live=-
        12345"
        """

        magic_link = AssemblyAIService.parse_magic_link(quoted_email)
        assert magic_link is not None
        assert "stytch.com/v1/magic_links/redirect" in magic_link
        assert "public-token-live-12345" in magic_link

    @pytest.mark.asyncio
    async def test_magic_link_invalid_content(self, assemblyai_service):
        """Test magic link parsing with invalid content."""
        invalid_email = "This email does not contain a magic link"

        magic_link = AssemblyAIService.parse_magic_link(invalid_email)
        assert magic_link is None

    @pytest.mark.asyncio
    async def test_email_data_parsing(self, assemblyai_service):
        """Test email data parsing functionality."""
        email_data = {
            "raw": 'href=3D"https://stytch.com/v1/magic_links/redirect?token=test123"',
            "subject": "Login to AssemblyAI",
            "sender": "<EMAIL>",
        }

        parsed_data = AssemblyAIService.parse_email_data(email_data)
        assert "magicLink" in parsed_data
        assert "parsed_by" in parsed_data
        assert parsed_data["parsed_by"] == "assemblyai_service"
        assert parsed_data["parser_version"] == "1.0"

    @pytest.mark.asyncio
    async def test_error_message_detection(self, assemblyai_service):
        """Test error message detection functionality."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Test with error elements
        mock_browser.page.add_element(
            ".error", visible=True, text_content="Invalid email address"
        )
        mock_browser.page.add_element(
            '[role="alert"]', visible=True, text_content="Please try again"
        )

        error_msg = await assemblyai_service._check_for_error_messages(
            mock_browser.page
        )
        assert error_msg is not None
        assert "Invalid email address" in error_msg or "Please try again" in error_msg

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_human_form_interaction(self, assemblyai_service):
        """Test human-like form interaction simulation."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Test human simulation (should not raise errors)
        await assemblyai_service._simulate_human_form_interaction(mock_browser)

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_human_typing_behavior(self, assemblyai_service):
        """Test human-like typing behavior."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Create mock input element
        mock_element = mock_browser.page.add_element(
            'input[type="email"]', visible=True
        )

        # Test human typing
        test_text = "<EMAIL>"
        await assemblyai_service._type_like_human(mock_element, test_text)

        # Verify element received the text
        assert mock_element.value == test_text

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_api_key_extraction_strategies(self, assemblyai_service):
        """Test different API key extraction strategies."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Test with API key in page content (32-char hex)
        test_api_key = "a1b2c3d4e5f6789012345678901234ef"
        mock_browser.page.set_content(f"<div>{test_api_key}</div>")

        # Simulate the API key extraction process that would happen in _process_magic_link
        page_text = await mock_browser.page.text_content("body") or ""

        # Check if API key is detectable
        assert test_api_key in page_text
        assert len(test_api_key) == 32
        assert all(c in "0123456789abcdef" for c in test_api_key.lower())

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_browser_configuration(self, assemblyai_service):
        """Test browser configuration and settings."""
        # Test headless setting
        headless = assemblyai_service._get_headless_setting()
        assert headless is None or isinstance(headless, bool)

        # Test browser engines
        engines = assemblyai_service._get_browser_engines()
        assert isinstance(engines, list)

    @pytest.mark.asyncio
    async def test_onetrust_cookie_handling(self, assemblyai_service):
        """Test OneTrust cookie consent handling."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Add OneTrust consent button
        consent_button = mock_browser.page.add_element(
            "#onetrust-accept-btn-handler", visible=True
        )

        # This would be part of the main registration flow
        # Test that consent button can be found and clicked
        onetrust_button = await mock_browser.page.query_selector(
            "#onetrust-accept-btn-handler"
        )
        assert onetrust_button is not None
        assert await onetrust_button.is_visible()

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_email_input_interaction(self, assemblyai_service):
        """Test email input field interaction."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Add email input with test-id
        email_input = mock_browser.page.add_element(
            '[data-testid="email"]', visible=True
        )

        # Test input selection and interaction
        input_element = mock_browser.page.get_by_test_id("email")
        assert input_element is not None

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_submit_button_interaction(self, assemblyai_service):
        """Test submit button interaction."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Add submit button with test-id
        submit_button = mock_browser.page.add_element(
            '[data-testid="continue-with-email"]', visible=True
        )

        # Test button selection and interaction
        button_element = mock_browser.page.get_by_test_id("continue-with-email")
        assert button_element is not None

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_local_email_parsing_fallback(self, assemblyai_service):
        """Test local email parsing fallback functionality."""
        # Test the local parsing method directly
        test_email_content = """
        href=3D"https://stytch.com/v1/magic_links/redirect?token=localtest123&amp;public_token=local-token"
        """

        magic_link = assemblyai_service._parse_magic_link_locally(test_email_content)
        assert magic_link is not None
        assert "stytch.com/v1/magic_links/redirect" in magic_link
        assert "token=localtest123" in magic_link

    @pytest.mark.asyncio
    async def test_profile_integration(self, assemblyai_service):
        """Test profile generation and integration."""
        profile = assemblyai_service.profile_generator.generate_profile()

        assert "email" in profile
        assert "username" in profile
        assert "first_name" in profile
        assert "last_name" in profile
        assert profile["email"].endswith(assemblyai_service.selected_domain.domain)

    @pytest.mark.asyncio
    async def test_randomizer_integration(self, assemblyai_service):
        """Test randomizer integration and functionality."""
        randomizer = assemblyai_service.randomizer

        # Test delay generation
        delay = randomizer.uniform(1.0, 3.0)
        assert 1.0 <= delay <= 3.0

        # Test random integers
        rand_int = randomizer.randint(1, 10)
        assert 1 <= rand_int <= 10

        # Test Gaussian distribution
        gauss_val = randomizer.gauss(0.12, 0.04)
        assert isinstance(gauss_val, float)

    @pytest.mark.asyncio
    async def test_copy_button_interaction(self, assemblyai_service):
        """Test copy button finding and interaction."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Add various copy button types
        mock_browser.page.add_element('button:has-text("Copy")', visible=True)
        mock_browser.page.add_element('[role="button"]:has-text("Copy")', visible=True)
        mock_browser.page.add_element('button[aria-label*="Copy"]', visible=True)

        # Test copy button detection
        copy_button = await mock_browser.page.query_selector('button:has-text("Copy")')
        assert copy_button is not None
        assert await copy_button.is_visible()

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_continue_button_interaction(self, assemblyai_service):
        """Test continue/dashboard button interaction."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Add various continue button types
        mock_browser.page.add_element(
            'a:has-text("Skip and go to my dashboard")', visible=True
        )
        mock_browser.page.add_element(
            'button:has-text("Continue to dashboard")', visible=True
        )
        mock_browser.page.add_element('a[href*="dashboard"]', visible=True)

        # Test continue button detection
        continue_button = await mock_browser.page.query_selector(
            'a:has-text("Skip and go to my dashboard")'
        )
        assert continue_button is not None
        assert await continue_button.is_visible()

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_error_handling_scenarios(self, assemblyai_service):
        """Test various error handling scenarios."""
        # Test empty email content
        assert AssemblyAIService.parse_magic_link("") is None
        assert AssemblyAIService.parse_magic_link(None) is None

        # Test malformed email data
        malformed_data = {"subject": "test"}  # Missing 'raw' field
        parsed = AssemblyAIService.parse_email_data(malformed_data)
        assert "magicLink" not in parsed  # Should not have magic link

    @pytest.mark.asyncio
    async def test_static_method_independence(self):
        """Test that static methods work independently of service instance."""
        # Test magic link parsing without service instance
        test_content = (
            'href=3D"https://stytch.com/v1/magic_links/redirect?token=static123"'
        )
        magic_link = AssemblyAIService.parse_magic_link(test_content)
        assert magic_link is not None
        assert "token=static123" in magic_link

        # Test email data parsing without service instance
        email_data = {"raw": test_content, "subject": "Test"}
        parsed_data = AssemblyAIService.parse_email_data(email_data)
        assert "magicLink" in parsed_data
