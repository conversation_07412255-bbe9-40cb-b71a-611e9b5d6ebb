"""Integration tests for Firecrawl service with mock browser."""

import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from tests.mocks.mock_browser import <PERSON><PERSON><PERSON><PERSON>er<PERSON>ana<PERSON>, MockPage
from core.config import Config, ServiceConfig
from core.models import RegistrationResult
from services.firecrawl import FirecrawlService


@pytest.mark.integration
class TestFirecrawlServiceIntegration:
    """Integration tests for Firecrawl service with mock browser."""

    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return Config.load()

    @pytest.fixture
    def service_config(self):
        """Create Firecrawl service configuration."""
        return ServiceConfig(
            name="Firecrawl",
            start_url="https://www.firecrawl.dev",
            email_domain_groups=["primary"],
            sender_domains=["firecrawl.dev"],
            delay_times={"page_load": 3000, "stay_on_finish": 10000},
        )

    @pytest.fixture
    def mock_email_client(self):
        """Create mock email client."""
        mock_client = AsyncMock()
        mock_client.get_latest_email_content.return_value = None
        return mock_client

    @pytest.fixture
    def firecrawl_service(self, config, service_config, mock_email_client):
        """Create Firecrawl service instance with mocked dependencies."""
        service = FirecrawlService(service_config, config)
        service.email_client = mock_email_client
        return service

    @pytest.mark.asyncio
    async def test_service_initialization(self, firecrawl_service):
        """Test that Firecrawl service initializes correctly."""
        assert firecrawl_service.service_config.name == "Firecrawl"
        assert firecrawl_service.selected_domain is not None
        assert firecrawl_service.profile_generator is not None
        assert firecrawl_service.randomizer is not None

    @pytest.mark.asyncio
    async def test_verification_link_parsing(self, firecrawl_service):
        """Test verification link parsing from email content."""
        # Test valid verification email
        valid_email = """
        Content-Type: text/html; charset=utf-8
        
        <html>
        <body>
        <a href=3D"https://firecrawl.dev/auth/v1/verify?token=abc123&amp;type=signup">
        Verify your account
        </a>
        </body>
        </html>
        """

        verification_link = FirecrawlService.parse_verification_link(valid_email)
        assert verification_link is not None
        assert "firecrawl.dev/auth/v1/verify" in verification_link
        assert "token=abc123" in verification_link

    @pytest.mark.asyncio
    async def test_verification_link_quoted_printable(self, firecrawl_service):
        """Test verification link parsing with quoted-printable encoding."""
        quoted_printable_email = """
        href=3D"https://firecrawl.dev/auth/v1/verify?token=3Dabc123=
        &amp;type=3Dsignup"
        """

        verification_link = FirecrawlService.parse_verification_link(
            quoted_printable_email
        )
        assert verification_link is not None
        assert "firecrawl.dev/auth/v1/verify" in verification_link

    @pytest.mark.asyncio
    async def test_verification_link_invalid_content(self, firecrawl_service):
        """Test verification link parsing with invalid content."""
        invalid_email = "This is not a valid verification email"

        verification_link = FirecrawlService.parse_verification_link(invalid_email)
        assert verification_link is None

    @pytest.mark.asyncio
    async def test_api_key_validation(self, firecrawl_service):
        """Test API key validation logic."""
        # Test valid Firecrawl API key
        valid_key = "fc-12345678901234567890123456789012"
        assert firecrawl_service._is_valid_firecrawl_api_key(valid_key)

        # Test invalid API keys
        invalid_keys = [
            "sk-1234567890",  # Wrong prefix
            "fc-123",  # Too short
            "",  # Empty
            "random-string",  # Wrong format
            "fc-12345678901234567890123456789012345",  # Contains invalid chars
        ]

        for invalid_key in invalid_keys:
            assert not firecrawl_service._is_valid_firecrawl_api_key(invalid_key)

    @pytest.mark.asyncio
    async def test_browser_configuration(self, firecrawl_service):
        """Test browser configuration and engine selection."""
        # Test headless setting
        headless = firecrawl_service._get_headless_setting()
        assert headless is None or isinstance(headless, bool)

        # Test browser engines
        engines = firecrawl_service._get_browser_engines()
        assert isinstance(engines, list)

    @pytest.mark.asyncio
    async def test_debug_screenshot_functionality(self, firecrawl_service):
        """Test debug screenshot functionality."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Test debug mode disabled (default)
        firecrawl_service.debug_mode = False
        await firecrawl_service._debug_screenshot(mock_browser, "test")
        # Should not raise error when debug disabled

        # Test debug mode enabled
        firecrawl_service.debug_mode = True
        with patch("pathlib.Path.mkdir"), patch.object(mock_browser.page, "screenshot"):
            await firecrawl_service._debug_screenshot(mock_browser, "test_debug")

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_wizard_completion_logic(self, firecrawl_service):
        """Test setup wizard completion logic."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Mock wizard elements
        mock_browser.page.add_element('text="Step 1 of 4"', visible=True)
        mock_browser.page.add_element('button:has-text("Next")', visible=True)
        mock_browser.page.add_element('button[role="radio"]', visible=True, count=3)

        # Test wizard detection and completion
        result = await firecrawl_service._complete_wizard(mock_browser)
        assert result is True

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_checkbox_strategies(self, firecrawl_service):
        """Test different checkbox checking strategies."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Test with various checkbox types
        mock_browser.page.add_element('[role="checkbox"]', visible=True)
        mock_browser.page.add_element('input[type="checkbox"]', visible=True)
        mock_browser.page.add_element('label:has-text("Terms")', visible=True)

        # Test checkbox finding and clicking strategies
        result = await firecrawl_service._check_terms_checkbox_enhanced(mock_browser)
        assert isinstance(result, bool)

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_api_key_extraction_methods(self, firecrawl_service):
        """Test different API key extraction methods."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Test page content method
        test_api_key = "fc-12345678901234567890123456789012"
        mock_browser.page.set_content(f"<div>Your API key: {test_api_key}</div>")

        api_key = await firecrawl_service._extract_api_key_from_page_content(
            mock_browser
        )
        assert api_key == test_api_key

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_clipboard_functionality(self, firecrawl_service):
        """Test clipboard reading functionality."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Test clipboard reading
        test_content = "fc-12345678901234567890123456789012"
        mock_browser.page.set_clipboard_content(test_content)

        clipboard_content = await firecrawl_service._read_clipboard_content(
            mock_browser
        )
        assert clipboard_content == test_content

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_dashboard_verification(self, firecrawl_service):
        """Test dashboard access verification."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Test successful dashboard access
        mock_browser.page.set_url("https://www.firecrawl.dev/app")

        result = await firecrawl_service._verify_dashboard_access(mock_browser)
        assert result is True

        # Test non-dashboard URL
        mock_browser.page.set_url("https://www.example.com")
        result = await firecrawl_service._verify_dashboard_access(mock_browser)
        assert result is True  # Should still return True (optimistic approach)

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_profile_generation_integration(self, firecrawl_service):
        """Test profile generation with domain integration."""
        profile = firecrawl_service.profile_generator.generate_profile()

        assert "email" in profile
        assert "password" not in profile  # Password generated separately
        assert profile["email"].endswith(firecrawl_service.selected_domain.domain)

    @pytest.mark.asyncio
    async def test_email_polling_logic(self, firecrawl_service, mock_email_client):
        """Test email polling and content retrieval."""
        test_email = "<EMAIL>"
        test_content = 'href=3D"https://firecrawl.dev/auth/v1/verify?token=test123"'

        # Setup mock to return email content
        mock_email_client.get_latest_email_content.return_value = test_content

        # Test email content retrieval
        content = await mock_email_client.get_latest_email_content(
            "firecrawl", test_email
        )
        assert content == test_content

        # Test verification link parsing from retrieved content
        verification_link = FirecrawlService.parse_verification_link(content)
        assert verification_link is not None

    @pytest.mark.asyncio
    async def test_error_handling_scenarios(self, firecrawl_service):
        """Test various error handling scenarios."""
        mock_browser = MockBrowserManager(["chromium"])
        await mock_browser.start()

        # Test API key extraction failure
        mock_browser.page.set_content("<div>No API key here</div>")

        api_key = await firecrawl_service._extract_api_key_from_page_content(
            mock_browser
        )
        assert api_key is None

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_randomizer_integration(self, firecrawl_service):
        """Test randomizer integration and timing variations."""
        randomizer = firecrawl_service.randomizer

        # Test delay generation
        delay = randomizer.uniform(1.0, 3.0)
        assert 1.0 <= delay <= 3.0

        # Test random choice
        choices = ["option1", "option2", "option3"]
        choice = randomizer.choice(choices)
        assert choice in choices

    @pytest.mark.asyncio
    async def test_service_config_inheritance(self, firecrawl_service):
        """Test service configuration inheritance from global config."""
        # Test delay times
        delay_times = firecrawl_service.service_config.delay_times
        assert isinstance(delay_times, dict)

        # Test sender domains
        sender_domains = firecrawl_service.service_config.sender_domains
        assert isinstance(sender_domains, list)
