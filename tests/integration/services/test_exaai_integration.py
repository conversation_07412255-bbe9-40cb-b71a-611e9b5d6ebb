"""Integration tests for ExaAI service with mock browser."""

import pytest
from unittest.mock import AsyncMock, MagicMock
from tests.mocks.mock_browser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MockPage
from core.config import Config, ServiceConfig
from core.models import RegistrationResult
from services.exaai import ExaAIService


@pytest.mark.integration
class TestExaAIServiceIntegration:
    """Integration tests for ExaAI service with mock browser."""

    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return Config.load()

    @pytest.fixture
    def service_config(self):
        """Create ExaAI service configuration."""
        return ServiceConfig(
            name="ExaAI",
            start_url="https://exa.ai/signup",
            email_domain_groups=["primary"],
            sender_domains=["exa.ai"],
            delay_times={"page_load": 3000, "stay_on_finish": 10000},
        )

    @pytest.fixture
    def mock_email_client(self):
        """Create mock email client."""
        mock_client = AsyncMock()
        mock_client.get_latest_email_content.return_value = None
        return mock_client

    @pytest.fixture
    def exaai_service(self, config, service_config, mock_email_client):
        """Create ExaAI service instance with mocked dependencies."""
        service = ExaAIService(service_config, config)
        service.email_client = mock_email_client
        return service

    @pytest.mark.asyncio
    async def test_service_initialization(self, exaai_service):
        """Test that ExaAI service initializes correctly."""
        assert exaai_service.service_config.name == "ExaAI"
        assert exaai_service.selected_domain is not None
        assert exaai_service.profile_generator is not None
        assert exaai_service.randomizer is not None

    @pytest.mark.asyncio
    async def test_browser_manager_creation(self, exaai_service):
        """Test browser manager creation with service settings."""
        # Mock browser engines
        exaai_service.full_config.browser_engines = ["chromium"]

        browser_engines = exaai_service._get_browser_engines()
        headless_setting = exaai_service._get_headless_setting()

        assert browser_engines == ["chromium"]
        assert headless_setting is None  # Should use default

    @pytest.mark.asyncio
    async def test_email_parsing_functionality(self, exaai_service):
        """Test email parsing with various email formats."""
        # Test valid magic link email
        valid_email = """
        Content-Type: text/html; charset=utf-8
        
        <html>
        <body>
        <a href="https://stytch.com/v1/magic_links/redirect?token=abc123&amp;public_token=public-token-live-12345">
        Click here to verify
        </a>
        </body>
        </html>
        """

        magic_link = ExaAIService.parse_magic_link(valid_email)
        assert magic_link is not None
        assert "stytch.com/v1/magic_links/redirect" in magic_link
        assert "token=abc123" in magic_link

    @pytest.mark.asyncio
    async def test_email_parsing_with_quoted_printable(self, exaai_service):
        """Test email parsing with quoted-printable encoding."""
        quoted_printable_email = """
        href=3D"https://stytch.com/v1/magic_links/redirect?token=3Dabc123=
        &amp;public_token=3Dpublic-token-live-12345"
        """

        magic_link = ExaAIService.parse_magic_link(quoted_printable_email)
        assert magic_link is not None
        assert "=" in magic_link  # Should have decoded =3D to =

    @pytest.mark.asyncio
    async def test_email_parsing_invalid_content(self, exaai_service):
        """Test email parsing with invalid content."""
        invalid_email = "This is not a valid email with magic link"

        magic_link = ExaAIService.parse_magic_link(invalid_email)
        assert magic_link is None

    @pytest.mark.asyncio
    async def test_profile_generation_consistency(self, exaai_service):
        """Test that profile generation works consistently."""
        profile1 = exaai_service.profile_generator.generate_profile()
        profile2 = exaai_service.profile_generator.generate_profile()

        # Should have consistent structure
        for profile in [profile1, profile2]:
            assert "email" in profile
            assert "username" in profile
            assert "first_name" in profile
            assert "last_name" in profile
            assert profile["email"].endswith(exaai_service.selected_domain.domain)

    @pytest.mark.asyncio
    async def test_domain_selection_logic(self, exaai_service):
        """Test domain selection from configured groups."""
        domains = exaai_service.full_config.get_domains_for_service("exaai")
        assert len(domains) > 0
        assert exaai_service.selected_domain in domains

    @pytest.mark.asyncio
    async def test_randomizer_integration(self, exaai_service):
        """Test randomizer integration and functionality."""
        randomizer = exaai_service.randomizer
        assert randomizer is not None

        # Test basic randomization functions
        delay_time = randomizer.uniform(1.0, 2.0)
        assert 1.0 <= delay_time <= 2.0

        random_int = randomizer.randint(1, 10)
        assert 1 <= random_int <= 10

    @pytest.mark.asyncio
    async def test_error_handling_no_email(self, exaai_service, mock_email_client):
        """Test error handling when no email is received."""
        # Setup mock to return no email
        mock_email_client.get_latest_email_content.return_value = None

        # This would normally timeout waiting for email
        # We can test the parsing logic directly
        result = ExaAIService.parse_magic_link("")
        assert result is None

    @pytest.mark.asyncio
    async def test_service_config_validation(self, config):
        """Test service configuration validation."""
        # Test invalid service config
        invalid_config = ServiceConfig(
            name="", start_url="invalid-url", email_domain_groups=[], sender_domains=[]
        )

        # Should handle gracefully or raise appropriate error
        with pytest.raises((ValueError, Exception)):
            ExaAIService(invalid_config, config)

    @pytest.mark.asyncio
    async def test_mock_browser_integration(self, exaai_service):
        """Test integration with mock browser manager."""
        mock_browser = MockBrowserManager(["chromium"])

        # Test browser engine selection
        assert mock_browser.browser_engine in ["chromium"]

        # Test page creation
        await mock_browser.start()
        assert mock_browser.page is not None
        assert isinstance(mock_browser.page, MockPage)

        await mock_browser.stop()

    @pytest.mark.asyncio
    async def test_static_method_functionality(self):
        """Test static methods work independently."""
        # Test email data parsing
        email_data = {
            "raw": 'href=3D"https://stytch.com/v1/magic_links/redirect?token=test123"',
            "subject": "Test email",
        }

        parsed_data = ExaAIService.parse_email_data(email_data)
        assert "magicLink" in parsed_data
        assert "parsed_by" in parsed_data
        assert parsed_data["parsed_by"] == "exaai_service"

    @pytest.mark.asyncio
    async def test_configuration_inheritance(self, exaai_service):
        """Test that service inherits global configuration properly."""
        # Test that service gets browser engines from global config
        global_engines = exaai_service.full_config.browser_engines
        service_engines = exaai_service._get_browser_engines()

        # Should inherit from global config
        assert isinstance(service_engines, list)

    @pytest.mark.asyncio
    async def test_email_client_factory_integration(self, exaai_service):
        """Test email client factory integration."""
        email_client = exaai_service.email_client
        assert email_client is not None
        assert hasattr(email_client, "get_latest_email_content")
