"""Integration tests for email client factory and webhook interactions."""

import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import aiohttp
from core.email_client_factory import EmailClientFactory
from core.webhook_client import Webhook<PERSON>lient
from core.config import Config


@pytest.mark.integration
class TestEmailClientIntegration:
    """Integration tests for email client factory and webhook interactions."""

    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return Config.load()

    @pytest.fixture
    def mock_webhook_client(self):
        """Create mock webhook client."""
        mock_client = AsyncMock(spec=WebhookClient)
        mock_client.get_latest_email_content.return_value = None
        return mock_client

    @pytest.mark.asyncio
    async def test_email_client_factory_creation(self, config):
        """Test email client factory creates appropriate client."""
        with patch.object(EmailClientFactory, "create_client") as mock_create:
            mock_client = AsyncMock()
            mock_create.return_value = mock_client

            client = EmailClientFactory.create_client()
            assert client is not None
            mock_create.assert_called_once()

    @pytest.mark.asyncio
    async def test_webhook_client_initialization(self, config):
        """Test webhook client initialization."""
        webhook_url = "http://localhost:8888"

        with patch("core.webhook_client.WebhookClient") as mock_webhook:
            mock_instance = AsyncMock()
            mock_webhook.return_value = mock_instance

            client = WebhookClient(webhook_url)
            assert client is not None

    @pytest.mark.asyncio
    async def test_email_content_retrieval_mock(self, mock_webhook_client):
        """Test email content retrieval with mock client."""
        service = "assemblyai"
        email = "<EMAIL>"
        expected_content = "Mock email content with magic link"

        # Setup mock response
        mock_webhook_client.get_latest_email_content.return_value = expected_content

        # Test retrieval
        content = await mock_webhook_client.get_latest_email_content(service, email)
        assert content == expected_content
        mock_webhook_client.get_latest_email_content.assert_called_once_with(
            service, email
        )

    @pytest.mark.asyncio
    async def test_email_content_retrieval_timeout(self, mock_webhook_client):
        """Test email content retrieval timeout behavior."""
        service = "firecrawl"
        email = "<EMAIL>"

        # Setup mock to return None (no email found)
        mock_webhook_client.get_latest_email_content.return_value = None

        # Test timeout behavior
        content = await mock_webhook_client.get_latest_email_content(service, email)
        assert content is None

    @pytest.mark.asyncio
    async def test_webhook_client_error_handling(self):
        """Test webhook client error handling."""
        webhook_url = "http://invalid-url:99999"

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_get.side_effect = aiohttp.ClientError("Connection failed")

            client = WebhookClient(webhook_url)

            # Should handle connection errors gracefully
            content = await client.get_latest_email_content(
                "test_service", "<EMAIL>"
            )
            assert content is None or content == ""

    @pytest.mark.asyncio
    async def test_multiple_service_email_retrieval(self, mock_webhook_client):
        """Test email retrieval for multiple services."""
        services = ["assemblyai", "firecrawl", "exaai"]
        email = "<EMAIL>"

        # Setup different responses for different services
        async def mock_get_content(service, email_addr):
            return f"Content for {service} service"

        mock_webhook_client.get_latest_email_content.side_effect = mock_get_content

        # Test retrieval for each service
        for service in services:
            content = await mock_webhook_client.get_latest_email_content(service, email)
            assert content == f"Content for {service} service"

    @pytest.mark.asyncio
    async def test_email_client_service_specific_behavior(self, mock_webhook_client):
        """Test service-specific email client behavior."""
        # Test AssemblyAI magic link handling
        assemblyai_content = (
            'href=3D"https://stytch.com/v1/magic_links/redirect?token=test123"'
        )
        mock_webhook_client.get_latest_email_content.return_value = assemblyai_content

        content = await mock_webhook_client.get_latest_email_content(
            "assemblyai", "<EMAIL>"
        )
        assert "stytch.com" in content

        # Test Firecrawl verification link handling
        firecrawl_content = (
            'href=3D"https://firecrawl.dev/auth/v1/verify?token=verify123"'
        )
        mock_webhook_client.get_latest_email_content.return_value = firecrawl_content

        content = await mock_webhook_client.get_latest_email_content(
            "firecrawl", "<EMAIL>"
        )
        assert "firecrawl.dev" in content

    @pytest.mark.asyncio
    async def test_concurrent_email_requests(self, mock_webhook_client):
        """Test concurrent email requests handling."""
        import asyncio

        services = ["assemblyai", "firecrawl", "exaai"]
        email = "<EMAIL>"

        async def mock_delayed_response(service, email_addr):
            await asyncio.sleep(0.1)  # Simulate network delay
            return f"Response for {service}"

        mock_webhook_client.get_latest_email_content.side_effect = mock_delayed_response

        # Create concurrent requests
        tasks = [
            mock_webhook_client.get_latest_email_content(service, email)
            for service in services
        ]

        # Execute concurrently
        results = await asyncio.gather(*tasks)

        # Verify all requests completed
        assert len(results) == len(services)
        for i, service in enumerate(services):
            assert results[i] == f"Response for {service}"

    @pytest.mark.asyncio
    async def test_email_content_encoding_handling(self, mock_webhook_client):
        """Test email content encoding handling."""
        # Test quoted-printable encoding
        quoted_content = 'href=3D\\"https://example.com/verify?token=3Dtest123\\" '
        mock_webhook_client.get_latest_email_content.return_value = quoted_content

        content = await mock_webhook_client.get_latest_email_content(
            "test", "<EMAIL>"
        )
        assert "3D" in content  # Should preserve encoding for service parsing

    @pytest.mark.asyncio
    async def test_email_client_configuration_variants(self, config):
        """Test email client with different configuration variants."""
        # Test with webhook configuration
        with patch.object(config, "email_retrieval", {"client_type": "webhook"}):
            with patch.object(EmailClientFactory, "create_client") as mock_create:
                mock_client = AsyncMock()
                mock_create.return_value = mock_client

                client = EmailClientFactory.create_client()
                assert client is not None

    @pytest.mark.asyncio
    async def test_webhook_url_validation(self):
        """Test webhook URL validation and handling."""
        valid_urls = [
            "http://localhost:8888",
            "https://webhook.example.com",
            "http://127.0.0.1:3000",
        ]

        for url in valid_urls:
            client = WebhookClient(url)
            assert client.webhook_url == url

    @pytest.mark.asyncio
    async def test_email_polling_simulation(self, mock_webhook_client):
        """Test email polling simulation for services."""
        service = "assemblyai"
        email = "<EMAIL>"

        # Simulate polling behavior - first few calls return None, then content
        call_count = 0

        async def mock_polling_response(service_name, email_addr):
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                return None  # No email yet
            return "Magic link content found"

        mock_webhook_client.get_latest_email_content.side_effect = mock_polling_response

        # Simulate polling loop
        content = None
        for attempt in range(5):
            content = await mock_webhook_client.get_latest_email_content(service, email)
            if content:
                break

        assert content == "Magic link content found"
        assert call_count >= 3

    @pytest.mark.asyncio
    async def test_email_client_cleanup(self, mock_webhook_client):
        """Test email client cleanup and resource management."""
        # Test that client can be used multiple times
        for i in range(3):
            content = await mock_webhook_client.get_latest_email_content(
                "test", f"test{i}@example.com"
            )
            # Should not raise errors on repeated usage

    @pytest.mark.asyncio
    async def test_service_email_filtering(self, mock_webhook_client):
        """Test service-specific email filtering."""
        # Test that emails are filtered by service
        service_emails = {
            "assemblyai": "assemblyai magic link content",
            "firecrawl": "firecrawl verification content",
            "exaai": "exaai magic link content",
        }

        async def mock_service_filter(service, email):
            return service_emails.get(service, None)

        mock_webhook_client.get_latest_email_content.side_effect = mock_service_filter

        # Test each service gets appropriate content
        for service, expected_content in service_emails.items():
            content = await mock_webhook_client.get_latest_email_content(
                service, "<EMAIL>"
            )
            assert content == expected_content

    @pytest.mark.asyncio
    async def test_email_content_validation(self, mock_webhook_client):
        """Test email content validation and sanitization."""
        # Test various email content formats
        test_contents = [
            "Valid email content",
            "",  # Empty content
            None,  # None content
            "Content with special chars: <>&\"'",
            "Very long content " + "x" * 10000,  # Large content
        ]

        for content in test_contents:
            mock_webhook_client.get_latest_email_content.return_value = content
            result = await mock_webhook_client.get_latest_email_content(
                "test", "<EMAIL>"
            )
            # Should handle all content types gracefully
            assert result == content or result is None

    @pytest.mark.asyncio
    async def test_integration_with_config_loading(self):
        """Test integration with configuration loading."""
        # Test that email client respects configuration
        config = Config.load()

        # Verify configuration has email-related settings
        assert hasattr(config, "webhook") or hasattr(config, "email_retrieval")

        # Test webhook URL extraction
        webhook_url = config.webhook_url
        assert isinstance(webhook_url, str)
        assert webhook_url.startswith(("http://", "https://"))

    @pytest.mark.asyncio
    async def test_error_recovery_mechanisms(self, mock_webhook_client):
        """Test error recovery mechanisms in email retrieval."""
        service = "test_service"
        email = "<EMAIL>"

        # Simulate intermittent failures followed by success
        call_count = 0

        async def mock_error_recovery(service_name, email_addr):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise Exception("Temporary failure")
            return "Recovered content"

        mock_webhook_client.get_latest_email_content.side_effect = mock_error_recovery

        # Test that service can recover from failures
        content = None
        for attempt in range(4):
            try:
                content = await mock_webhook_client.get_latest_email_content(
                    service, email
                )
                if content:
                    break
            except Exception:
                continue  # Retry on failure

        assert content == "Recovered content"
