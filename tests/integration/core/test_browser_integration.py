"""Integration tests for BrowserManager with mock implementations."""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from tests.mocks.mock_browser import MockBrowserManager
from core.browser import <PERSON>rowser<PERSON>anager
from core.randomizer import Randomizer, create_randomizer, RandomizationProfile


@pytest.mark.integration
class TestBrowserManagerIntegration:
    """Integration tests for BrowserManager with mocks."""

    @pytest.fixture
    def randomizer(self):
        """Create test randomizer."""
        return create_randomizer(RandomizationProfile.NORMAL, enabled=True)

    @pytest.fixture
    def mock_browser_manager(self, randomizer):
        """Create mock browser manager."""
        return MockBrowserManager(
            browser_engines=["chromium", "firefox"], randomizer=randomizer
        )

    @pytest.mark.asyncio
    async def test_browser_engine_selection(self, mock_browser_manager):
        """Test browser engine selection logic."""
        await mock_browser_manager.start()

        # Should select one of the configured engines
        assert mock_browser_manager.browser_engine in ["chromium", "firefox"]

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_browser_engine_fallback(self):
        """Test browser engine fallback when engines fail."""
        # Create manager with invalid engines to test fallback
        with patch("playwright.async_api.async_playwright") as mock_playwright:
            mock_playwright.return_value.start.return_value.chromium.launch.side_effect = Exception(
                "Engine failed"
            )

            browser_manager = BrowserManager(browser_engines=["chromium"])

            with pytest.raises(RuntimeError, match="All browser engines failed"):
                await browser_manager.start()

    @pytest.mark.asyncio
    async def test_context_creation_with_options(self, mock_browser_manager):
        """Test browser context creation with various options."""
        await mock_browser_manager.start()

        assert mock_browser_manager.context is not None
        assert mock_browser_manager.page is not None

        # Test context options were applied
        context_options = mock_browser_manager._get_context_options()
        assert "viewport" in context_options
        assert "user_agent" in context_options
        assert "permissions" in context_options

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_randomization_features(self, mock_browser_manager):
        """Test randomization features integration."""
        await mock_browser_manager.start()

        # Test viewport randomization
        viewport1 = mock_browser_manager._get_random_viewport()
        viewport2 = mock_browser_manager._get_random_viewport()

        assert "width" in viewport1 and "height" in viewport1
        assert "width" in viewport2 and "height" in viewport2

        # Test user agent randomization
        ua1 = mock_browser_manager._get_random_user_agent()
        ua2 = mock_browser_manager._get_random_user_agent()

        assert len(ua1) > 0
        assert len(ua2) > 0

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_page_navigation(self, mock_browser_manager):
        """Test page navigation functionality."""
        await mock_browser_manager.start()

        test_url = "https://example.com"
        await mock_browser_manager.goto(test_url)

        assert mock_browser_manager.page.url == test_url

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_element_interaction(self, mock_browser_manager):
        """Test element interaction methods."""
        await mock_browser_manager.start()

        # Add test elements
        mock_browser_manager.page.add_element('input[type="text"]', visible=True)
        mock_browser_manager.page.add_element("button#submit", visible=True)

        # Test fill method
        await mock_browser_manager.fill(
            'input[type="text"]', "test input", human_like=False
        )

        # Test click method
        await mock_browser_manager.click("button#submit", human_like=False)

        # Test text retrieval
        text = await mock_browser_manager.get_text("button#submit")
        assert isinstance(text, str)

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_wait_functionality(self, mock_browser_manager):
        """Test various wait functionality."""
        await mock_browser_manager.start()

        # Add element to wait for
        mock_browser_manager.page.add_element("#dynamic-element", visible=True)

        # Test wait for selector
        element = await mock_browser_manager.wait_for_selector("#dynamic-element")
        assert element is not None

        # Test timing waits
        await mock_browser_manager.wait(100)  # 100ms
        await mock_browser_manager.random_short_delay()

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_screenshot_functionality(self, mock_browser_manager):
        """Test screenshot functionality."""
        await mock_browser_manager.start()

        with patch.object(mock_browser_manager.page, "screenshot") as mock_screenshot:
            await mock_browser_manager.screenshot("test.png")
            mock_screenshot.assert_called_once_with(path="test.png")

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_delay_methods(self, mock_browser_manager):
        """Test various delay methods."""
        await mock_browser_manager.start()

        # Test different delay types
        await mock_browser_manager.random_delay(0.1, 0.2)
        await mock_browser_manager.random_short_delay()
        await mock_browser_manager.random_medium_delay()
        await mock_browser_manager.random_user_action_delay()

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_scrolling_behavior(self, mock_browser_manager):
        """Test scrolling behavior simulation."""
        await mock_browser_manager.start()

        # Test random scroll (should not raise errors)
        await mock_browser_manager.random_scroll()

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_browser_engine_configuration_parsing(self):
        """Test browser engine configuration parsing."""
        # Test simple string engines
        browser_manager = BrowserManager(browser_engines=["chromium", "firefox"])
        parsed = browser_manager._parse_browser_engines(["chromium", "firefox"])

        assert len(parsed) == 2
        assert parsed[0]["name"] == "chromium"
        assert parsed[1]["name"] == "firefox"

        # Test complex engine configurations
        complex_engines = [
            {"bit": {"browser_id": "test", "api_url": "http://localhost:54345"}},
            "chromium",
        ]
        parsed_complex = browser_manager._parse_browser_engines(complex_engines)

        assert len(parsed_complex) == 2
        assert parsed_complex[0]["name"] == "bit"
        assert "options" in parsed_complex[0]

    @pytest.mark.asyncio
    async def test_headless_mode_configuration(self, randomizer):
        """Test headless mode configuration."""
        # Test with headless override
        browser_manager = MockBrowserManager(
            browser_engines=["chromium"],
            service_headless_override=False,
            randomizer=randomizer,
        )

        await browser_manager.start()
        assert browser_manager.service_headless_override is False
        await browser_manager.stop()

    @pytest.mark.asyncio
    async def test_global_engine_configs(self, randomizer):
        """Test global engine configurations."""
        global_configs = {
            "chromium": {"headless": True, "custom_option": "value"},
            "firefox": {"headless": False},
        }

        browser_manager = MockBrowserManager(
            browser_engines=["chromium"],
            global_engine_configs=global_configs,
            randomizer=randomizer,
        )

        await browser_manager.start()
        assert browser_manager.global_engine_configs == global_configs
        await browser_manager.stop()

    @pytest.mark.asyncio
    async def test_permissions_handling(self, mock_browser_manager):
        """Test permissions handling for different browser engines."""
        await mock_browser_manager.start()

        context_options = mock_browser_manager._get_context_options()

        # Should have permissions based on browser engine
        if mock_browser_manager.browser_engine in ["chromium", "bit", "fpchrome"]:
            assert "clipboard-read" in context_options["permissions"]
            assert "clipboard-write" in context_options["permissions"]
        elif mock_browser_manager.browser_engine == "webkit":
            assert "clipboard-read" in context_options["permissions"]
        elif mock_browser_manager.browser_engine == "firefox":
            assert len(context_options["permissions"]) == 0

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_error_handling_during_startup(self, randomizer):
        """Test error handling during browser startup."""
        # Test with no browser engines configured
        with pytest.raises(ValueError, match="No browser engines configured"):
            BrowserManager(browser_engines=[], randomizer=randomizer)

    @pytest.mark.asyncio
    async def test_cleanup_on_failure(self, randomizer):
        """Test cleanup when browser startup fails."""
        with patch("playwright.async_api.async_playwright") as mock_playwright:
            # Make browser launch fail
            mock_playwright.return_value.start.return_value.chromium.launch.side_effect = Exception(
                "Launch failed"
            )

            browser_manager = BrowserManager(
                browser_engines=["chromium"], randomizer=randomizer
            )

            with pytest.raises(RuntimeError):
                await browser_manager.start()

            # Cleanup should have been attempted
            assert browser_manager.browser is None

    @pytest.mark.asyncio
    async def test_user_agent_pool(self, mock_browser_manager):
        """Test user agent pool and selection."""
        await mock_browser_manager.start()

        # Test that user agent pool is properly defined
        assert len(mock_browser_manager.user_agents) > 0

        # Test user agent selection
        ua = mock_browser_manager._get_random_user_agent()
        assert ua in mock_browser_manager.user_agents

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_viewport_pool(self, mock_browser_manager):
        """Test viewport size pool and selection."""
        await mock_browser_manager.start()

        # Test that viewport pool is properly defined
        assert len(mock_browser_manager.viewport_sizes) > 0

        # Test viewport selection
        viewport = mock_browser_manager._get_random_viewport()
        assert viewport["width"] >= 800
        assert viewport["height"] >= 600

        await mock_browser_manager.stop()

    @pytest.mark.asyncio
    async def test_context_manager_usage(self, randomizer):
        """Test browser manager as context manager."""
        browser_manager = MockBrowserManager(
            browser_engines=["chromium"], randomizer=randomizer
        )

        async with browser_manager as browser:
            assert browser.page is not None
            assert browser.context is not None

        # Should be properly cleaned up after context exit
        assert browser_manager.page is None or hasattr(browser_manager, "_cleaned_up")

    @pytest.mark.asyncio
    async def test_timeout_configuration(self, randomizer):
        """Test timeout configuration."""
        custom_timeout = 60000  # 60 seconds
        browser_manager = MockBrowserManager(
            browser_engines=["chromium"], timeout=custom_timeout, randomizer=randomizer
        )

        assert browser_manager.timeout == custom_timeout

        await browser_manager.start()
        # Context should have the custom timeout
        assert browser_manager.context is not None
        await browser_manager.stop()

    @pytest.mark.asyncio
    async def test_randomization_disabled(self, randomizer):
        """Test behavior when randomization is disabled."""
        browser_manager = MockBrowserManager(
            browser_engines=["chromium"],
            enable_randomization=False,
            randomizer=randomizer,
        )

        await browser_manager.start()

        # Should use first engine when randomization disabled
        assert browser_manager.browser_engine == "chromium"

        # Viewport should be consistent
        viewport1 = browser_manager._get_random_viewport()
        viewport2 = browser_manager._get_random_viewport()
        assert viewport1 == viewport2

        await browser_manager.stop()
