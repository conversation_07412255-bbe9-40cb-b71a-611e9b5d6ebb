"""
Additional mock utilities for testing.
"""

from typing import Dict, List, Any, Optional
from unittest.mock import MagicMock, AsyncMock
import json


class MockStorageClient:
    """Mock storage client for testing."""

    def __init__(self):
        self._storage: Dict[str, Any] = {}
        self.actions_performed: List[tuple] = []

    async def store_result(
        self, service: str, email: str, result: Dict[str, Any]
    ) -> bool:
        """Mock store result."""
        key = f"{service}:{email}"
        self._storage[key] = result
        self.actions_performed.append(("store_result", service, email))
        return True

    async def get_result(self, service: str, email: str) -> Optional[Dict[str, Any]]:
        """Mock get result."""
        key = f"{service}:{email}"
        self.actions_performed.append(("get_result", service, email))
        return self._storage.get(key)

    async def list_results(self, service: str) -> List[Dict[str, Any]]:
        """Mock list results."""
        self.actions_performed.append(("list_results", service))
        return [v for k, v in self._storage.items() if k.startswith(f"{service}:")]

    def clear_storage(self):
        """Clear mock storage."""
        self._storage.clear()
        self.actions_performed.clear()


class MockWebhookClient:
    """Mock webhook client for testing."""

    def __init__(self):
        self.sent_webhooks: List[Dict[str, Any]] = []
        self.actions_performed: List[tuple] = []

    async def send_webhook(self, url: str, data: Dict[str, Any]) -> bool:
        """Mock send webhook."""
        webhook_data = {"url": url, "data": data, "timestamp": "mock_timestamp"}
        self.sent_webhooks.append(webhook_data)
        self.actions_performed.append(("send_webhook", url))
        return True

    def get_sent_webhooks(self) -> List[Dict[str, Any]]:
        """Get all sent webhooks."""
        return self.sent_webhooks.copy()

    def clear_webhooks(self):
        """Clear sent webhooks."""
        self.sent_webhooks.clear()
        self.actions_performed.clear()


class MockApiResponse:
    """Mock API response for external service calls."""

    def __init__(
        self,
        status_code: int = 200,
        json_data: Dict[str, Any] = None,
        text_data: str = None,
        headers: Dict[str, str] = None,
    ):
        self.status_code = status_code
        self._json_data = json_data or {}
        self._text_data = text_data or ""
        self.headers = headers or {}

    async def json(self) -> Dict[str, Any]:
        """Mock JSON response."""
        return self._json_data

    async def text(self) -> str:
        """Mock text response."""
        return self._text_data

    def raise_for_status(self):
        """Mock raise for status."""
        if self.status_code >= 400:
            raise Exception(f"HTTP {self.status_code}")


class MockHttpClient:
    """Mock HTTP client for testing external API calls."""

    def __init__(self):
        self.responses: Dict[str, MockApiResponse] = {}
        self.requests_made: List[Dict[str, Any]] = []

    def set_response(self, url: str, response: MockApiResponse):
        """Set mock response for a URL."""
        self.responses[url] = response

    async def get(self, url: str, **kwargs) -> MockApiResponse:
        """Mock GET request."""
        self.requests_made.append({"method": "GET", "url": url, "kwargs": kwargs})
        return self.responses.get(url, MockApiResponse())

    async def post(self, url: str, **kwargs) -> MockApiResponse:
        """Mock POST request."""
        self.requests_made.append({"method": "POST", "url": url, "kwargs": kwargs})
        return self.responses.get(url, MockApiResponse())

    def clear_requests(self):
        """Clear request history."""
        self.requests_made.clear()


def create_mock_service_result(
    service: str, success: bool = True, api_key: str = None, error_message: str = None
) -> Dict[str, Any]:
    """Create a mock service result for testing."""
    result = {
        "service": service,
        "success": success,
        "timestamp": "mock_timestamp",
        "email": "<EMAIL>",
        "profile": {
            "first_name": "Test",
            "last_name": "User",
            "email": "<EMAIL>",
        },
    }

    if success and api_key:
        result["api_key"] = api_key
        result["status"] = "completed"
    elif not success and error_message:
        result["error"] = error_message
        result["status"] = "failed"
    else:
        result["status"] = "pending"

    return result


def create_mock_verification_email(
    service: str,
    email: str,
    verification_code: str = "123456",
    verification_link: str = None,
) -> Dict[str, Any]:
    """Create a mock verification email for testing."""
    if verification_link is None:
        verification_link = f"https://{service}.example.com/verify?token=mock_token"

    return {
        "subject": f"{service.title()} Email Verification",
        "sender": f"noreply@{service}.example.com",
        "recipient": email,
        "body": f"""
        Welcome to {service.title()}!
        
        Please verify your email address by clicking the link below:
        {verification_link}
        
        Or use this verification code: {verification_code}
        
        If you didn't sign up for {service.title()}, please ignore this email.
        """,
        "html_body": f'''
        <html>
        <body>
        <h1>Welcome to {service.title()}!</h1>
        <p>Please verify your email address by clicking the link below:</p>
        <a href="{verification_link}">Verify Email Address</a>
        <p>Or use this verification code: <strong>{verification_code}</strong></p>
        <p><small>If you didn't sign up for {service.title()}, please ignore this email.</small></p>
        </body>
        </html>
        ''',
    }
