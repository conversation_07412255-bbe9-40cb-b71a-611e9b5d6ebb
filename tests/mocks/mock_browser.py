"""
Mock browser manager for testing.

This provides a fake browser that simulates browser behavior without actually
launching a real browser instance. Perfect for fast, reliable integration tests.
"""

from typing import Dict, List, Any, Optional, Tuple
from unittest.mock import AsyncMock, MagicMock
import asyncio


class MockElement:
    """Mock browser element."""

    def __init__(self, selector: str, text_content: str = "mock text", value: str = ""):
        self.selector = selector
        self._text_content = text_content
        self._value = value
        self._is_visible = True
        self._is_enabled = True

    async def click(self, **kwargs):
        """Mock click action."""
        await asyncio.sleep(0.01)  # Tiny delay to simulate async
        return True

    async def fill(self, value: str, **kwargs):
        """Mock fill action."""
        self._value = value
        await asyncio.sleep(0.01)
        return True

    async def text_content(self) -> str:
        """Mock text content."""
        return self._text_content

    async def get_attribute(self, name: str) -> Optional[str]:
        """Mock get attribute."""
        if name == "value":
            return self._value
        return None

    async def is_visible(self, **kwargs) -> bool:
        """Mock visibility check."""
        return self._is_visible

    async def is_enabled(self, **kwargs) -> bool:
        """Mock enabled check."""
        return self._is_enabled

    def set_visible(self, visible: bool):
        """Set mock visibility."""
        self._is_visible = visible

    def set_enabled(self, enabled: bool):
        """Set mock enabled state."""
        self._is_enabled = enabled


class MockPage:
    """Mock browser page."""

    def __init__(self, mock_responses: Dict[str, Any] = None):
        self.mock_responses = mock_responses or {}
        self.url = "https://mock.example.com"
        self._content = "<html><body>Mock page content</body></html>"
        self._title = "Mock Page"
        self.context = MagicMock()

    async def goto(self, url: str, **kwargs):
        """Mock navigation."""
        self.url = url
        await asyncio.sleep(0.01)
        return True

    async def wait_for_selector(self, selector: str, **kwargs) -> MockElement:
        """Mock wait for selector."""
        text_content = self.mock_responses.get(f"text_{selector}", "mock text")
        return MockElement(selector, text_content)

    async def query_selector(self, selector: str, **kwargs) -> Optional[MockElement]:
        """Mock query selector."""
        if selector in self.mock_responses.get("missing_selectors", []):
            return None
        text_content = self.mock_responses.get(f"text_{selector}", "mock text")
        return MockElement(selector, text_content)

    async def query_selector_all(self, selector: str, **kwargs) -> List[MockElement]:
        """Mock query selector all."""
        count = self.mock_responses.get(f"count_{selector}", 1)
        return [MockElement(f"{selector}_{i}") for i in range(count)]

    def locator(self, selector: str) -> "MockLocator":
        """Mock locator."""
        return MockLocator(selector, self.mock_responses)

    async def content(self) -> str:
        """Mock page content."""
        return self.mock_responses.get("page_content", self._content)

    async def title(self) -> str:
        """Mock page title."""
        return self.mock_responses.get("page_title", self._title)

    async def evaluate(self, script: str, **kwargs) -> Any:
        """Mock JavaScript evaluation."""
        # Return mock responses for common scripts
        if "navigator.userAgent" in script:
            return self.mock_responses.get("user_agent", "Mock UserAgent")
        elif "window.innerWidth" in script:
            return self.mock_responses.get("viewport_width", 1280)
        elif "window.innerHeight" in script:
            return self.mock_responses.get("viewport_height", 720)
        elif "navigator.clipboard.readText" in script:
            return self.mock_responses.get("clipboard", "mock-clipboard-content")
        elif "document.body.innerText" in script:
            return self.mock_responses.get("page_text", "Mock page text content")
        else:
            return self.mock_responses.get("evaluate_result", True)

    async def screenshot(self, **kwargs):
        """Mock screenshot."""
        await asyncio.sleep(0.01)
        return b"mock_screenshot_data"

    async def close(self):
        """Mock page close."""
        await asyncio.sleep(0.01)

    def on(self, event: str, handler):
        """Mock event handler."""
        pass

    @property
    def mouse(self):
        """Mock mouse."""
        return MockMouse()


class MockLocator:
    """Mock Playwright locator."""

    def __init__(self, selector: str, mock_responses: Dict[str, Any] = None):
        self.selector = selector
        self.mock_responses = mock_responses or {}

    async def click(self, **kwargs):
        """Mock click."""
        await asyncio.sleep(0.01)
        return True

    async def fill(self, value: str, **kwargs):
        """Mock fill."""
        await asyncio.sleep(0.01)
        return True

    async def count(self) -> int:
        """Mock count."""
        return self.mock_responses.get(f"count_{self.selector}", 1)

    async def is_visible(self, **kwargs) -> bool:
        """Mock visibility."""
        return self.mock_responses.get(f"visible_{self.selector}", True)

    def first(self) -> "MockLocator":
        """Mock first locator."""
        return MockLocator(f"{self.selector}_first", self.mock_responses)

    def nth(self, index: int) -> "MockLocator":
        """Mock nth locator."""
        return MockLocator(f"{self.selector}_{index}", self.mock_responses)


class MockMouse:
    """Mock mouse actions."""

    async def click(self, x: float, y: float, **kwargs):
        """Mock mouse click."""
        await asyncio.sleep(0.01)


class MockBrowserManager:
    """
    Mock browser manager that simulates browser behavior for testing.

    This replaces the real BrowserManager in integration tests to provide:
    - Fast execution (no real browser startup)
    - Predictable responses
    - Easy test setup
    """

    def __init__(self, mock_responses: Dict[str, Any] = None):
        self.mock_responses = mock_responses or {}
        self.actions_performed: List[Tuple[str, ...]] = []
        self.headless = True
        self.timeout = 30000
        self.enable_randomization = False
        self.browser_engine = "mock"

        # Mock browser objects
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = MockPage(mock_responses)

    async def start(self):
        """Mock browser startup."""
        await asyncio.sleep(0.01)
        self.actions_performed.append(("start",))

    async def stop(self):
        """Mock browser shutdown."""
        await asyncio.sleep(0.01)
        self.actions_performed.append(("stop",))

    async def __aenter__(self):
        """Mock context manager entry."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Mock context manager exit."""
        await self.stop()

    async def goto(self, url: str, **kwargs):
        """Mock navigation."""
        self.actions_performed.append(("goto", url))
        await self.page.goto(url, **kwargs)
        return self.mock_responses.get("goto_result", True)

    async def fill(self, selector: str, value: str, **kwargs):
        """Mock form filling."""
        self.actions_performed.append(("fill", selector, value))
        element = await self.page.wait_for_selector(selector)
        await element.fill(value)
        return True

    async def click(self, selector: str, **kwargs):
        """Mock clicking."""
        self.actions_performed.append(("click", selector))
        element = await self.page.wait_for_selector(selector)
        await element.click()
        return True

    async def wait_for_selector(self, selector: str, **kwargs) -> MockElement:
        """Mock wait for selector."""
        self.actions_performed.append(("wait_for_selector", selector))
        return await self.page.wait_for_selector(selector, **kwargs)

    async def get_text(self, selector: str) -> str:
        """Mock get text."""
        self.actions_performed.append(("get_text", selector))
        element = await self.page.wait_for_selector(selector)
        return await element.text_content()

    async def screenshot(self, path: str = "mock_screenshot.png"):
        """Mock screenshot."""
        self.actions_performed.append(("screenshot", path))
        return await self.page.screenshot()

    async def wait(self, ms: int):
        """Mock wait."""
        await asyncio.sleep(ms / 10000)  # Much faster than real wait
        self.actions_performed.append(("wait", ms))

    async def random_delay(self, min_seconds: float = 0.01, max_seconds: float = 0.02):
        """Mock random delay - much faster."""
        await asyncio.sleep(0.01)
        self.actions_performed.append(("random_delay", min_seconds, max_seconds))

    async def random_short_delay(self):
        """Mock short delay."""
        await asyncio.sleep(0.01)
        self.actions_performed.append(("random_short_delay",))

    async def random_medium_delay(self):
        """Mock medium delay."""
        await asyncio.sleep(0.01)
        self.actions_performed.append(("random_medium_delay",))

    async def random_long_delay(self):
        """Mock long delay."""
        await asyncio.sleep(0.01)
        self.actions_performed.append(("random_long_delay",))

    async def extract_clipboard_content(self) -> Optional[str]:
        """Mock clipboard extraction."""
        self.actions_performed.append(("extract_clipboard_content",))
        clipboard_content = self.mock_responses.get("clipboard", None)
        if clipboard_content:
            return clipboard_content
        return None

    def get_performed_actions(self) -> List[Tuple[str, ...]]:
        """Get list of all actions performed during test."""
        return self.actions_performed.copy()

    def clear_actions(self):
        """Clear the actions log."""
        self.actions_performed.clear()

    def set_mock_response(self, key: str, value: Any):
        """Set a mock response for testing."""
        self.mock_responses[key] = value
        if hasattr(self.page, "mock_responses"):
            self.page.mock_responses[key] = value

    def simulate_error(self, action: str, error_message: str = "Mock error"):
        """Configure the mock to simulate an error for a specific action."""
        self.mock_responses[f"error_{action}"] = error_message


class MockEmailClient:
    """Mock email client for testing."""

    def __init__(self, mock_emails: List[Dict[str, Any]] = None):
        self.mock_emails = mock_emails or []
        self.actions_performed: List[Tuple[str, ...]] = []

    async def wait_for_email(
        self, service_name: str, email_address: str, timeout: int = 60
    ) -> bool:
        """Mock wait for email."""
        self.actions_performed.append(
            ("wait_for_email", service_name, email_address, timeout)
        )
        await asyncio.sleep(0.01)
        return len(self.mock_emails) > 0

    async def get_latest_email_content(
        self, service_name: str, email_address: str
    ) -> Optional[str]:
        """Mock get latest email content."""
        self.actions_performed.append(
            ("get_latest_email_content", service_name, email_address)
        )
        if self.mock_emails:
            return self.mock_emails[-1].get("body", "")
        return None

    def add_mock_email(self, email_data: Dict[str, Any]):
        """Add a mock email to the client."""
        self.mock_emails.append(email_data)


class MockRandomizer:
    """Mock randomizer with predictable outputs."""

    def __init__(self, seed_value: int = 42):
        self.seed_value = seed_value
        self.actions_performed: List[Tuple[str, ...]] = []

    def choice(self, choices: List[Any]) -> Any:
        """Mock choice - returns first item."""
        self.actions_performed.append(("choice", len(choices)))
        return choices[0] if choices else None

    def randint(self, min_val: int, max_val: int) -> int:
        """Mock randint - returns predictable value."""
        self.actions_performed.append(("randint", min_val, max_val))
        return min_val + (self.seed_value % (max_val - min_val + 1))

    def random(self) -> float:
        """Mock random - returns predictable value."""
        self.actions_performed.append(("random",))
        return 0.5

    async def delay(self, min_seconds: float, max_seconds: float):
        """Mock delay - much faster."""
        self.actions_performed.append(("delay", min_seconds, max_seconds))
        await asyncio.sleep(0.01)

    async def human_type(self, element: MockElement, text: str):
        """Mock human typing."""
        self.actions_performed.append(("human_type", text))
        await element.fill(text)

    async def human_click(self, page: MockPage, element: MockElement):
        """Mock human click."""
        self.actions_performed.append(("human_click",))
        await element.click()

    async def short_delay(self):
        """Mock short delay."""
        await asyncio.sleep(0.01)

    async def medium_delay(self):
        """Mock medium delay."""
        await asyncio.sleep(0.01)

    async def long_delay(self):
        """Mock long delay."""
        await asyncio.sleep(0.01)

    async def page_load_delay(self):
        """Mock page load delay."""
        await asyncio.sleep(0.01)

    async def user_action_delay(self):
        """Mock user action delay."""
        await asyncio.sleep(0.01)
