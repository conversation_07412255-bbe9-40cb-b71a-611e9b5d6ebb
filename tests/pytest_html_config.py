"""Pytest HTML report configuration and customizations."""

import pytest
from datetime import datetime


def pytest_html_report_title(report):
    """Customize the HTML report title."""
    report.title = "RegBot Automation Test Report"


def pytest_html_results_table_header(cells):
    """Customize the HTML report table headers."""
    cells.insert(2, "<th>Description</th>")
    cells.insert(1, "<th>Time</th>")


def pytest_html_results_table_row(report, cells):
    """Customize the HTML report table rows."""
    cells.insert(2, f"<td>{getattr(report, 'description', '')}</td>")
    cells.insert(1, f"<td>{datetime.now().strftime('%H:%M:%S')}</td>")


def pytest_html_results_summary(prefix, summary, postfix):
    """Customize the HTML report summary section."""
    # Add custom summary information
    prefix.extend(
        [
            "<div style='margin: 10px 0; padding: 10px; background-color: #f0f0f0; border-radius: 5px;'>",
            "<h3>Test Execution Summary</h3>",
            f"<p><strong>Execution Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>",
            "<p><strong>Test Categories:</strong></p>",
            "<ul>",
            "<li>Unit Tests: Core functionality validation</li>",
            "<li>Integration Tests: Component interaction validation</li>",
            "<li>Browser Tests: Real browser functionality validation</li>",
            "<li>E2E Tests: Complete workflow validation</li>",
            "</ul>",
            "</div>",
        ]
    )


@pytest.fixture(autouse=True)
def add_test_description(request):
    """Add test descriptions to HTML report."""
    if hasattr(request.node.function, "__doc__") and request.node.function.__doc__:
        request.node.description = request.node.function.__doc__.strip()
    else:
        request.node.description = "No description available"


def pytest_configure(config):
    """Configure pytest with custom markers and options."""
    # Add custom markers
    config.addinivalue_line(
        "markers", "unit: Unit tests - fast, isolated tests of individual components"
    )
    config.addinivalue_line(
        "markers",
        "integration: Integration tests - test component interactions with mocks",
    )
    config.addinivalue_line(
        "markers", "browser: Browser tests - test real browser functionality"
    )
    config.addinivalue_line(
        "markers", "e2e: End-to-end tests - test complete workflows"
    )
    config.addinivalue_line(
        "markers", "slow: Slow tests - tests that take significant time to run"
    )
    config.addinivalue_line(
        "markers", "performance: Performance tests - benchmarking and load testing"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test items during collection."""
    # Auto-mark tests based on their location
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "browser" in str(item.fspath):
            item.add_marker(pytest.mark.browser)
            item.add_marker(pytest.mark.slow)
        elif "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
            item.add_marker(pytest.mark.slow)

        # Add slow marker to browser and e2e tests
        if any(marker.name in ["browser", "e2e"] for marker in item.iter_markers()):
            item.add_marker(pytest.mark.slow)


def pytest_html_results_table_html(report, data):
    """Customize individual test result HTML."""
    if report.passed:
        data.append('<div style="color: green;">✓ PASSED</div>')
    elif report.failed:
        data.append('<div style="color: red;">✗ FAILED</div>')
    elif report.skipped:
        data.append('<div style="color: orange;">⊘ SKIPPED</div>')


class TestMetrics:
    """Collect and display test metrics."""

    def __init__(self):
        self.start_time = datetime.now()
        self.test_counts = {
            "unit": 0,
            "integration": 0,
            "browser": 0,
            "e2e": 0,
            "total": 0,
        }

    def add_test(self, test_type):
        """Add a test to metrics."""
        self.test_counts[test_type] += 1
        self.test_counts["total"] += 1

    def get_summary(self):
        """Get test execution summary."""
        duration = datetime.now() - self.start_time
        return {"duration": str(duration), "counts": self.test_counts}


# Global test metrics instance
test_metrics = TestMetrics()


def pytest_runtest_setup(item):
    """Hook called before each test runs."""
    # Track test types
    if item.get_closest_marker("unit"):
        test_metrics.add_test("unit")
    elif item.get_closest_marker("integration"):
        test_metrics.add_test("integration")
    elif item.get_closest_marker("browser"):
        test_metrics.add_test("browser")
    elif item.get_closest_marker("e2e"):
        test_metrics.add_test("e2e")


def pytest_terminal_summary(terminalreporter, exitstatus, config):
    """Add custom terminal summary information."""
    metrics = test_metrics.get_summary()

    terminalreporter.write_sep("=", "Test Execution Summary")
    terminalreporter.write_line(f"Total execution time: {metrics['duration']}")
    terminalreporter.write_line(f"Total tests run: {metrics['counts']['total']}")
    terminalreporter.write_line(f"  - Unit tests: {metrics['counts']['unit']}")
    terminalreporter.write_line(
        f"  - Integration tests: {metrics['counts']['integration']}"
    )
    terminalreporter.write_line(f"  - Browser tests: {metrics['counts']['browser']}")
    terminalreporter.write_line(f"  - E2E tests: {metrics['counts']['e2e']}")

    if exitstatus == 0:
        terminalreporter.write_line("✅ All tests passed!", green=True)
    else:
        terminalreporter.write_line("❌ Some tests failed!", red=True)
