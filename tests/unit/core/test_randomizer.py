"""
Unit tests for randomizer module.

Tests randomization logic, profiles, and timing behavior without external dependencies.
"""

import pytest
import asyncio
import time
from unittest.mock import patch, MagicMock

from core.randomizer import (
    Randomizer,
    RandomizerConfig,
    RandomizationProfile,
    create_randomizer,
)


class TestRandomizationProfile:
    """Test randomization profile enum."""

    def test_profile_values(self):
        """Test profile enum values."""
        assert RandomizationProfile.CAUTIOUS == "cautious"
        assert RandomizationProfile.NORMAL == "normal"
        assert RandomizationProfile.IMPATIENT == "impatient"
        assert RandomizationProfile.TESTING == "testing"

    def test_profile_membership(self):
        """Test profile membership."""
        profiles = list(RandomizationProfile)
        assert len(profiles) == 4
        assert RandomizationProfile.NORMAL in profiles


class TestRandomizerConfig:
    """Test randomizer configuration."""

    def test_default_config(self):
        """Test default configuration values."""
        config = RandomizerConfig()
        assert config.enabled is True
        assert config.profile == RandomizationProfile.NORMAL
        assert config.global_multiplier == 1.0
        assert config.typing_delay_mean == 0.12
        assert config.typo_probability == 0.02

    def test_custom_config(self):
        """Test custom configuration values."""
        config = RandomizerConfig(
            enabled=False,
            profile=RandomizationProfile.TESTING,
            global_multiplier=0.5,
            typing_delay_mean=0.05,
        )
        assert config.enabled is False
        assert config.profile == RandomizationProfile.TESTING
        assert config.global_multiplier == 0.5
        assert config.typing_delay_mean == 0.05

    def test_workflow_delays_default(self):
        """Test default workflow delays."""
        config = RandomizerConfig()
        assert "after_email_entry" in config.workflow_delays
        assert "after_navigation" in config.workflow_delays
        assert "session_ending" in config.workflow_delays

        # Check delay ranges are tuples
        delay_range = config.workflow_delays["after_email_entry"]
        assert isinstance(delay_range, tuple)
        assert len(delay_range) == 2
        assert delay_range[0] < delay_range[1]

    def test_delay_range_validation(self):
        """Test delay range validation."""
        # Valid range
        config = RandomizerConfig(short_delay_range=(0.1, 0.5))
        assert config.short_delay_range == (0.1, 0.5)

        # Test with equal values (edge case)
        config = RandomizerConfig(short_delay_range=(0.5, 0.5))
        assert config.short_delay_range == (0.5, 0.5)


class TestRandomizer:
    """Test randomizer functionality."""

    def test_randomizer_creation(self):
        """Test randomizer creation."""
        config = RandomizerConfig()
        randomizer = Randomizer(config)
        assert randomizer.config == config
        assert randomizer.enabled == config.enabled

    def test_randomizer_disabled(self):
        """Test randomizer when disabled."""
        config = RandomizerConfig(enabled=False)
        randomizer = Randomizer(config)
        assert randomizer.enabled is False

    def test_choice_method(self):
        """Test choice method."""
        randomizer = Randomizer(RandomizerConfig())
        choices = ["a", "b", "c", "d"]

        # Test multiple selections
        results = [randomizer.choice(choices) for _ in range(100)]

        # All results should be from the choices
        assert all(result in choices for result in results)

        # Should have some variation (not all the same)
        unique_results = set(results)
        assert len(unique_results) > 1

    def test_choice_empty_list(self):
        """Test choice with empty list."""
        randomizer = Randomizer(RandomizerConfig())
        with pytest.raises(IndexError):
            randomizer.choice([])

    def test_choice_single_item(self):
        """Test choice with single item."""
        randomizer = Randomizer(RandomizerConfig())
        result = randomizer.choice(["only_choice"])
        assert result == "only_choice"

    def test_randint_method(self):
        """Test randint method."""
        randomizer = Randomizer(RandomizerConfig())

        # Test range
        results = [randomizer.randint(1, 10) for _ in range(100)]

        # All results should be in range
        assert all(1 <= result <= 10 for result in results)

        # Should have some variation
        unique_results = set(results)
        assert len(unique_results) > 1

    def test_randint_equal_bounds(self):
        """Test randint with equal bounds."""
        randomizer = Randomizer(RandomizerConfig())
        result = randomizer.randint(5, 5)
        assert result == 5

    def test_random_method(self):
        """Test random method."""
        randomizer = Randomizer(RandomizerConfig())

        results = [randomizer.random() for _ in range(100)]

        # All results should be between 0 and 1
        assert all(0.0 <= result < 1.0 for result in results)

        # Should have variation
        unique_results = set(results)
        assert len(unique_results) > 50  # High probability of uniqueness

    @pytest.mark.asyncio
    async def test_delay_method(self):
        """Test delay method timing."""
        config = RandomizerConfig(global_multiplier=0.01)  # Speed up for testing
        randomizer = Randomizer(config)

        start_time = time.time()
        await randomizer.delay(0.1, 0.2)
        elapsed_time = time.time() - start_time

        # Should take some time (accounting for multiplier)
        assert elapsed_time >= 0.001  # At least 1ms
        assert elapsed_time < 0.1  # But not too long for tests

    @pytest.mark.asyncio
    async def test_delay_disabled(self):
        """Test delay when randomizer is disabled."""
        config = RandomizerConfig(enabled=False)
        randomizer = Randomizer(config)

        start_time = time.time()
        await randomizer.delay(1.0, 2.0)  # Would be slow if enabled
        elapsed_time = time.time() - start_time

        # Should be very fast when disabled
        assert elapsed_time < 0.01

    @pytest.mark.asyncio
    async def test_short_delay(self):
        """Test short delay method."""
        config = RandomizerConfig(short_delay_range=(0.01, 0.02), global_multiplier=0.1)
        randomizer = Randomizer(config)

        start_time = time.time()
        await randomizer.short_delay()
        elapsed_time = time.time() - start_time

        assert elapsed_time >= 0.001
        assert elapsed_time < 0.1

    @pytest.mark.asyncio
    async def test_medium_delay(self):
        """Test medium delay method."""
        config = RandomizerConfig(
            medium_delay_range=(0.01, 0.02), global_multiplier=0.1
        )
        randomizer = Randomizer(config)

        start_time = time.time()
        await randomizer.medium_delay()
        elapsed_time = time.time() - start_time

        assert elapsed_time >= 0.001
        assert elapsed_time < 0.1

    @pytest.mark.asyncio
    async def test_workflow_delay(self):
        """Test workflow-specific delays."""
        config = RandomizerConfig(global_multiplier=0.01)
        config.workflow_delays["test_delay"] = (0.05, 0.10)
        randomizer = Randomizer(config)

        start_time = time.time()
        await randomizer.workflow_delay("test_delay")
        elapsed_time = time.time() - start_time

        assert elapsed_time >= 0.0001
        assert elapsed_time < 0.1

    @pytest.mark.asyncio
    async def test_workflow_delay_unknown(self):
        """Test workflow delay with unknown key."""
        randomizer = Randomizer(RandomizerConfig())

        # Should not raise an error, should use default
        await randomizer.workflow_delay("unknown_delay_key")

    @pytest.mark.asyncio
    async def test_human_type_mock(self):
        """Test human typing simulation."""
        config = RandomizerConfig(global_multiplier=0.01)
        randomizer = Randomizer(config)

        # Mock element
        mock_element = MagicMock()
        mock_element.fill = MagicMock()

        start_time = time.time()
        await randomizer.human_type(mock_element, "test text")
        elapsed_time = time.time() - start_time

        # Should take some time for typing simulation
        assert elapsed_time >= 0.001

        # Should have called fill method
        mock_element.fill.assert_called_once_with("test text")

    @pytest.mark.asyncio
    async def test_human_click_mock(self):
        """Test human click simulation."""
        config = RandomizerConfig(global_multiplier=0.01)
        randomizer = Randomizer(config)

        # Mock page and element
        mock_page = MagicMock()
        mock_element = MagicMock()
        mock_element.click = MagicMock()

        start_time = time.time()
        await randomizer.human_click(mock_page, mock_element)
        elapsed_time = time.time() - start_time

        # Should take some time for click simulation
        assert elapsed_time >= 0.001

        # Should have called click method
        mock_element.click.assert_called_once()

    def test_global_multiplier_effect(self):
        """Test global multiplier affects delay calculations."""
        # Fast multiplier
        config_fast = RandomizerConfig(global_multiplier=0.1)
        randomizer_fast = Randomizer(config_fast)

        # Slow multiplier
        config_slow = RandomizerConfig(global_multiplier=2.0)
        randomizer_slow = Randomizer(config_slow)

        # Same base range
        min_delay, max_delay = 1.0, 2.0

        # Fast should be shorter
        fast_delay = randomizer_fast._calculate_delay(min_delay, max_delay)
        slow_delay = randomizer_slow._calculate_delay(min_delay, max_delay)

        assert fast_delay < slow_delay


class TestRandomizerProfiles:
    """Test different randomizer profiles."""

    def test_cautious_profile(self):
        """Test cautious profile has longer delays."""
        cautious = create_randomizer(RandomizationProfile.CAUTIOUS)
        normal = create_randomizer(RandomizationProfile.NORMAL)

        # Cautious should have longer delays
        cautious_config = cautious.config
        normal_config = normal.config

        # Compare a delay range
        assert (
            cautious_config.medium_delay_range[0] >= normal_config.medium_delay_range[0]
        )
        assert (
            cautious_config.medium_delay_range[1] >= normal_config.medium_delay_range[1]
        )

    def test_impatient_profile(self):
        """Test impatient profile has shorter delays."""
        impatient = create_randomizer(RandomizationProfile.IMPATIENT)
        normal = create_randomizer(RandomizationProfile.NORMAL)

        # Impatient should have shorter delays
        impatient_config = impatient.config
        normal_config = normal.config

        # Compare a delay range
        assert (
            impatient_config.short_delay_range[0] <= normal_config.short_delay_range[0]
        )
        assert (
            impatient_config.short_delay_range[1] <= normal_config.short_delay_range[1]
        )

    def test_testing_profile(self):
        """Test testing profile has minimal delays."""
        testing = create_randomizer(RandomizationProfile.TESTING)

        # Testing should have very short delays
        testing_config = testing.config

        # All delays should be very short
        assert testing_config.short_delay_range[1] < 0.1
        assert testing_config.medium_delay_range[1] < 0.2
        assert testing_config.global_multiplier <= 0.1


class TestCreateRandomizer:
    """Test randomizer factory function."""

    def test_create_randomizer_profiles(self):
        """Test creating randomizers with different profiles."""
        for profile in RandomizationProfile:
            randomizer = create_randomizer(profile)
            assert isinstance(randomizer, Randomizer)
            assert randomizer.config.profile == profile

    def test_create_randomizer_enabled_disabled(self):
        """Test creating enabled/disabled randomizers."""
        enabled = create_randomizer(RandomizationProfile.NORMAL, enabled=True)
        disabled = create_randomizer(RandomizationProfile.NORMAL, enabled=False)

        assert enabled.enabled is True
        assert disabled.enabled is False

    def test_create_randomizer_custom_config(self):
        """Test creating randomizer with custom config."""
        custom_config = RandomizerConfig(typing_delay_mean=0.05, global_multiplier=0.5)

        randomizer = create_randomizer(config=custom_config)
        assert randomizer.config.typing_delay_mean == 0.05
        assert randomizer.config.global_multiplier == 0.5


@pytest.mark.unit
class TestRandomizerIntegration:
    """Integration tests for randomizer functionality."""

    @pytest.mark.asyncio
    async def test_randomizer_workflow_simulation(self):
        """Test complete workflow with randomizer."""
        config = RandomizerConfig(global_multiplier=0.01)  # Speed up
        randomizer = Randomizer(config)

        # Simulate a complete workflow
        start_time = time.time()

        # Navigation delay
        await randomizer.workflow_delay("after_navigation")

        # User action delay
        await randomizer.user_action_delay()

        # Short delay
        await randomizer.short_delay()

        # Email entry delay
        await randomizer.workflow_delay("after_email_entry")

        elapsed_time = time.time() - start_time

        # Should have taken some time but not too long for tests
        assert elapsed_time >= 0.001
        assert elapsed_time < 0.5

    def test_randomizer_reproducibility_with_seed(self):
        """Test randomizer reproducibility with seed."""
        # Set same seed for both randomizers
        with patch("random.seed") as mock_seed:
            randomizer1 = Randomizer(RandomizerConfig())
            randomizer2 = Randomizer(RandomizerConfig())

            # Both should set seed
            assert mock_seed.call_count >= 2

    def test_randomizer_different_instances_different_results(self):
        """Test different randomizer instances produce different results."""
        randomizer1 = Randomizer(RandomizerConfig())
        randomizer2 = Randomizer(RandomizerConfig())

        choices = list(range(100))

        # Generate multiple results from each
        results1 = [randomizer1.choice(choices) for _ in range(20)]
        results2 = [randomizer2.choice(choices) for _ in range(20)]

        # Should be different (with high probability)
        assert results1 != results2
