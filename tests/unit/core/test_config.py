"""
Unit tests for core configuration management.

Tests configuration loading, validation, and error handling without external dependencies.
"""

import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch, mock_open

from core.config import (
    Config,
    BrowserConfig,
    EmailConfig,
    ServiceConfig,
    BitBrowserConfig,
    EmailDomainConfig,
    WebhookConfig,
    EmailInterceptorConfig,
    ContinuousAutomationConfig,
)


class TestBitBrowserConfig:
    """Test BitBrowser configuration."""

    def test_default_values(self):
        """Test default configuration values."""
        config = BitBrowserConfig()
        assert config.browser_id == "bit"
        assert config.credentials == {}
        assert config.api_url == "http://127.0.0.1:54345"

    def test_custom_values(self):
        """Test custom configuration values."""
        config = BitBrowserConfig(
            browser_id="custom_browser",
            api_url="http://192.168.1.100:54345",
            credentials={"username": "test", "password": "secret"},
        )
        assert config.browser_id == "custom_browser"
        assert config.api_url == "http://192.168.1.100:54345"
        assert config.credentials["username"] == "test"


class TestEmailDomainConfig:
    """Test email domain configuration."""

    def test_default_values(self):
        """Test default email domain values."""
        config = EmailDomainConfig(domain="test.example.com")
        assert config.domain == "test.example.com"
        assert config.weight == 100
        assert config.enabled is True

    def test_custom_values(self):
        """Test custom email domain values."""
        config = EmailDomainConfig(domain="custom.domain.com", weight=50, enabled=False)
        assert config.domain == "custom.domain.com"
        assert config.weight == 50
        assert config.enabled is False


class TestWebhookConfig:
    """Test webhook configuration."""

    def test_default_values(self):
        """Test default webhook values."""
        config = WebhookConfig()
        assert config.local_baseurl == "http://localhost:8888"
        assert config.external_baseurl is None

    def test_with_external_url(self):
        """Test webhook with external URL."""
        config = WebhookConfig(
            local_baseurl="http://127.0.0.1:9000",
            external_baseurl="https://webhook.example.com",
        )
        assert config.local_baseurl == "http://127.0.0.1:9000"
        assert config.external_baseurl == "https://webhook.example.com"


class TestEmailInterceptorConfig:
    """Test email interceptor configuration."""

    def test_default_values(self):
        """Test default email interceptor values."""
        config = EmailInterceptorConfig()
        assert config.worker_name == "regbot-emailparser"
        assert config.store_raw_email is True
        assert config.forward_unknown is True
        assert config.forward_email == ""
        assert config.environments == {}

    def test_with_environments(self):
        """Test email interceptor with environments."""
        environments = {
            "dev": {"webhook_url": "http://localhost:8888/webhook"},
            "prod": {"webhook_url": "https://prod.example.com/webhook"},
        }
        config = EmailInterceptorConfig(environments=environments)
        assert len(config.environments) == 2
        assert "dev" in config.environments
        assert "prod" in config.environments


class TestContinuousAutomationConfig:
    """Test continuous automation configuration."""

    def test_default_values(self):
        """Test default continuous automation values."""
        config = ContinuousAutomationConfig()
        assert config.min_interval_minutes == 30


class TestBrowserConfig:
    """Test browser configuration."""

    def test_default_browser_config(self):
        """Test default browser configuration."""
        config = BrowserConfig()
        assert config.headless is True
        assert config.timeout == 30000
        assert config.enable_randomization is True
        assert config.engines == ["chromium", "firefox"]

    def test_custom_browser_config(self):
        """Test custom browser configuration."""
        config = BrowserConfig(
            headless=False,
            timeout=60000,
            enable_randomization=False,
            engines=["webkit"],
        )
        assert config.headless is False
        assert config.timeout == 60000
        assert config.enable_randomization is False
        assert config.engines == ["webkit"]


class TestEmailConfig:
    """Test email configuration."""

    def test_default_email_config(self):
        """Test default email configuration."""
        config = EmailConfig()
        assert len(config.domains) > 0  # Should have default domains
        assert config.timeout == 60

    def test_custom_email_config(self):
        """Test custom email configuration."""
        domains = [
            EmailDomainConfig(domain="test1.com", weight=70),
            EmailDomainConfig(domain="test2.com", weight=30),
        ]
        config = EmailConfig(domains=domains, timeout=120)
        assert len(config.domains) == 2
        assert config.timeout == 120
        assert config.domains[0].domain == "test1.com"


class TestServiceConfig:
    """Test service configuration."""

    def test_default_service_config(self):
        """Test default service configuration."""
        config = ServiceConfig()
        assert config.enabled is True
        assert config.timeout == 120
        assert config.max_retries == 3

    def test_disabled_service_config(self):
        """Test disabled service configuration."""
        config = ServiceConfig(enabled=False, timeout=60, max_retries=1)
        assert config.enabled is False
        assert config.timeout == 60
        assert config.max_retries == 1


class TestConfig:
    """Test main configuration class."""

    def test_default_config(self):
        """Test default configuration creation."""
        config = Config()
        assert config.browser is not None
        assert config.email is not None
        assert config.webhook is not None
        assert config.services is not None

    def test_config_with_custom_values(self):
        """Test configuration with custom values."""
        browser_config = BrowserConfig(headless=False)
        email_config = EmailConfig(timeout=90)

        config = Config(browser=browser_config, email=email_config)
        assert config.browser.headless is False
        assert config.email.timeout == 90

    @pytest.mark.unit
    def test_load_config_from_dict(self):
        """Test loading configuration from dictionary."""
        config_dict = {
            "browser": {"headless": False, "timeout": 45000, "engines": ["firefox"]},
            "email": {
                "timeout": 90,
                "domains": [{"domain": "test.example.com", "weight": 100}],
            },
            "services": {
                "exaai": {"enabled": True, "timeout": 60},
                "assemblyai": {"enabled": False, "timeout": 30},
            },
        }

        config = Config.from_dict(config_dict)
        assert config.browser.headless is False
        assert config.browser.timeout == 45000
        assert config.browser.engines == ["firefox"]
        assert config.email.timeout == 90
        assert len(config.email.domains) == 1
        assert config.services["exaai"].enabled is True
        assert config.services["assemblyai"].enabled is False

    @pytest.mark.unit
    def test_load_config_from_yaml_string(self):
        """Test loading configuration from YAML string."""
        yaml_content = """
        browser:
          headless: true
          timeout: 30000
        email:
          timeout: 60
          domains:
            - domain: "test1.com"
              weight: 80
            - domain: "test2.com"
              weight: 20
        services:
          exaai:
            enabled: true
            timeout: 120
        """

        with patch("builtins.open", mock_open(read_data=yaml_content)):
            config = Config.from_file("mock_config.yaml")

        assert config.browser.headless is True
        assert config.email.timeout == 60
        assert len(config.email.domains) == 2
        assert config.email.domains[0].domain == "test1.com"
        assert config.email.domains[0].weight == 80
        assert config.services["exaai"].enabled is True

    @pytest.mark.unit
    def test_load_config_file_not_found(self):
        """Test handling of missing configuration file."""
        with patch("pathlib.Path.exists", return_value=False):
            with pytest.raises(FileNotFoundError):
                Config.from_file("nonexistent.yaml")

    @pytest.mark.unit
    def test_load_config_invalid_yaml(self):
        """Test handling of invalid YAML."""
        invalid_yaml = "invalid: yaml: content: ["

        with patch("builtins.open", mock_open(read_data=invalid_yaml)):
            with pytest.raises(yaml.YAMLError):
                Config.from_file("invalid.yaml")

    @pytest.mark.unit
    def test_config_validation_error(self):
        """Test configuration validation errors."""
        with pytest.raises(ValueError):
            # Invalid timeout (negative)
            BrowserConfig(timeout=-1000)

    def test_config_to_dict(self):
        """Test converting configuration to dictionary."""
        config = Config()
        config_dict = config.to_dict()

        assert isinstance(config_dict, dict)
        assert "browser" in config_dict
        assert "email" in config_dict
        assert "services" in config_dict
        assert isinstance(config_dict["browser"], dict)

    def test_config_merge(self):
        """Test merging configurations."""
        base_config = Config()
        override_config = {"browser": {"headless": False}, "email": {"timeout": 90}}

        merged_config = base_config.merge(override_config)
        assert merged_config.browser.headless is False
        assert merged_config.email.timeout == 90
        # Other values should remain from base config
        assert merged_config.browser.timeout == base_config.browser.timeout


@pytest.mark.unit
class TestConfigFileOperations:
    """Test configuration file operations."""

    def test_save_and_load_config_file(self):
        """Test saving and loading configuration file."""
        # Create a temporary config
        config = Config()
        config.browser.headless = False
        config.email.timeout = 90

        with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
            temp_path = Path(f.name)
            config.save_to_file(temp_path)

        try:
            # Load the config back
            loaded_config = Config.from_file(temp_path)
            assert loaded_config.browser.headless is False
            assert loaded_config.email.timeout == 90
        finally:
            # Clean up
            temp_path.unlink()

    def test_config_file_permissions(self):
        """Test configuration file permission handling."""
        with tempfile.NamedTemporaryFile(suffix=".yaml") as f:
            temp_path = Path(f.name)

            # Mock permission denied
            with patch(
                "builtins.open", side_effect=PermissionError("Permission denied")
            ):
                with pytest.raises(PermissionError):
                    Config.from_file(temp_path)


@pytest.mark.unit
class TestConfigEnvironmentVariables:
    """Test configuration environment variable overrides."""

    def test_config_from_env_vars(self):
        """Test loading configuration from environment variables."""
        env_vars = {
            "REGBOT_BROWSER_HEADLESS": "false",
            "REGBOT_BROWSER_TIMEOUT": "45000",
            "REGBOT_EMAIL_TIMEOUT": "90",
        }

        with patch.dict("os.environ", env_vars):
            config = Config.from_env()
            assert config.browser.headless is False
            assert config.browser.timeout == 45000
            assert config.email.timeout == 90

    def test_config_env_override_priority(self):
        """Test environment variable override priority."""
        # File config
        yaml_content = """
        browser:
          headless: true
          timeout: 30000
        """

        # Environment overrides
        env_vars = {
            "REGBOT_BROWSER_HEADLESS": "false",
            "REGBOT_BROWSER_TIMEOUT": "60000",
        }

        with patch("builtins.open", mock_open(read_data=yaml_content)):
            with patch.dict("os.environ", env_vars):
                config = Config.from_file_with_env_override("config.yaml")

        # Environment should override file
        assert config.browser.headless is False
        assert config.browser.timeout == 60000
