"""
Unit tests for domain selector functionality.

Tests domain selection logic and weighting without external dependencies.
"""

import pytest
from unittest.mock import Mock

from core.domain_selector import DomainSelector, EmailDomain


class TestEmailDomain:
    """Test EmailDomain model."""

    def test_email_domain_creation(self):
        """Test EmailDomain creation."""
        domain = EmailDomain(domain="test.com", weight=50, enabled=True)
        assert domain.domain == "test.com"
        assert domain.weight == 50
        assert domain.enabled is True

    def test_email_domain_defaults(self):
        """Test EmailDomain default values."""
        domain = EmailDomain(domain="test.com")
        assert domain.domain == "test.com"
        assert domain.weight == 100  # Default weight
        assert domain.enabled is True  # Default enabled

    def test_email_domain_disabled(self):
        """Test disabled EmailDomain."""
        domain = EmailDomain(domain="test.com", enabled=False)
        assert domain.enabled is False


class TestDomainSelector:
    """Test DomainSelector functionality."""

    def test_domain_selector_creation(self):
        """Test DomainSelector creation."""
        domains = [
            EmailDomain(domain="test1.com", weight=70),
            EmailDomain(domain="test2.com", weight=30),
        ]
        selector = DomainSelector(domains)
        assert len(selector.domains) == 2
        assert selector.randomizer is None

    def test_domain_selector_with_randomizer(self):
        """Test DomainSelector with custom randomizer."""
        domains = [EmailDomain(domain="test.com")]
        mock_randomizer = Mock()

        selector = DomainSelector(domains, randomizer=mock_randomizer)
        assert selector.randomizer == mock_randomizer

    def test_select_domain_single(self):
        """Test selecting from single domain."""
        domains = [EmailDomain(domain="only.com")]
        selector = DomainSelector(domains)

        selected = selector.select_domain()
        assert selected == "only.com"

    def test_select_domain_weighted(self):
        """Test weighted domain selection."""
        domains = [
            EmailDomain(domain="heavy.com", weight=90),
            EmailDomain(domain="light.com", weight=10),
        ]
        selector = DomainSelector(domains)

        # Test multiple selections
        selections = [selector.select_domain() for _ in range(100)]

        # All selections should be valid domains
        valid_domains = {"heavy.com", "light.com"}
        assert all(selection in valid_domains for selection in selections)

        # Heavy domain should appear more often (statistically)
        heavy_count = selections.count("heavy.com")
        light_count = selections.count("light.com")

        # With 90/10 weight, heavy should be much more common
        assert heavy_count > light_count

    def test_select_domain_enabled_only(self):
        """Test selection only picks enabled domains."""
        domains = [
            EmailDomain(domain="enabled.com", weight=50, enabled=True),
            EmailDomain(domain="disabled.com", weight=50, enabled=False),
        ]
        selector = DomainSelector(domains)

        # Should only select enabled domain
        selections = [selector.select_domain() for _ in range(50)]
        assert all(selection == "enabled.com" for selection in selections)

    def test_select_domain_equal_weights(self):
        """Test selection with equal weights."""
        domains = [
            EmailDomain(domain="equal1.com", weight=50),
            EmailDomain(domain="equal2.com", weight=50),
        ]
        selector = DomainSelector(domains)

        # Should get both domains with roughly equal frequency
        selections = [selector.select_domain() for _ in range(100)]

        count1 = selections.count("equal1.com")
        count2 = selections.count("equal2.com")

        # Should be roughly equal (within reasonable variance)
        assert 20 <= count1 <= 80  # Allow for randomness
        assert 20 <= count2 <= 80
        assert count1 + count2 == 100

    def test_select_domain_with_custom_randomizer(self):
        """Test domain selection with custom randomizer."""
        domains = [
            EmailDomain(domain="first.com", weight=70),
            EmailDomain(domain="second.com", weight=30),
        ]

        # Mock randomizer that always returns first choice
        mock_randomizer = Mock()
        mock_randomizer.choices.return_value = [domains[0]]

        selector = DomainSelector(domains, randomizer=mock_randomizer)
        selected = selector.select_domain()

        assert selected == "first.com"
        # Should have called randomizer with correct parameters
        mock_randomizer.choices.assert_called_once()

    def test_get_enabled_domains(self):
        """Test getting only enabled domains."""
        domains = [
            EmailDomain(domain="enabled1.com", enabled=True),
            EmailDomain(domain="disabled.com", enabled=False),
            EmailDomain(domain="enabled2.com", enabled=True),
        ]
        selector = DomainSelector(domains)

        enabled = selector.get_enabled_domains()
        assert len(enabled) == 2
        assert enabled[0].domain == "enabled1.com"
        assert enabled[1].domain == "enabled2.com"

    def test_get_total_weight(self):
        """Test calculating total weight of enabled domains."""
        domains = [
            EmailDomain(domain="domain1.com", weight=30, enabled=True),
            EmailDomain(domain="domain2.com", weight=20, enabled=True),
            EmailDomain(domain="disabled.com", weight=50, enabled=False),
        ]
        selector = DomainSelector(domains)

        total_weight = selector.get_total_weight()
        assert total_weight == 50  # Only enabled domains count

    def test_empty_domains_list(self):
        """Test handling of empty domains list."""
        selector = DomainSelector([])

        with pytest.raises((IndexError, ValueError)):
            selector.select_domain()

    def test_all_domains_disabled(self):
        """Test handling when all domains are disabled."""
        domains = [
            EmailDomain(domain="disabled1.com", enabled=False),
            EmailDomain(domain="disabled2.com", enabled=False),
        ]
        selector = DomainSelector(domains)

        with pytest.raises((IndexError, ValueError)):
            selector.select_domain()

    def test_zero_weight_domains(self):
        """Test handling of domains with zero weight."""
        domains = [
            EmailDomain(domain="zero.com", weight=0),
            EmailDomain(domain="normal.com", weight=100),
        ]
        selector = DomainSelector(domains)

        # Should only select the non-zero weight domain
        selections = [selector.select_domain() for _ in range(20)]
        assert all(selection == "normal.com" for selection in selections)


class TestDomainSelectorEdgeCases:
    """Test edge cases and error conditions."""

    def test_negative_weights(self):
        """Test handling of negative weights."""
        domains = [
            EmailDomain(domain="negative.com", weight=-10),
            EmailDomain(domain="positive.com", weight=100),
        ]

        # Should handle negative weights gracefully
        selector = DomainSelector(domains)

        # Should still work (negative weights treated as 0 or ignored)
        selected = selector.select_domain()
        assert selected in ["negative.com", "positive.com"]

    def test_very_large_weights(self):
        """Test handling of very large weights."""
        domains = [
            EmailDomain(domain="huge.com", weight=1000000),
            EmailDomain(domain="tiny.com", weight=1),
        ]
        selector = DomainSelector(domains)

        # Should still work without overflow
        selected = selector.select_domain()
        assert selected in ["huge.com", "tiny.com"]

    def test_duplicate_domain_names(self):
        """Test handling of duplicate domain names."""
        domains = [
            EmailDomain(domain="duplicate.com", weight=50),
            EmailDomain(domain="duplicate.com", weight=50),
        ]
        selector = DomainSelector(domains)

        # Should still work, will just select the duplicate name
        selected = selector.select_domain()
        assert selected == "duplicate.com"

    def test_domain_name_validation(self):
        """Test domain name format validation."""
        # Test various domain formats
        valid_domains = [
            "simple.com",
            "sub.domain.com",
            "test-domain.org",
            "123domain.net",
            "domain_with_underscore.co.uk",
        ]

        for domain_name in valid_domains:
            domain = EmailDomain(domain=domain_name)
            selector = DomainSelector([domain])
            selected = selector.select_domain()
            assert selected == domain_name


class TestDomainSelectorPerformance:
    """Test performance characteristics."""

    def test_selection_performance(self):
        """Test domain selection performance with many domains."""
        import time

        # Create many domains
        domains = [
            EmailDomain(domain=f"domain{i}.com", weight=i + 1) for i in range(1000)
        ]
        selector = DomainSelector(domains)

        start_time = time.time()

        # Perform many selections
        selections = [selector.select_domain() for _ in range(1000)]

        elapsed_time = time.time() - start_time

        # Should be fast (less than 1 second)
        assert elapsed_time < 1.0
        assert len(selections) == 1000

    def test_memory_usage_with_many_domains(self):
        """Test memory usage doesn't grow excessively."""
        # Create many domains
        domains = [
            EmailDomain(domain=f"test{i}.example.com", weight=10) for i in range(10000)
        ]

        selector = DomainSelector(domains)

        # Should handle large number of domains
        assert len(selector.domains) == 10000

        # Should still be able to select
        selected = selector.select_domain()
        assert selected.startswith("test")
        assert selected.endswith(".example.com")
