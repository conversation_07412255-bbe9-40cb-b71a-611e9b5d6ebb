"""
Unit tests for profile generation.

Tests profile generation logic, validation, and randomization without external dependencies.
"""

import pytest
import string
from unittest.mock import Mock, patch, MagicMock

from core.profile import ProfileGenerator


class TestProfileGenerator:
    """Test profile generator functionality."""

    def test_profile_generator_creation(self):
        """Test profile generator creation."""
        generator = ProfileGenerator("test.example.com")
        assert generator.domain == "test.example.com"
        assert generator.faker is not None
        assert generator.randomizer is None

    def test_profile_generator_with_randomizer(self):
        """Test profile generator with custom randomizer."""
        mock_randomizer = Mock()
        generator = ProfileGenerator("test.example.com", randomizer=mock_randomizer)
        assert generator.randomizer == mock_randomizer

    def test_generate_profile_structure(self):
        """Test generated profile has correct structure."""
        generator = ProfileGenerator("test.example.com")
        profile = generator.generate_profile()

        # Check all required fields exist
        required_fields = [
            "first_name",
            "last_name",
            "username",
            "email",
            "password",
            "company",
            "job_title",
            "phone",
            "country",
            "city",
            "address",
            "website",
            "bio",
        ]

        for field in required_fields:
            assert field in profile, f"Missing field: {field}"
            assert profile[field] is not None, f"Field {field} is None"
            assert isinstance(profile[field], str), f"Field {field} is not string"
            assert len(profile[field]) > 0, f"Field {field} is empty"

    def test_generate_profile_email_domain(self):
        """Test generated profile uses correct email domain."""
        domain = "custom.test.com"
        generator = ProfileGenerator(domain)
        profile = generator.generate_profile()

        assert profile["email"].endswith(f"@{domain}")

    def test_generate_profile_username_format(self):
        """Test username format is correct."""
        generator = ProfileGenerator("test.example.com")
        profile = generator.generate_profile()

        username = profile["username"]
        email = profile["email"]

        # Username should be in email
        assert username in email

        # Username should be firstname + digits
        assert any(char.isdigit() for char in username), (
            "Username should contain digits"
        )
        assert any(char.isalpha() for char in username), (
            "Username should contain letters"
        )

        # Should be reasonable length
        assert 4 <= len(username) <= 20

    def test_generate_profile_password_security(self):
        """Test generated password meets security requirements."""
        generator = ProfileGenerator("test.example.com")
        profile = generator.generate_profile()

        password = profile["password"]

        # Check password length
        assert len(password) >= 12, "Password should be at least 12 characters"

        # Check password complexity
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in string.punctuation for c in password)

        assert has_upper, "Password should have uppercase letters"
        assert has_lower, "Password should have lowercase letters"
        assert has_digit, "Password should have digits"
        assert has_special, "Password should have special characters"

    def test_generate_profile_name_capitalization(self):
        """Test names are properly capitalized."""
        generator = ProfileGenerator("test.example.com")
        profile = generator.generate_profile()

        first_name = profile["first_name"]
        last_name = profile["last_name"]

        # Names should be capitalized
        assert first_name[0].isupper(), "First name should be capitalized"
        assert last_name[0].isupper(), "Last name should be capitalized"

        # Rest should be lowercase (for simple names)
        if len(first_name) > 1:
            assert first_name[1:].islower() or first_name[1:].isalpha()

    def test_generate_profile_with_custom_randomizer(self):
        """Test profile generation with custom randomizer."""
        mock_randomizer = Mock()
        mock_randomizer.choices.return_value = ["1", "2", "3"]

        generator = ProfileGenerator("test.example.com", randomizer=mock_randomizer)

        with patch.object(generator.faker, "first_name", return_value="john"):
            profile = generator.generate_profile()

        # Should use randomizer for digits
        mock_randomizer.choices.assert_called_once_with(string.digits, k=3)
        assert profile["username"] == "john123"
        assert profile["email"] == "<EMAIL>"

    def test_generate_profile_without_randomizer_fallback(self):
        """Test profile generation falls back to standard random when no randomizer."""
        generator = ProfileGenerator("test.example.com")  # No randomizer

        with patch("random.choices", return_value=["4", "5", "6"]) as mock_random:
            with patch.object(generator.faker, "first_name", return_value="jane"):
                profile = generator.generate_profile()

        # Should use random.choices as fallback
        mock_random.assert_called_once_with(string.digits, k=3)
        assert profile["username"] == "jane456"

    def test_generate_multiple_profiles_unique(self):
        """Test multiple generated profiles are unique."""
        generator = ProfileGenerator("test.example.com")

        profiles = [generator.generate_profile() for _ in range(10)]

        # Check email uniqueness
        emails = [p["email"] for p in profiles]
        assert len(set(emails)) == len(emails), "All emails should be unique"

        # Check username uniqueness
        usernames = [p["username"] for p in profiles]
        assert len(set(usernames)) == len(usernames), "All usernames should be unique"

    def test_generate_password_method(self):
        """Test private password generation method."""
        generator = ProfileGenerator("test.example.com")

        # Test default length
        password = generator._generate_password()
        assert len(password) >= 12

        # Test custom length
        password_custom = generator._generate_password(length=20)
        assert len(password_custom) >= 20

    def test_generate_password_different_each_time(self):
        """Test password generation produces different passwords."""
        generator = ProfileGenerator("test.example.com")

        passwords = [generator._generate_password() for _ in range(10)]

        # All passwords should be unique
        assert len(set(passwords)) == len(passwords)

    @pytest.mark.unit
    def test_profile_fields_reasonable_length(self):
        """Test profile fields have reasonable lengths."""
        generator = ProfileGenerator("test.example.com")
        profile = generator.generate_profile()

        # Test field length constraints
        assert len(profile["first_name"]) <= 50
        assert len(profile["last_name"]) <= 50
        assert len(profile["email"]) <= 100
        assert len(profile["username"]) <= 50
        assert len(profile["password"]) <= 100
        assert len(profile["bio"]) <= 200  # Explicitly set in generation

    @pytest.mark.unit
    def test_profile_email_format_valid(self):
        """Test generated email format is valid."""
        generator = ProfileGenerator("test.example.com")
        profile = generator.generate_profile()

        email = profile["email"]

        # Basic email format validation
        assert "@" in email
        assert email.count("@") == 1

        username_part, domain_part = email.split("@")
        assert len(username_part) > 0
        assert len(domain_part) > 0
        assert "." in domain_part

    def test_profile_website_url_format(self):
        """Test generated website URL format."""
        generator = ProfileGenerator("test.example.com")
        profile = generator.generate_profile()

        website = profile["website"]

        # Should be a URL-like format
        assert website.startswith(("http://", "https://"))

    def test_profile_phone_number_format(self):
        """Test phone number is generated."""
        generator = ProfileGenerator("test.example.com")
        profile = generator.generate_profile()

        phone = profile["phone"]

        # Should contain some digits
        assert any(char.isdigit() for char in phone)

        # Should be reasonable length
        assert 10 <= len(phone) <= 20


class TestProfileGeneratorEdgeCases:
    """Test edge cases and error conditions."""

    def test_empty_domain(self):
        """Test profile generation with empty domain."""
        generator = ProfileGenerator("")
        profile = generator.generate_profile()

        # Should still generate profile, email will just have empty domain
        assert "@" in profile["email"]

    def test_very_long_domain(self):
        """Test profile generation with very long domain."""
        long_domain = "very.long.domain.name.example.com" * 5  # Very long
        generator = ProfileGenerator(long_domain)
        profile = generator.generate_profile()

        # Should still work
        assert profile["email"].endswith(f"@{long_domain}")

    def test_domain_with_special_chars(self):
        """Test domain with special characters."""
        domain = "test-domain.example_site.com"
        generator = ProfileGenerator(domain)
        profile = generator.generate_profile()

        assert profile["email"].endswith(f"@{domain}")

    def test_faker_localization(self):
        """Test faker localization doesn't break profile generation."""
        # Test with different locale
        generator = ProfileGenerator("test.com")
        generator.faker = generator.faker.__class__("es_ES")  # Spanish locale

        profile = generator.generate_profile()

        # Should still generate all required fields
        assert all(
            key in profile
            for key in ["first_name", "last_name", "email", "username", "password"]
        )

    def test_randomizer_choice_method_error(self):
        """Test handling of randomizer choice method errors."""
        mock_randomizer = Mock()
        mock_randomizer.choices.side_effect = Exception("Randomizer error")

        generator = ProfileGenerator("test.com", randomizer=mock_randomizer)

        # Should fall back to standard random
        with patch("random.choices", return_value=["7", "8", "9"]):
            profile = generator.generate_profile()

        # Should still generate profile successfully
        assert "email" in profile
        assert "username" in profile


class TestProfileGeneratorPerformance:
    """Test performance characteristics."""

    def test_profile_generation_performance(self):
        """Test profile generation is reasonably fast."""
        import time

        generator = ProfileGenerator("test.example.com")

        start_time = time.time()

        # Generate multiple profiles
        profiles = [generator.generate_profile() for _ in range(100)]

        elapsed_time = time.time() - start_time

        # Should be fast (less than 1 second for 100 profiles)
        assert elapsed_time < 1.0
        assert len(profiles) == 100

    def test_memory_usage_reasonable(self):
        """Test profile generation doesn't use excessive memory."""
        generator = ProfileGenerator("test.example.com")

        # Generate many profiles
        profiles = [generator.generate_profile() for _ in range(1000)]

        # Check profile structure is reasonable
        for profile in profiles[:10]:  # Check first 10
            assert isinstance(profile, dict)
            assert len(profile) < 20  # Reasonable number of fields

            # Check no extremely long strings
            for value in profile.values():
                if isinstance(value, str):
                    assert len(value) < 1000  # No extremely long values
