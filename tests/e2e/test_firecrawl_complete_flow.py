#!/usr/bin/env python3
"""
BitBrowser Local API test for Firecrawl signup.
Tests if BitBrows<PERSON> can bypass antibot detection where <PERSON><PERSON> failed.
"""

import asyncio
import logging
import sys
import time
import requests
import json
import re
import random
from pathlib import Path
from datetime import datetime
from typing import Optional

# Add project root to path
sys.path.append(str(Path(__file__).parent))


# BitBrowser API functions (inline to avoid import issues)
def openBrowser(browser_id):
    """Open BitBrowser instance."""
    url = "http://127.0.0.1:54345"
    headers = {"Content-Type": "application/json"}
    json_data = {"id": f"{browser_id}"}
    res = requests.post(
        f"{url}/browser/open", data=json.dumps(json_data), headers=headers
    ).json()
    return res


def closeBrowser(browser_id):
    """Close BitBrowser instance."""
    url = "http://127.0.0.1:54345"
    headers = {"Content-Type": "application/json"}
    json_data = {"id": f"{browser_id}"}
    requests.post(
        f"{url}/browser/close", data=json.dumps(json_data), headers=headers
    ).json()


from playwright.async_api import async_playwright
from core.profile import ProfileGenerator
from core.email_client_factory import EmailClientFactory
from core.utils import generate_strong_password
from core.domain_selector import DomainSelector, EmailDomain
from core.randomizer import create_randomizer, RandomizationProfile
from services.firecrawl import FirecrawlService

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Configuration
BROWSER_ID = "08b818f4e0a84f0a86a08fa404f41662"
FIRECRAWL_HOMEPAGE_URL = "https://www.firecrawl.dev"
FIRECRAWL_DOMAINS = ["firecrawl.dev", "service.firecrawl.dev"]


class BitBrowserFirecrawlTest:
    """Test Firecrawl signup using BitBrowser antidetect browser."""

    def __init__(self):
        self.randomizer = create_randomizer(RandomizationProfile.NORMAL, enabled=True)
        self.email_client = EmailClientFactory.create_client()

        # Use actual domains from config
        test_domains = [
            EmailDomain(domain="ai.whatisinitfor.me", weight=50, enabled=True),
            EmailDomain(domain="sg.164136.xyz", weight=30, enabled=True),
            EmailDomain(domain="iad.164136.xyz", weight=20, enabled=True),
        ]
        self.domain_selector = DomainSelector(test_domains, randomizer=self.randomizer)
        self.selected_domain = self.domain_selector.select_domain()

        logger.info(f"Selected email domain: {self.selected_domain}")

        self.profile_generator = ProfileGenerator(
            self.selected_domain, randomizer=self.randomizer
        )

    async def take_screenshot(self, page, name: str):
        """Take a debug screenshot."""
        try:
            screenshot_dir = (
                Path("debug")
                / "bitbrowser_test"
                / datetime.now().strftime("%Y%m%d_%H%M%S")
            )
            screenshot_dir.mkdir(parents=True, exist_ok=True)
            screenshot_path = screenshot_dir / f"{name}.png"
            await page.screenshot(path=str(screenshot_path))
            logger.info(f"📸 Screenshot saved: {screenshot_path}")
        except Exception as e:
            logger.warning(f"Failed to save screenshot: {e}")

    async def perform_signup(self, page, profile: dict, password: str):
        """Perform the signup process on Firecrawl or detect existing login."""
        logger.info("🌐 Navigating to Firecrawl homepage")
        await page.goto(FIRECRAWL_HOMEPAGE_URL)
        await asyncio.sleep(3)  # Wait for page load
        await self.take_screenshot(page, "01_homepage")

        # CRITICAL: Check if we're already logged in
        logger.info("🔍 Checking if already logged in...")
        try:
            already_logged_in_indicators = [
                'button:has-text("Dashboard")',
                'a:has-text("Dashboard")',
                'text="Sign out"',
                'button:has-text("Sign out")',
                '[href*="/dashboard"]',
            ]

            already_logged_in = False
            for indicator in already_logged_in_indicators:
                try:
                    element = page.locator(indicator).first
                    if await element.is_visible():
                        logger.info(
                            f"✅ ALREADY LOGGED IN! Found indicator: {indicator}"
                        )
                        already_logged_in = True
                        break
                except:
                    continue

            if already_logged_in:
                logger.info(
                    "🎉 SESSION PERSISTENCE SUCCESS: BitBrowser maintained login session!"
                )
                logger.info("⏭️ Skipping signup process and going directly to dashboard")

                # Click Dashboard button to go to dashboard
                try:
                    dashboard_button = page.locator(
                        'button:has-text("Dashboard"), a:has-text("Dashboard")'
                    ).first
                    await dashboard_button.click()
                    logger.info("✅ Clicked Dashboard button")
                    await asyncio.sleep(5)
                    await self.take_screenshot(page, "02_existing_dashboard")
                    return True
                except Exception as e:
                    logger.warning(f"Could not click Dashboard button: {e}")
                    # Try direct navigation
                    await page.goto("https://www.firecrawl.dev/dashboard")
                    await asyncio.sleep(5)
                    await self.take_screenshot(page, "02_dashboard_direct")
                    return True

        except Exception as e:
            logger.warning(f"Error checking login status: {e}")

        # If not logged in, proceed with signup
        logger.info("🔍 Not logged in, proceeding with signup process")
        logger.info("🔍 Looking for 'Sign Up' button in navigation")
        try:
            # Find and click the "Sign Up" button in navigation
            signup_nav_selectors = [
                'button:has-text("Sign Up")',
                'a:has-text("Sign Up")',
                '[href*="/signup"]',
                '[href*="/auth"]',
                'nav button:has-text("Sign Up")',
                'header button:has-text("Sign Up")',
            ]

            button_found = False
            for selector in signup_nav_selectors:
                try:
                    signup_button = page.locator(selector).first
                    if await signup_button.is_visible():
                        await signup_button.click()
                        logger.info(
                            f"✅ Clicked Sign Up button with selector: {selector}"
                        )
                        button_found = True
                        break
                except Exception as e:
                    logger.debug(f"Sign Up button selector {selector} failed: {e}")
                    continue

            if not button_found:
                logger.error("❌ Could not find 'Sign Up' button in navigation")
                await self.take_screenshot(page, "error_no_signup_button")
                return False

            # Wait for signup page to load
            await asyncio.sleep(5)
            await self.take_screenshot(page, "03_signup_page")

        except Exception as e:
            logger.error(f"❌ Error clicking Sign Up button: {e}")
            await self.take_screenshot(page, "error_signup_button")
            return False

        logger.info(f"📧 Filling signup form with email: {profile['email']}")

        # Find and fill email input
        try:
            email_input = await page.wait_for_selector(
                'input[type="email"]', timeout=10000
            )
            await email_input.fill(profile["email"])
            await self.randomizer.user_action_delay()

            # Find and fill password input
            password_input = await page.wait_for_selector(
                'input[type="password"]', timeout=5000
            )
            await password_input.fill(password)
            await self.randomizer.user_action_delay()

            logger.info("✅ Form fields filled successfully")

        except Exception as e:
            logger.error(f"❌ Failed to fill form fields: {e}")
            await self.take_screenshot(page, "error_form_fill")
            return False

        await self.take_screenshot(page, "04_form_filled")

        # Find and click signup button
        try:
            # Wait for signup button to be enabled
            await page.wait_for_function(
                """() => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    const signupButton = buttons.find(btn => btn.textContent && btn.textContent.includes('Sign up'));
                    return signupButton && !signupButton.disabled;
                }""",
                timeout=10000,
            )

            # Click the signup button
            signup_button = page.locator('button:has-text("Sign up")').first
            await signup_button.click()
            logger.info("🚀 Signup button clicked")

            # Wait for form submission response
            await asyncio.sleep(5)
            await self.take_screenshot(page, "05_after_signup")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to submit signup form: {e}")
            await self.take_screenshot(page, "error_signup_submit")
            return False

    async def wait_for_verification_email(self, email: str) -> Optional[str]:
        """Wait for and retrieve verification email."""
        logger.info(f"📬 Waiting for verification email for {email}")

        for attempt in range(12):  # Wait up to 60 seconds
            await asyncio.sleep(5)

            try:
                raw_content = await self.email_client.get_latest_email_content(
                    "firecrawl", email
                )
                if raw_content:
                    logger.info(
                        f"✅ Verification email received after {(attempt + 1) * 5} seconds"
                    )

                    # Debug: Save full email content to file for inspection
                    debug_dir = (
                        Path("debug")
                        / "bitbrowser_test"
                        / datetime.now().strftime("%Y%m%d_%H%M%S")
                    )
                    debug_dir.mkdir(parents=True, exist_ok=True)
                    email_file = debug_dir / "full_email_content.txt"
                    with open(email_file, "w", encoding="utf-8") as f:
                        f.write(raw_content)
                    logger.info(f"📧 Full email content saved to: {email_file}")

                    return raw_content

                logger.info(f"⏳ Waiting for email... ({(attempt + 1) * 5}s)")

            except Exception as e:
                logger.warning(f"Error checking email: {e}")

        logger.error("❌ Verification email not received after 60 seconds")
        return None

    async def handle_verification(self, page, email_content: str):
        """Handle email verification process."""
        logger.info("🔗 Parsing verification link from email")

        # Debug: Log more of the email content to find the link
        logger.info(f"📧 Full email content length: {len(email_content)} characters")

        # Look for href= pattern which contains the complete URL

        # Method that works: Look for href=3D" pattern and decode it
        href_pattern = r'href=3D"([^"]+)"'
        href_matches = re.findall(href_pattern, email_content)
        logger.info(f"🔍 href= matches found: {len(href_matches)}")

        verification_link = None
        for match in href_matches:
            if "firecrawl.dev/auth/v1/verify" in match:
                # Decode the quoted-printable URL
                decoded_url = match.replace("=3D", "=").replace("&amp;", "&")
                # Remove line breaks (quoted-printable line continuation)
                clean_url = re.sub(r"=\s*\n", "", decoded_url)
                verification_link = clean_url
                logger.info(
                    f"🎯 Successfully extracted verification URL: {verification_link}"
                )
                break
        if not verification_link:
            logger.error("❌ Could not parse verification link")
            return False

        logger.info(f"🔗 Found verification link: {verification_link}")

        # Navigate to verification link
        await page.goto(verification_link)
        await asyncio.sleep(5)  # Wait longer for redirect
        await self.take_screenshot(page, "05_verification_page")

        logger.info("✅ Verification link opened")
        return True

    async def complete_setup_wizard(self, page):
        """Complete the 4-step setup wizard with step-specific handling."""
        logger.info("🧙 Starting setup wizard completion")

        # Wait for wizard to load
        await asyncio.sleep(10)
        await self.take_screenshot(page, "06_wizard_start")

        try:
            # Handle the wizard step by step
            for step in range(1, 5):  # Steps 1-4
                logger.info(f"📋 Processing Wizard Step {step}")

                # Wait for the step to be visible
                await asyncio.sleep(3)
                await self.take_screenshot(page, f"07_wizard_step{step}")

                if step == 1:
                    # Step 1: Bonus credits - just click Next
                    await self._handle_wizard_step1(page)
                elif step == 2:
                    # Step 2: Questions - select first option and continue
                    await self._handle_wizard_step2(page)
                elif step == 3:
                    # Step 3: More questions - select first option and continue
                    await self._handle_wizard_step3(page)
                elif step == 4:
                    # Step 4: CRITICAL - Terms of Service
                    return await self._handle_wizard_step4_terms(page)

            return True

        except Exception as e:
            logger.error(f"❌ Failed to complete setup wizard: {e}")
            await self.take_screenshot(page, "error_wizard")
            return False

    async def _handle_wizard_step1(self, page):
        """Handle Step 1: Bonus credits."""
        logger.info("📋 Wizard Step 1: Bonus credits")

        # Close any "What's New" popup first
        try:
            close_popup = page.locator(
                '[data-testid="close"], .close, button:has-text("×")'
            ).first
            await close_popup.click(timeout=3000)
            logger.info("✅ Closed What's New popup")
            await asyncio.sleep(1)
        except:
            logger.info("ℹ️ No popup to close or already closed")

        # Click Next button to proceed
        next_button = page.locator('button:has-text("Next")').first
        await next_button.click()
        logger.info("✅ Clicked Next on Step 1")
        await asyncio.sleep(2)

    async def _handle_wizard_step2(self, page):
        """Handle Step 2: Questions - Auto-advances after selection."""
        logger.info("📋 Wizard Step 2: Selecting random option (1-3)")

        # Select random radio button option (1-3) if available
        try:
            option_buttons = page.locator('button[role="radio"]')
            button_count = await option_buttons.count()
            logger.info(f"Found {button_count} radio button options in Step 2")

            if button_count > 0:
                # Choose random option between 1-3 (or max available)
                max_choice = min(3, button_count)
                random_choice = random.randint(0, max_choice - 1)

                selected_button = option_buttons.nth(random_choice)
                await selected_button.click()
                logger.info(
                    f"✅ Selected random option {random_choice + 1} of {button_count} in Step 2 - will auto-advance"
                )
            else:
                logger.info("ℹ️ No radio buttons found in Step 2")
        except Exception as e:
            logger.warning(f"Error selecting option in Step 2: {e}")

        # No need to click Next - it auto-advances after selection
        await asyncio.sleep(3)  # Wait for auto-advance

    async def _handle_wizard_step3(self, page):
        """Handle Step 3: More questions - Auto-advances after selection."""
        logger.info("📋 Wizard Step 3: Selecting random option (1-3)")

        # Select random radio button option (1-3) if available
        try:
            option_buttons = page.locator('button[role="radio"]')
            button_count = await option_buttons.count()
            logger.info(f"Found {button_count} radio button options in Step 3")

            if button_count > 0:
                # Choose random option between 1-3 (or max available)
                max_choice = min(3, button_count)
                random_choice = random.randint(0, max_choice - 1)

                selected_button = option_buttons.nth(random_choice)
                await selected_button.click()
                logger.info(
                    f"✅ Selected random option {random_choice + 1} of {button_count} in Step 3 - will auto-advance"
                )
            else:
                logger.info("ℹ️ No radio buttons found in Step 3")
        except Exception as e:
            logger.warning(f"Error selecting option in Step 3: {e}")

        # No need to click Next - it auto-advances after selection
        await asyncio.sleep(3)  # Wait for auto-advance

    async def _handle_wizard_step4_terms(self, page):
        """Handle Step 4: CRITICAL Terms of Service agreement."""
        logger.info("📋 Wizard Step 4: CRITICAL - Terms of Service")

        # Wait for Terms of Service content to load
        await asyncio.sleep(3)
        await self.take_screenshot(page, "08_wizard_step4_before_terms")

        # Wait for the terms content to be visible
        try:
            await page.wait_for_selector('text="Terms of Service"', timeout=10000)
            logger.info("✅ Terms of Service content loaded")
        except:
            logger.warning("⚠️ Terms of Service content not found, continuing anyway")

        # No need to close What's New popup - focus on checkbox clicking

        # ENHANCED: Find and check the "I agree to Terms of Service" checkbox with multiple strategies
        terms_agreement_found = await self._check_terms_checkbox_enhanced(page)

        if not terms_agreement_found:
            logger.error(
                "❌ CRITICAL: Could not find or check Terms of Service checkbox!"
            )
            await self.take_screenshot(page, "error_terms_checkbox_not_found")
            # Still try to continue, but this will likely fail

        # Verify checkbox is checked
        await self._verify_checkbox_state(page)

        await asyncio.sleep(2)
        await self.take_screenshot(page, "09_wizard_step4_after_terms")

    async def _close_blocking_popups(self, page):
        """Close any blocking popups that might interfere with checkbox interactions."""
        logger.info("🔍 Closing 'What's New' popup that blocks checkbox interactions")
        try:
            popup_close_selectors = [
                '[data-testid="close"]',
                'button:has-text("×")',
                ".close",
                '[aria-label="Close"]',
                'button[title="Close"]',
                # Specific to What's New popup
                'div:has-text("What\'s New") button',
                'div:has-text("Firecrawl v") + button',
                # Additional popup selectors
                ".modal-close",
                ".popup-close",
                '[data-dismiss="modal"]',
                'button[class*="close"]',
            ]

            popup_closed = False
            for selector in popup_close_selectors:
                try:
                    close_button = page.locator(selector).first
                    if await close_button.is_visible():
                        await close_button.click()
                        logger.info(
                            f"✅ CRITICAL: Closed What's New popup using: {selector}"
                        )
                        popup_closed = True
                        await asyncio.sleep(2)  # Wait for popup to fully close
                        break
                except Exception as e:
                    logger.debug(f"Popup close selector {selector} failed: {e}")
                    continue

            if popup_closed:
                await self.take_screenshot(page, "08b_popup_closed")

        except Exception as e:
            logger.warning(f"Error closing popup: {e}")

    async def _check_terms_checkbox_enhanced(self, page):
        """Enhanced checkbox checking with multiple strategies for JavaScript-controlled checkboxes."""
        logger.info(
            "🎯 ENHANCED: Attempting to check Terms of Service checkbox with multiple strategies"
        )

        # Strategy 1: Try mouse coordinate-based clicking (most reliable for custom checkboxes)
        terms_found = await self._try_mouse_coordinate_click(page)
        if terms_found:
            return True

        # Strategy 2: Try standard Playwright checkbox methods
        terms_found = await self._try_standard_checkbox_methods(page)
        if terms_found:
            return True

        # Strategy 3: Try force clicking approaches
        terms_found = await self._try_force_click_methods(page)
        if terms_found:
            return True

        # Strategy 4: Try JavaScript evaluation (programmatic)
        terms_found = await self._try_javascript_checkbox_methods(page)
        if terms_found:
            return True

        # Strategy 5: Try clicking labels and parent elements
        terms_found = await self._try_label_click_methods(page)
        if terms_found:
            return True

        logger.error("❌ All checkbox strategies failed")
        return False

    async def _try_mouse_coordinate_click(self, page):
        """Try mouse coordinate-based clicking for custom checkboxes."""
        logger.info("🔍 Strategy 1: Enhanced checkbox detection and clicking")

        # Method 1: Look for ARIA role checkboxes (modern custom checkboxes)
        try:
            logger.info("🔍 Method 1A: Looking for ARIA role checkboxes")
            aria_checkboxes = page.locator('[role="checkbox"]')
            checkbox_count = await aria_checkboxes.count()
            logger.info(f"Found {checkbox_count} ARIA checkboxes")

            if checkbox_count > 0:
                first_checkbox = aria_checkboxes.first
                await first_checkbox.click(force=True)
                logger.info("✅ STRATEGY 1A: Successfully clicked ARIA checkbox")
                await asyncio.sleep(1)
                return True

        except Exception as e:
            logger.debug(f"ARIA checkbox click failed: {e}")

        # Method 1B: Look for nested checkboxes in custom components
        try:
            logger.info(
                "🔍 Method 1B: Looking for nested checkboxes in custom components"
            )

            # Look for common custom checkbox patterns
            custom_patterns = [
                '*:has-text("Terms") [role="checkbox"]',
                '*:has-text("I agree") [role="checkbox"]',
                'div:has-text("Terms") input[type="checkbox"]',
                'label:has-text("Terms") input[type="checkbox"]',
                '*[class*="checkbox"]:has-text("Terms")',
                '*[class*="check"]:has-text("Terms")',
            ]

            for pattern in custom_patterns:
                try:
                    elements = page.locator(pattern)
                    if await elements.count() > 0:
                        await elements.first.click(force=True)
                        logger.info(
                            f"✅ STRATEGY 1B: Successfully clicked using pattern: {pattern}"
                        )
                        await asyncio.sleep(1)
                        return True
                except:
                    continue

        except Exception as e:
            logger.debug(f"Nested checkbox search failed: {e}")

        # Method 1C: JavaScript-based comprehensive checkbox detection
        try:
            logger.info("🔍 Method 1C: JavaScript comprehensive checkbox detection")

            result = await page.evaluate("""
                () => {
                    console.log('Starting comprehensive checkbox search...');

                    // Strategy 1: Look for hidden input checkboxes and click their labels
                    const hiddenInputs = document.querySelectorAll('input[type="checkbox"]');
                    console.log('Found', hiddenInputs.length, 'input checkboxes');

                    for (let input of hiddenInputs) {
                        // Find associated label
                        let label = input.closest('label') ||
                                   document.querySelector(`label[for="${input.id}"]`) ||
                                   input.parentElement;

                        if (label) {
                            console.log('Clicking label for hidden checkbox');
                            label.click();
                            return { success: true, method: 'hidden-input-label', element: label.tagName };
                        }
                    }

                    // Strategy 2: Look for ARIA checkboxes
                    const ariaCheckboxes = document.querySelectorAll('[role="checkbox"]');
                    console.log('Found', ariaCheckboxes.length, 'ARIA checkboxes');

                    for (let checkbox of ariaCheckboxes) {
                        if (checkbox.offsetParent !== null) { // visible
                            console.log('Clicking ARIA checkbox');
                            checkbox.click();
                            return { success: true, method: 'aria-checkbox', element: checkbox.tagName };
                        }
                    }

                    // Strategy 3: Look for elements with checkbox-like classes near Terms text
                    const termsElements = Array.from(document.querySelectorAll('*')).filter(el =>
                        el.textContent && el.textContent.includes('Terms')
                    );
                    console.log('Found', termsElements.length, 'elements with Terms text');

                    for (let termsEl of termsElements) {
                        // Look for clickable elements in the same container
                        const container = termsEl.closest('div, label, span') || termsEl;
                        const clickables = container.querySelectorAll(
                            '[role="checkbox"], input[type="checkbox"], ' +
                            '*[class*="checkbox"], *[class*="check"], ' +
                            'button, [onclick], [data-testid*="check"]'
                        );

                        for (let clickable of clickables) {
                            if (clickable.offsetParent !== null) {
                                console.log('Clicking checkbox near Terms text');
                                clickable.click();
                                return { success: true, method: 'terms-nearby', element: clickable.tagName };
                            }
                        }
                    }

                    // Strategy 4: Brute force - click anything that looks like a checkbox
                    const allCheckboxLike = document.querySelectorAll(
                        '*[class*="checkbox"], *[class*="check"], *[data-testid*="check"], ' +
                        '*[id*="checkbox"], *[id*="check"], *[id*="terms"], *[id*="agree"]'
                    );
                    console.log('Found', allCheckboxLike.length, 'checkbox-like elements');

                    for (let element of allCheckboxLike) {
                        if (element.offsetParent !== null) {
                            console.log('Clicking checkbox-like element');
                            element.click();
                            return { success: true, method: 'checkbox-like', element: element.tagName };
                        }
                    }

                    return { success: false, message: 'No clickable checkbox found' };
                }
            """)

            if result.get("success"):
                logger.info(
                    f"✅ STRATEGY 1C: JavaScript found and clicked checkbox: {result}"
                )
                await asyncio.sleep(1)
                return True
            else:
                logger.warning(
                    f"⚠️ STRATEGY 1C: JavaScript search failed: {result.get('message')}"
                )

        except Exception as e:
            logger.debug(f"JavaScript comprehensive search failed: {e}")

        # Method 1D: Coordinate-based clicking as last resort
        try:
            logger.info("🔍 Method 1D: Coordinate-based clicking near Terms text")

            terms_elements = page.locator(
                '*:has-text("Terms of Service"), *:has-text("I agree")'
            )
            if await terms_elements.count() > 0:
                first_element = terms_elements.first
                bbox = await first_element.bounding_box()
                if bbox:
                    # Try multiple click positions
                    click_positions = [
                        (bbox["x"] + 20, bbox["y"] + bbox["height"] / 2),  # Left side
                        (
                            bbox["x"] - 30,
                            bbox["y"] + bbox["height"] / 2,
                        ),  # Further left
                        (bbox["x"] + 10, bbox["y"] + bbox["height"] / 2),  # Close left
                    ]

                    for i, (click_x, click_y) in enumerate(click_positions):
                        logger.info(
                            f"🎯 Trying click position {i + 1}: ({click_x}, {click_y})"
                        )
                        await page.mouse.click(click_x, click_y)
                        await asyncio.sleep(0.5)

                    logger.info("✅ STRATEGY 1D: Completed coordinate-based clicking")
                    return True

        except Exception as e:
            logger.debug(f"Coordinate-based clicking failed: {e}")

        return False

    async def _try_standard_checkbox_methods(self, page):
        """Try standard Playwright checkbox interaction methods."""
        logger.info("🔍 Strategy 2: Standard Playwright checkbox methods")

        try:
            # Method 2A: Look for visible checkboxes first
            visible_checkboxes = page.locator('input[type="checkbox"]:visible')
            checkbox_count = await visible_checkboxes.count()
            logger.info(f"Found {checkbox_count} visible checkboxes")

            if checkbox_count > 0:
                first_checkbox = visible_checkboxes.first
                await first_checkbox.set_checked(True, force=True)
                logger.info(
                    "✅ STRATEGY 2A: Successfully checked visible checkbox using setChecked"
                )
                return True

        except Exception as e:
            logger.debug(f"Visible checkbox method failed: {e}")

        try:
            # Method 2B: Try all checkboxes (including hidden) with force
            all_checkboxes = page.locator('input[type="checkbox"]')
            checkbox_count = await all_checkboxes.count()
            logger.info(f"Found {checkbox_count} total checkboxes (including hidden)")

            if checkbox_count > 0:
                first_checkbox = all_checkboxes.first
                await first_checkbox.check(force=True)
                logger.info(
                    "✅ STRATEGY 2B: Successfully checked checkbox using check() with force"
                )
                return True

        except Exception as e:
            logger.debug(f"Force check method failed: {e}")

        try:
            # Method 2C: Use dispatchEvent on checkboxes (from research)
            checkboxes = page.locator('input[type="checkbox"]')
            if await checkboxes.count() > 0:
                first_checkbox = checkboxes.first
                await first_checkbox.dispatch_event("click")
                logger.info(
                    "✅ STRATEGY 2C: Successfully dispatched click event on checkbox"
                )
                return True

        except Exception as e:
            logger.debug(f"Dispatch event method failed: {e}")

        return False

    async def _try_force_click_methods(self, page):
        """Try force clicking methods for stubborn checkboxes."""
        logger.info("🔍 Strategy 2: Force clicking methods")

        try:
            # Method 1: Force click the checkbox input
            checkboxes = page.locator('input[type="checkbox"]')
            if await checkboxes.count() > 0:
                first_checkbox = checkboxes.first
                await first_checkbox.click(force=True)
                logger.info(
                    "✅ STRATEGY 2: Successfully clicked checkbox with force=True"
                )
                return True

        except Exception as e:
            logger.debug(f"Force click method failed: {e}")

        try:
            # Method 2: Dispatch click event
            checkboxes = page.locator('input[type="checkbox"]')
            if await checkboxes.count() > 0:
                first_checkbox = checkboxes.first
                await first_checkbox.dispatch_event("click")
                logger.info(
                    "✅ STRATEGY 2: Successfully dispatched click event on checkbox"
                )
                return True

        except Exception as e:
            logger.debug(f"Dispatch event method failed: {e}")

        return False

    async def _try_javascript_checkbox_methods(self, page):
        """Try JavaScript evaluation methods for programmatic checkbox control."""
        logger.info("🔍 Strategy 3: JavaScript evaluation methods")

        try:
            # Method 1: Direct JavaScript checkbox manipulation
            result = await page.evaluate("""
                () => {
                    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    if (checkboxes.length > 0) {
                        const checkbox = checkboxes[0]; // First checkbox (Terms of Service)
                        checkbox.checked = true;

                        // Trigger change event to notify JavaScript frameworks
                        const changeEvent = new Event('change', { bubbles: true });
                        checkbox.dispatchEvent(changeEvent);

                        // Also trigger click event
                        const clickEvent = new Event('click', { bubbles: true });
                        checkbox.dispatchEvent(clickEvent);

                        return checkbox.checked;
                    }
                    return false;
                }
            """)

            if result:
                logger.info(
                    "✅ STRATEGY 3: Successfully checked checkbox using JavaScript evaluation"
                )
                return True

        except Exception as e:
            logger.debug(f"JavaScript evaluation method failed: {e}")

        try:
            # Method 2: Find checkbox by text content and check it
            result = await page.evaluate("""
                () => {
                    // Look for checkboxes near "Terms of Service" text
                    const allElements = document.querySelectorAll('*');
                    for (let element of allElements) {
                        if (element.textContent && element.textContent.includes('Terms of Service')) {
                            // Look for checkbox in this element or nearby
                            const checkbox = element.querySelector('input[type="checkbox"]') ||
                                           element.parentElement?.querySelector('input[type="checkbox"]') ||
                                           element.previousElementSibling?.querySelector('input[type="checkbox"]') ||
                                           element.nextElementSibling?.querySelector('input[type="checkbox"]');

                            if (checkbox) {
                                checkbox.checked = true;
                                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                checkbox.dispatchEvent(new Event('click', { bubbles: true }));
                                return true;
                            }
                        }
                    }
                    return false;
                }
            """)

            if result:
                logger.info(
                    "✅ STRATEGY 3: Successfully found and checked Terms checkbox using text search"
                )
                return True

        except Exception as e:
            logger.debug(f"JavaScript text search method failed: {e}")

        return False

    async def _try_label_click_methods(self, page):
        """Try clicking labels and parent elements to activate checkboxes."""
        logger.info("🔍 Strategy 4: Label and parent element clicking methods")

        try:
            # Method 1: Click labels that contain "Terms of Service"
            labels = page.locator(
                'label:has-text("Terms of Service"), label:has-text("I agree"), label:has-text("terms")'
            )
            label_count = await labels.count()
            logger.info(f"Found {label_count} potential terms labels")

            if label_count > 0:
                first_label = labels.first
                await first_label.click(force=True)
                logger.info(
                    "✅ STRATEGY 4: Successfully clicked Terms of Service label"
                )
                return True

        except Exception as e:
            logger.debug(f"Label clicking method failed: {e}")

        try:
            # Method 2: Click parent elements of checkboxes
            checkboxes = page.locator('input[type="checkbox"]')
            if await checkboxes.count() > 0:
                first_checkbox = checkboxes.first
                parent = first_checkbox.locator("..")
                await parent.click(force=True)
                logger.info(
                    "✅ STRATEGY 4: Successfully clicked checkbox parent element"
                )
                return True

        except Exception as e:
            logger.debug(f"Parent element clicking method failed: {e}")

        try:
            # Method 3: Look for clickable elements with Terms text
            terms_elements = page.locator(
                '*:has-text("Terms of Service"), *:has-text("I agree")'
            )
            if await terms_elements.count() > 0:
                first_element = terms_elements.first
                await first_element.click(force=True)
                logger.info("✅ STRATEGY 4: Successfully clicked Terms text element")
                return True

        except Exception as e:
            logger.debug(f"Terms text element clicking failed: {e}")

        return False

    async def _verify_checkbox_state(self, page):
        """Verify that checkboxes are properly checked."""
        try:
            checked_checkbox = page.locator('input[type="checkbox"]:checked')
            checked_count = await checked_checkbox.count()
            logger.info(f"✅ Total checked checkboxes: {checked_count}")

            # Also verify using JavaScript
            js_checked_count = await page.evaluate("""
                () => {
                    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    let checkedCount = 0;
                    checkboxes.forEach(cb => {
                        if (cb.checked) checkedCount++;
                    });
                    return checkedCount;
                }
            """)
            logger.info(
                f"✅ JavaScript verified checked checkboxes: {js_checked_count}"
            )

        except Exception as e:
            logger.warning(f"Could not verify checkbox state: {e}")

        # Focus on clicking Complete Onboarding button

        # Click Complete Onboarding button with enhanced logic
        return await self._click_complete_onboarding_button(page)

    async def _click_complete_onboarding_button(self, page):
        """Enhanced Complete Onboarding button clicking with multiple strategies."""
        logger.info("🎯 Attempting to click Complete Onboarding button")

        # Strategy 1: Standard button clicking
        button_clicked = await self._try_standard_button_click(page)
        if button_clicked:
            return await self._verify_wizard_completion(page)

        # Strategy 2: Force clicking methods
        button_clicked = await self._try_force_button_click(page)
        if button_clicked:
            return await self._verify_wizard_completion(page)

        # Strategy 3: JavaScript button clicking
        button_clicked = await self._try_javascript_button_click(page)
        if button_clicked:
            return await self._verify_wizard_completion(page)

        logger.error("❌ CRITICAL: Could not click any Complete Onboarding button!")
        await self.take_screenshot(page, "error_no_complete_button")
        return False

    async def _try_standard_button_click(self, page):
        """Try standard button clicking methods."""
        logger.info("🔍 Strategy 1: Standard button clicking")

        complete_buttons = [
            'button:has-text("Complete Onboarding")',
            'button:has-text("Complete")',
            'button:has-text("Finish")',
            'button:has-text("Done")',
            '[data-testid*="complete"]',
            '[data-testid*="finish"]',
        ]

        for button_selector in complete_buttons:
            try:
                logger.info(f"🔍 Looking for button: {button_selector}")
                finish_button = page.locator(button_selector).first

                # Check if button exists and is visible
                if await finish_button.is_visible():
                    logger.info(f"✅ Found visible button: {button_selector}")

                    # Wait for button to be enabled (not disabled)
                    try:
                        await page.wait_for_function(
                            f"""() => {{
                                const button = document.querySelector('{button_selector.replace(":", "\\:")}');
                                return button && !button.disabled && button.offsetParent !== null;
                            }}""",
                            timeout=10000,
                        )
                        logger.info(
                            f"✅ Button is enabled and clickable: {button_selector}"
                        )
                    except:
                        logger.warning(
                            f"⚠️ Button may not be enabled yet: {button_selector}"
                        )

                    # Try to click the button
                    await finish_button.click()
                    logger.info(
                        f"✅ STRATEGY 1: Successfully clicked {button_selector}"
                    )
                    await asyncio.sleep(3)  # Wait for action to complete
                    return True
                else:
                    logger.info(f"Button not visible: {button_selector}")

            except Exception as e:
                logger.debug(f"Standard button {button_selector} failed: {e}")
                continue

        return False

    async def _try_force_button_click(self, page):
        """Try force clicking methods for Complete Onboarding button."""
        logger.info("🔍 Strategy 2: Force button clicking")

        complete_buttons = [
            'button:has-text("Complete Onboarding")',
            'button:has-text("Complete")',
            'button:has-text("Finish")',
        ]

        for button_selector in complete_buttons:
            try:
                finish_button = page.locator(button_selector).first
                if await finish_button.count() > 0:
                    await finish_button.click(force=True)
                    logger.info(f"✅ STRATEGY 2: Force clicked {button_selector}")
                    await asyncio.sleep(3)
                    return True

            except Exception as e:
                logger.debug(f"Force button click {button_selector} failed: {e}")
                continue

        return False

    async def _try_javascript_button_click(self, page):
        """Try JavaScript methods to click Complete Onboarding button."""
        logger.info("🔍 Strategy 3: JavaScript button clicking")

        try:
            # Method 1: Find and click button by text content
            result = await page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button');
                    for (let button of buttons) {
                        if (button.textContent &&
                            (button.textContent.includes('Complete Onboarding') ||
                             button.textContent.includes('Complete') ||
                             button.textContent.includes('Finish'))) {
                            button.click();
                            return true;
                        }
                    }
                    return false;
                }
            """)

            if result:
                logger.info(
                    "✅ STRATEGY 3: Successfully clicked button using JavaScript"
                )
                await asyncio.sleep(3)
                return True

        except Exception as e:
            logger.debug(f"JavaScript button click failed: {e}")

        return False

    async def _verify_wizard_completion(self, page):
        """Verify that the setup wizard has been completed."""
        logger.info("🔍 Verifying wizard completion...")

        # Wait for wizard completion
        await asyncio.sleep(5)
        await self.take_screenshot(page, "10_wizard_complete")

        try:
            # Wait a bit more for any transitions
            await asyncio.sleep(3)

            # Check multiple indicators that wizard is closed
            wizard_indicators = [
                'text="Step 4 of 4"',  # Step indicator should be gone
                'text="Welcome to Firecrawl!"',  # Welcome text should be gone
                'button:has-text("Complete Onboarding")',  # Complete button should be gone
            ]

            wizard_still_open = False
            for indicator in wizard_indicators:
                try:
                    if await page.locator(indicator).is_visible(timeout=2000):
                        logger.warning(f"⚠️ Wizard indicator still visible: {indicator}")
                        wizard_still_open = True
                        break
                except:
                    # If locator times out, that's good - element is not visible
                    continue

            if wizard_still_open:
                logger.warning("⚠️ Wizard still appears to be open")
                await self.take_screenshot(page, "error_wizard_still_open")
                return False
            else:
                logger.info(
                    "✅ Setup wizard completed successfully - all wizard elements are gone"
                )
                return True

        except Exception as e:
            logger.error(f"Error verifying wizard completion: {e}")
            # If we can't verify, assume it worked if we got this far
            logger.info(
                "✅ Setup wizard appears to have completed (verification failed but assuming success)"
            )
            return True

    async def extract_api_key(self, page):
        """Extract API key from dashboard with enhanced pattern matching."""
        logger.info("🔑 Attempting to extract API key")

        try:
            # Grant clipboard permissions before attempting to copy
            context = page.context
            await context.grant_permissions(["clipboard-read", "clipboard-write"])
            logger.info("✅ Granted clipboard permissions")

            # Wait for dashboard to fully load
            await asyncio.sleep(5)
            await self.take_screenshot(page, "12_api_key_search")

            # ENHANCED: First try to extract API key directly from page content
            logger.info("🔍 PRIORITY: Scanning page content for fc- API keys")
            try:
                page_content = await page.content()
                logger.info(f"📄 Page content length: {len(page_content)} characters")

                # Enhanced patterns specifically for Firecrawl API keys
                api_key_patterns = [
                    r"fc-[a-zA-Z0-9]{40,}",  # Standard Firecrawl API key pattern
                    r"fc-[a-zA-Z0-9_-]{32,}",  # Alternative Firecrawl pattern
                    r'"fc-[^"]*"',  # Quoted API key
                    r"'fc-[^']*'",  # Single quoted API key
                    r'value="fc-[^"]*"',  # Input value attribute
                    r'data-[^=]*="fc-[^"]*"',  # Data attribute
                ]

                found_keys = []
                for pattern in api_key_patterns:
                    matches = re.findall(pattern, page_content, re.IGNORECASE)
                    for match in matches:
                        # Clean the match (remove quotes, etc.)
                        clean_match = re.sub(r'["\']', "", match)
                        clean_match = re.sub(
                            r"^[^f]*fc-", "fc-", clean_match
                        )  # Remove prefixes
                        clean_match = re.sub(
                            r"[^a-zA-Z0-9_-].*$", "", clean_match
                        )  # Remove suffixes

                        if clean_match.startswith("fc-") and len(clean_match) > 20:
                            found_keys.append(clean_match)
                            logger.info(
                                f"🔑 FOUND API KEY in page content: {clean_match}"
                            )

                # Return the first valid API key found
                if found_keys:
                    api_key = found_keys[0]  # Take the first/best match
                    logger.info(
                        f"🎉 SUCCESS! Extracted API key from page content: {api_key}"
                    )
                    return api_key
                else:
                    logger.warning("⚠️ No fc- API keys found in page content")

            except Exception as e:
                logger.error(f"Error scanning page content: {e}")

            # FALLBACK: Try clicking copy buttons if page content parsing failed
            logger.info("🔄 FALLBACK: Trying copy button approach")

            # Look for API Key section and copy button with various strategies
            api_key_selectors = [
                'div:has-text("API Key") button[title*="Copy"], div:has-text("API Key") button[aria-label*="copy"]',  # Copy button with tooltip
                'div:has-text("API Key") button svg',  # SVG icon in API Key section
                'button:has([data-icon="copy"])',  # Copy button with copy icon
                "button:has(.lucide-clipboard)",  # Copy button with clipboard icon
                'button[title*="Copy API"], button[aria-label*="Copy API"]',  # Buttons with copy API text
                '[data-testid*="copy"]',  # Any copy button
                'button:has-text("Copy")',  # Button with "Copy" text
            ]

            for selector in api_key_selectors:
                try:
                    logger.info(f"🔍 Trying API key selector: {selector}")
                    copy_buttons = page.locator(selector)
                    copy_button_count = await copy_buttons.count()

                    if copy_button_count > 0:
                        logger.info(
                            f"Found {copy_button_count} potential copy buttons with selector: {selector}"
                        )

                        # Try each copy button
                        for i in range(
                            min(copy_button_count, 3)
                        ):  # Limit to first 3 buttons
                            copy_button = copy_buttons.nth(i)

                            if await copy_button.is_visible():
                                logger.info(
                                    f"🎯 Clicking copy button {i + 1} with selector: {selector}"
                                )

                                # Set up permission handler for any clipboard permission dialogs
                                async def handle_dialog(dialog):
                                    logger.info(f"🔐 Handling dialog: {dialog.message}")
                                    await dialog.accept()

                                page.on("dialog", handle_dialog)

                                await copy_button.click()
                                await asyncio.sleep(
                                    3
                                )  # Give more time for clipboard operation
                                await self.take_screenshot(
                                    page, f"13_after_copy_button_{i + 1}"
                                )

                                # Try to read from clipboard with multiple attempts
                                api_key = await self._extract_from_clipboard(page)
                                if api_key and api_key.startswith("fc-"):
                                    return api_key

                        break  # Stop after trying first selector that has buttons

                except Exception as e:
                    logger.debug(f"Copy button selector {selector} failed: {e}")
                    continue

            # FINAL FALLBACK: Look for any text that looks like an API key on page
            logger.info("🔍 LAST RESORT: Looking for any API key patterns on page")
            try:
                # Get all text content from the page
                all_text = await page.evaluate("() => document.body.innerText")

                # Look for fc- patterns in the visible text
                fc_pattern = r"fc-[a-zA-Z0-9_-]{20,}"
                matches = re.findall(fc_pattern, all_text, re.IGNORECASE)

                for match in matches:
                    if len(match) > 30:  # Reasonable API key length
                        logger.info(f"🔑 Found potential API key in page text: {match}")
                        return match

            except Exception as e:
                logger.warning(f"Error scanning page text: {e}")

            logger.warning("⚠️ Could not extract API key using any method")
            return None

        except Exception as e:
            logger.error(f"❌ Error extracting API key: {e}")
            await self.take_screenshot(page, "error_api_key")
            return None

    async def _extract_from_clipboard(self, page):
        """Extract API key from clipboard."""
        for attempt in range(3):
            try:
                # Use page.evaluate to access clipboard
                api_key = await page.evaluate("""
                    async () => {
                        try {
                            const text = await navigator.clipboard.readText();
                            return text;
                        } catch (error) {
                            console.log('Clipboard read error:', error);
                            return null;
                        }
                    }
                """)

                if api_key and len(api_key) > 10 and not api_key.startswith("http"):
                    logger.info(
                        f"🔑 SUCCESS! Extracted API key from clipboard: {api_key}"
                    )
                    return api_key
                elif api_key:
                    logger.info(
                        f"🔍 Clipboard content (attempt {attempt + 1}): {api_key[:50]}..."
                    )

            except Exception as e:
                logger.warning(f"Clipboard read attempt {attempt + 1} failed: {e}")

            await asyncio.sleep(2)  # Wait between attempts

        return None

    async def _extract_from_page_content(self, page):
        """Extract API key from page content."""
        logger.info("🔍 Attempting to find API key text on page")
        api_key_patterns = [
            r"fc-[a-zA-Z0-9]{40,}",  # Firecrawl API key pattern
            r"[a-zA-Z0-9]{32,}",  # General long string pattern
        ]

        page_content = await page.content()
        for pattern in api_key_patterns:
            matches = re.findall(pattern, page_content)
            for match in matches:
                if len(match) > 20 and not match.startswith("http"):
                    logger.info(f"🔑 Found potential API key on page: {match}")
                    return match

        return None

    async def save_credentials(self, profile: dict, password: str, api_key: str = None):
        """Save account credentials to file immediately."""
        try:
            credentials_file = Path("SAVED_CREDENTIALS.json")

            # Load existing credentials or create new
            if credentials_file.exists():
                with open(credentials_file, "r") as f:
                    all_credentials = json.load(f)
                # Ensure firecrawl_accounts exists
                if "firecrawl_accounts" not in all_credentials:
                    all_credentials["firecrawl_accounts"] = []
            else:
                all_credentials = {"firecrawl_accounts": []}

            # Create new account entry
            account_data = {
                "email": profile["email"],
                "password": password,
                "created_at": datetime.now().isoformat(),
                "status": "verified" if api_key else "pending_api_key",
            }

            if api_key and len(api_key) > 10 and not api_key.startswith("- __main__"):
                account_data["api_key"] = api_key

            # Check if account already exists and update it
            existing_account = None
            for i, acc in enumerate(all_credentials["firecrawl_accounts"]):
                if acc.get("email") == profile["email"]:
                    existing_account = i
                    break

            if existing_account is not None:
                # Update existing account
                all_credentials["firecrawl_accounts"][existing_account].update(
                    account_data
                )
                logger.info(f"💾 Updated existing credentials for {profile['email']}")
            else:
                # Add new account
                all_credentials["firecrawl_accounts"].append(account_data)
                logger.info(f"💾 Added new credentials for {profile['email']}")

            # Save back to file
            with open(credentials_file, "w") as f:
                json.dump(all_credentials, f, indent=2)

            logger.info(f"💾 Credentials saved to {credentials_file}")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to save credentials: {e}")
            return False

    async def validate_dashboard_access(self, page):
        try:
            # Wait for page to fully load
            await asyncio.sleep(10)  # Give more time for redirects
            await self.take_screenshot(page, "11_dashboard")

            # Check current URL
            current_url = page.url
            logger.info(f"📍 Current URL: {current_url}")

            # Check if we were redirected to dashboard
            if "dashboard" in current_url.lower() or "app" in current_url.lower():
                logger.info("✅ Dashboard access confirmed via URL")
                return True

            # Look for common dashboard elements
            dashboard_selectors = [
                'text="API Key"',
                'text="Dashboard"',
                'text="Welcome"',
                '[data-testid*="dashboard"]',
                ".dashboard",
                'text="Firecrawl"',
            ]

            for selector in dashboard_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=3000)
                    if element:
                        logger.info(
                            f"✅ Dashboard access confirmed. Found element: {selector}"
                        )
                        return True
                except:
                    continue

            # Check page title
            title = await page.title()
            logger.info(f"📄 Page title: {title}")

            if any(word in title.lower() for word in ["dashboard", "firecrawl", "app"]):
                logger.info("✅ Dashboard access confirmed via page title")
                return True

            logger.warning("⚠️ Dashboard access uncertain - no clear indicators found")
            return False

        except Exception as e:
            logger.error(f"❌ Error validating dashboard access: {e}")
            await self.take_screenshot(page, "error_dashboard_validation")
            return False

    async def run_test(self):
        """Run the complete test."""
        logger.info("🚀 Starting BitBrowser Firecrawl signup test")

        # Generate profile
        profile = self.profile_generator.generate_profile()
        password = generate_strong_password()
        logger.info(f"👤 Generated profile: {profile['email']}")
        logger.info(f"🔐 Generated password: {password}")
        print(f"\n=== CREDENTIALS GENERATED ===")
        print(f"Email: {profile['email']}")
        print(f"Password: {password}")
        print(f"=============================\n")

        # Open BitBrowser
        logger.info(f"🌐 Opening BitBrowser with ID: {BROWSER_ID}")
        try:
            browser_response = openBrowser(BROWSER_ID)
            if not browser_response.get("success"):
                logger.error(f"❌ Failed to open browser: {browser_response}")
                return False

            ws_endpoint = browser_response["data"]["ws"]
            logger.info(f"🔗 WebSocket endpoint: {ws_endpoint}")

        except Exception as e:
            logger.error(f"❌ Error opening BitBrowser: {e}")
            return False

        # Connect Playwright to BitBrowser
        async with async_playwright() as playwright:
            try:
                browser = await playwright.chromium.connect_over_cdp(ws_endpoint)
                default_context = browser.contexts[0]
                page = await default_context.new_page()

                logger.info("✅ Connected to BitBrowser via CDP")

                # Step 1: Perform signup or detect existing login
                signup_success = await self.perform_signup(page, profile, password)
                if not signup_success:
                    return False

                # Check if we're already on dashboard (existing login case)
                current_url = page.url
                logger.info(f"📍 Current URL after signup: {current_url}")

                if "dashboard" in current_url.lower() or "app" in current_url.lower():
                    logger.info(
                        "🎉 ALREADY ON DASHBOARD: Skipping email verification and wizard"
                    )

                    # Save credentials for existing session (no specific email/password for this case)
                    logger.info("💾 Saving session info...")
                    try:
                        # For existing sessions, we don't have the specific credentials
                        await self.save_credentials(
                            {"email": "<EMAIL>"},
                            "existing_session",
                        )
                    except:
                        pass

                    # Go directly to API key extraction
                    api_key = await self.extract_api_key(page)

                    # Final results for existing session
                    if api_key:
                        logger.info(
                            "🎉 EXISTING SESSION SUCCESS: API key extracted from existing account!"
                        )
                        logger.info(f"🔑 API Key: {api_key}")
                        return True
                    else:
                        logger.info(
                            "🎊 EXISTING SESSION: Dashboard accessible, but API key extraction failed"
                        )
                        logger.info(
                            "💡 Account is ready to use - API key can be found manually in dashboard"
                        )
                        return True

                # If not on dashboard, continue with normal flow (new signup)
                logger.info(
                    "📧 New signup detected, continuing with email verification..."
                )

                # Step 2: Wait for verification email
                email_content = await self.wait_for_verification_email(profile["email"])
                if not email_content:
                    return False

                # Step 3: Handle verification
                verification_success = await self.handle_verification(
                    page, email_content
                )
                if not verification_success:
                    return False

                # Step 4: Complete setup wizard
                wizard_success = await self.complete_setup_wizard(page)
                if not wizard_success:
                    logger.warning("⚠️ Setup wizard failed, but continuing...")

                # Step 5: Validate dashboard access and save credentials immediately
                dashboard_success = await self.validate_dashboard_access(page)
                if dashboard_success:
                    logger.info(
                        "🎊 Dashboard access confirmed - saving credentials immediately!"
                    )
                    await self.save_credentials(profile, password)

                # Step 6: Wait after wizard completion before API key extraction
                logger.info(
                    "⏳ Waiting 5-10 seconds after wizard completion before API key extraction..."
                )
                wait_time = random.randint(5, 10)
                logger.info(
                    f"⏳ Waiting {wait_time} seconds for dashboard to fully load..."
                )
                await asyncio.sleep(wait_time)

                # Step 7: Extract API key
                api_key = await self.extract_api_key(page)

                # Update credentials with API key if extracted
                if api_key:
                    await self.save_credentials(profile, password, api_key)

                # Final results
                if api_key:
                    logger.info(
                        "🎉 COMPLETE SUCCESS: BitBrowser test completed successfully!"
                    )
                    logger.info(f"✅ Account created: {profile['email']}")
                    logger.info(f"✅ Password: {password}")
                    logger.info(f"🔑 API Key: {api_key}")
                    return True
                elif dashboard_success:
                    logger.info(
                        "🎊 PARTIAL SUCCESS: Dashboard accessible but no API key extracted"
                    )
                    logger.info(f"✅ Account created: {profile['email']}")
                    logger.info(f"✅ Password: {password}")
                    return True
                else:
                    logger.warning(
                        "⚠️ MINIMAL SUCCESS: Signup completed but dashboard/API access uncertain"
                    )
                    return False

            except Exception as e:
                logger.error(f"❌ Error during Playwright automation: {e}")
                await self.take_screenshot(page, "error_automation")
                return False

            finally:
                try:
                    await page.close()
                    await browser.close()
                except:
                    pass

                # Close BitBrowser
                try:
                    closeBrowser(BROWSER_ID)
                    logger.info("🔒 BitBrowser closed")
                except Exception as e:
                    logger.warning(f"Warning: Failed to close BitBrowser: {e}")


async def main():
    """Main entry point."""
    logger.info("=" * 60)
    logger.info("🧪 BitBrowser + Firecrawl Signup Test")
    logger.info("=" * 60)

    test = BitBrowserFirecrawlTest()
    success = await test.run_test()

    if success:
        logger.info("🎊 TEST RESULT: SUCCESS - BitBrowser bypassed antibot detection!")
        sys.exit(0)
    else:
        logger.error("💥 TEST RESULT: FAILED - Could not complete signup process")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
