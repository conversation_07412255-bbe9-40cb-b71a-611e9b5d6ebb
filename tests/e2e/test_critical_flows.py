"""Minimal E2E tests for critical functionality."""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch
from core.config import Config, ServiceConfig
from core.browser import BrowserManager
from core.randomizer import create_randomizer, RandomizationProfile
from services.assemblyai import AssemblyAIService


@pytest.mark.e2e
@pytest.mark.slow
class TestCriticalE2EFlows:
    """Minimal E2E tests for critical application flows."""

    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return Config.load()

    @pytest.fixture
    def randomizer(self):
        """Create test randomizer."""
        return create_randomizer(RandomizationProfile.TESTING, enabled=False)

    @pytest.mark.asyncio
    async def test_config_loading_and_validation(self, config):
        """Test that configuration loads and validates correctly."""
        # Test basic config structure
        assert hasattr(config, "services")
        assert hasattr(config, "email_domain_groups")
        assert hasattr(config, "webhook")

        # Test domain group resolution
        if "assemblyai" in config.services:
            domains = config.get_domains_for_service("assemblyai")
            assert isinstance(domains, list)

    @pytest.mark.asyncio
    async def test_service_initialization_flow(self, config):
        """Test complete service initialization flow."""
        # Create service config
        service_config = ServiceConfig(
            name="TestService",
            start_url="https://example.com",
            email_domain_groups=["primary"],
            sender_domains=["example.com"],
            delay_times={"page_load": 1000},
        )

        # Test service can be created with config
        with patch.object(AssemblyAIService, "__init__", return_value=None):
            # This tests the service can be instantiated without errors
            pass

    @pytest.mark.asyncio
    async def test_browser_manager_complete_lifecycle(self, randomizer):
        """Test complete browser manager lifecycle."""
        manager = BrowserManager(
            browser_engines=["chromium"],
            randomizer=randomizer,
            enable_randomization=False,
            timeout=5000,
        )

        # Test startup
        await manager.start()
        assert manager.browser is not None
        assert manager.context is not None
        assert manager.page is not None

        # Test basic navigation (data URL to avoid external dependency)
        test_html = """
        <!DOCTYPE html>
        <html>
        <body>
            <h1>E2E Test Page</h1>
            <form id="test-form">
                <input type="email" id="email" required>
                <button type="submit">Submit</button>
            </form>
            <div id="result"></div>
            <script>
                document.getElementById('test-form').onsubmit = function(e) {
                    e.preventDefault();
                    document.getElementById('result').textContent = 'Form submitted successfully';
                };
            </script>
        </body>
        </html>
        """

        await manager.page.set_content(test_html)

        # Test form interaction flow
        await manager.fill("#email", "<EMAIL>", human_like=False)
        await manager.click('button[type="submit"]', human_like=False)

        # Verify form submission worked
        result_text = await manager.get_text("#result")
        assert "Form submitted successfully" in result_text

        # Test cleanup
        await manager.stop()

    @pytest.mark.asyncio
    async def test_email_parsing_integration(self):
        """Test email parsing functionality works end-to-end."""
        # Test AssemblyAI magic link parsing
        test_email_content = """
        Content-Type: text/html; charset=utf-8
        
        <html>
        <body>
        Please click this link to continue:
        <a href=3D"https://stytch.com/v1/magic_links/redirect?token=abc123&amp;public_token=public-token-live-12345">
        Login to AssemblyAI
        </a>
        </body>
        </html>
        """

        magic_link = AssemblyAIService.parse_magic_link(test_email_content)
        assert magic_link is not None
        assert "stytch.com/v1/magic_links/redirect" in magic_link
        assert "token=abc123" in magic_link

        # Test email data parsing
        email_data = {"raw": test_email_content, "subject": "Login Link"}
        parsed_data = AssemblyAIService.parse_email_data(email_data)
        assert "magicLink" in parsed_data
        assert parsed_data["magicLink"] == magic_link

    @pytest.mark.asyncio
    async def test_randomizer_integration_flow(self, randomizer):
        """Test randomizer integration across components."""
        # Test delay generation
        delay1 = randomizer.uniform(0.1, 0.2)
        delay2 = randomizer.uniform(0.1, 0.2)

        assert 0.1 <= delay1 <= 0.2
        assert 0.1 <= delay2 <= 0.2

        # Test random choice
        choices = ["option1", "option2", "option3"]
        choice = randomizer.choice(choices)
        assert choice in choices

        # Test integer generation
        rand_int = randomizer.randint(1, 10)
        assert 1 <= rand_int <= 10

    @pytest.mark.asyncio
    async def test_profile_generation_flow(self, config):
        """Test profile generation with domain integration."""
        from core.profile import ProfileGenerator
        from core.domain_selector import DomainSelector
        from core.randomizer import create_randomizer, RandomizationProfile

        # Get domains for a service
        domains = config.get_domains_for_service("assemblyai")
        if not domains:
            # Create test domain if none configured
            from core.config import EmailDomainConfig

            domains = [EmailDomainConfig(domain="test.example.com", weight=100)]

        # Create domain selector
        randomizer = create_randomizer(RandomizationProfile.TESTING, enabled=False)
        domain_selector = DomainSelector(domains, randomizer=randomizer)
        selected_domain = domain_selector.select_domain()

        # Generate profile
        profile_generator = ProfileGenerator(selected_domain, randomizer=randomizer)
        profile = profile_generator.generate_profile()

        # Verify profile structure
        assert "email" in profile
        assert "username" in profile
        assert "first_name" in profile
        assert "last_name" in profile
        assert profile["email"].endswith(selected_domain.domain)

    @pytest.mark.asyncio
    async def test_mock_service_registration_flow(self, config):
        """Test mock service registration flow without external calls."""
        service_config = ServiceConfig(
            name="MockService",
            start_url="https://mock.example.com/signup",
            email_domain_groups=["primary"],
            sender_domains=["mock.example.com"],
            delay_times={"page_load": 1000, "stay_on_finish": 2000},
        )

        # Create mock email client
        mock_email_client = AsyncMock()
        mock_email_client.get_latest_email_content.return_value = """
        href=3D"https://stytch.com/v1/magic_links/redirect?token=mock123"
        """

        # Test service initialization
        service = AssemblyAIService(service_config, config)
        service.email_client = mock_email_client

        # Test profile generation
        profile = service.profile_generator.generate_profile()
        assert profile["email"].endswith(service.selected_domain.domain)

        # Test email parsing
        raw_content = await mock_email_client.get_latest_email_content(
            "mock", profile["email"]
        )
        magic_link = AssemblyAIService.parse_magic_link(raw_content)
        assert magic_link is not None
        assert "stytch.com" in magic_link

    @pytest.mark.asyncio
    async def test_error_handling_flow(self, randomizer):
        """Test error handling throughout the application flow."""
        # Test browser manager with invalid configuration
        with pytest.raises(ValueError):
            BrowserManager(browser_engines=[], randomizer=randomizer)

        # Test config with invalid data
        with pytest.raises((ValueError, Exception)):
            Config.load(config_path="non_existent_config.yaml")

    @pytest.mark.asyncio
    async def test_clipboard_functionality_e2e(self, randomizer):
        """Test clipboard functionality end-to-end."""
        manager = BrowserManager(
            browser_engines=["chromium"],
            randomizer=randomizer,
            enable_randomization=False,
        )

        await manager.start()

        try:
            # Create page with clipboard test
            clipboard_html = """
            <!DOCTYPE html>
            <html>
            <body>
                <button id="copy-btn">Copy Text</button>
                <input id="paste-input" type="text" placeholder="Paste here">
                <div id="result"></div>
                <script>
                    const testText = 'clipboard-test-content';
                    
                    document.getElementById('copy-btn').onclick = async function() {
                        try {
                            await navigator.clipboard.writeText(testText);
                            document.getElementById('result').textContent = 'Text copied';
                        } catch (err) {
                            document.getElementById('result').textContent = 'Copy failed: ' + err.message;
                        }
                    };
                </script>
            </body>
            </html>
            """

            await manager.page.set_content(clipboard_html)

            # Test copy operation
            await manager.click("#copy-btn", human_like=False)

            # Wait for copy operation
            await asyncio.sleep(0.5)

            # Verify copy worked (result should show success or failure)
            result_text = await manager.get_text("#result")
            assert len(result_text) > 0  # Should have some result

        finally:
            await manager.stop()

    @pytest.mark.asyncio
    async def test_multi_step_form_flow(self, randomizer):
        """Test multi-step form interaction flow."""
        manager = BrowserManager(
            browser_engines=["chromium"],
            randomizer=randomizer,
            enable_randomization=False,
        )

        await manager.start()

        try:
            # Create multi-step form
            form_html = """
            <!DOCTYPE html>
            <html>
            <body>
                <div id="step1" class="step">
                    <h2>Step 1: Email</h2>
                    <input type="email" id="email" required>
                    <button id="next1">Next</button>
                </div>
                <div id="step2" class="step" style="display:none;">
                    <h2>Step 2: Details</h2>
                    <input type="text" id="name" placeholder="Name" required>
                    <button id="next2">Next</button>
                </div>
                <div id="step3" class="step" style="display:none;">
                    <h2>Step 3: Complete</h2>
                    <p>Registration complete!</p>
                    <div id="final-result"></div>
                </div>
                <script>
                    document.getElementById('next1').onclick = function() {
                        const email = document.getElementById('email').value;
                        if (email) {
                            document.getElementById('step1').style.display = 'none';
                            document.getElementById('step2').style.display = 'block';
                        }
                    };
                    
                    document.getElementById('next2').onclick = function() {
                        const name = document.getElementById('name').value;
                        if (name) {
                            document.getElementById('step2').style.display = 'none';
                            document.getElementById('step3').style.display = 'block';
                            document.getElementById('final-result').textContent = 
                                'Welcome ' + name + '! Email: ' + document.getElementById('email').value;
                        }
                    };
                </script>
            </body>
            </html>
            """

            await manager.page.set_content(form_html)

            # Step 1: Fill email and proceed
            await manager.fill("#email", "<EMAIL>", human_like=False)
            await manager.click("#next1", human_like=False)

            # Wait for step transition
            await asyncio.sleep(0.2)

            # Step 2: Fill name and proceed
            await manager.fill("#name", "Test User", human_like=False)
            await manager.click("#next2", human_like=False)

            # Wait for final step
            await asyncio.sleep(0.2)

            # Verify completion
            final_text = await manager.get_text("#final-result")
            assert "Welcome Test User" in final_text
            assert "<EMAIL>" in final_text

        finally:
            await manager.stop()

    @pytest.mark.asyncio
    async def test_configuration_inheritance_flow(self, config):
        """Test configuration inheritance between global and service configs."""
        # Test that services inherit from global configuration
        if "assemblyai" in config.services:
            service_config = config.services["assemblyai"]

            # Verify service has required fields
            assert hasattr(service_config, "name")
            assert hasattr(service_config, "start_url")
            assert hasattr(service_config, "email_domain_groups")

            # Test domain resolution
            domains = config.get_domains_for_service("assemblyai")
            assert isinstance(domains, list)

    @pytest.mark.asyncio
    async def test_timing_and_delays_integration(self, randomizer):
        """Test timing and delay integration across components."""
        import time

        # Test various delay methods work without errors
        start_time = time.time()

        # Short delays for testing
        await randomizer.delay(0.01, 0.02)

        end_time = time.time()
        elapsed = end_time - start_time

        # Should have some delay but not too long
        assert elapsed >= 0.01
        assert elapsed <= 0.1  # Allow some overhead

    @pytest.mark.asyncio
    async def test_complete_mock_automation_flow(self, config):
        """Test complete automation flow with all mocked external dependencies."""
        # This test simulates a complete registration flow without external calls

        # 1. Configuration loading ✓
        assert config is not None

        # 2. Service initialization
        service_config = ServiceConfig(
            name="CompleteTestService",
            start_url="https://test.example.com",
            email_domain_groups=["primary"],
            sender_domains=["test.example.com"],
            delay_times={"page_load": 500},
        )

        # 3. Mock email client
        mock_email_client = AsyncMock()
        mock_email_client.get_latest_email_content.return_value = """
        <a href=3D"https://stytch.com/v1/magic_links/redirect?token=complete123">Complete</a>
        """

        # 4. Service creation
        service = AssemblyAIService(service_config, config)
        service.email_client = mock_email_client

        # 5. Profile generation
        profile = service.profile_generator.generate_profile()
        assert "email" in profile

        # 6. Email parsing simulation
        raw_content = await mock_email_client.get_latest_email_content(
            "test", profile["email"]
        )
        magic_link = AssemblyAIService.parse_magic_link(raw_content)
        assert magic_link is not None

        # 7. Verify complete flow worked
        assert "stytch.com" in magic_link
        assert "complete123" in magic_link
