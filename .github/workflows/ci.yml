name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  PYTHON_VERSION: "3.11"

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-type: [unit, integration, browser]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install uv
      uses: astral-sh/setup-uv@v2

    - name: Install dependencies
      run: |
        uv sync --dev

    - name: Install Playwright browsers (for browser tests)
      if: matrix.test-type == 'browser'
      run: |
        uv run playwright install chromium

    - name: Run unit tests
      if: matrix.test-type == 'unit'
      run: |
        uv run ci-test-unit

    - name: Run integration tests
      if: matrix.test-type == 'integration'
      run: |
        uv run ci-test-integration

    - name: Run browser tests
      if: matrix.test-type == 'browser'
      run: |
        uv run ci-test-browser
      env:
        # Ensure headless mode for CI
        BROWSER_HEADLESS: true

    - name: Upload coverage to Codecov
      if: matrix.test-type != 'browser'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: ${{ matrix.test-type }}
        name: coverage-${{ matrix.test-type }}

  lint:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install uv
      uses: astral-sh/setup-uv@v2

    - name: Install dependencies
      run: |
        uv sync --dev

    - name: Run ruff (linter)
      run: |
        uv run lint

    - name: Run ruff (formatter)
      run: |
        uv run format-check

    - name: Run mypy (type checking)
      run: |
        uv run type-check

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install uv
      uses: astral-sh/setup-uv@v2

    - name: Install dependencies
      run: |
        uv sync --dev

    - name: Run bandit security scan
      run: |
        uv run security

    - name: Upload bandit report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: bandit-report
        path: bandit-report.json

  e2e:
    name: E2E Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install uv
      uses: astral-sh/setup-uv@v2

    - name: Install dependencies
      run: |
        uv sync --dev

    - name: Install Playwright browsers
      run: |
        uv run playwright install chromium

    - name: Run E2E tests
      run: |
        uv run ci-test-e2e
      env:
        BROWSER_HEADLESS: true

  build:
    name: Build Check
    runs-on: ubuntu-latest
    needs: [test, lint]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install uv
      uses: astral-sh/setup-uv@v2

    - name: Install dependencies
      run: |
        uv sync --dev

    - name: Test package build
      run: |
        uv run build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-artifacts
        path: dist/

  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install uv
      uses: astral-sh/setup-uv@v2

    - name: Install dependencies
      run: |
        uv sync --dev

    - name: Run performance tests
      run: |
        uv run pytest tests/ -m "not slow" --benchmark-only --benchmark-json=benchmark.json || true

    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: benchmark-results
        path: benchmark.json

  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [test, lint, security, build]
    if: always()
    
    steps:
    - name: Notify success
      if: needs.test.result == 'success' && needs.lint.result == 'success' && needs.build.result == 'success'
      run: |
        echo "✅ All CI checks passed successfully!"
        
    - name: Notify failure
      if: needs.test.result == 'failure' || needs.lint.result == 'failure' || needs.build.result == 'failure'
      run: |
        echo "❌ CI checks failed. Please review the logs."
        exit 1