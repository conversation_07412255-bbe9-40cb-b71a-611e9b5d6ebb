"""
Pytest configuration and shared fixtures for RegBot test suite.
"""

import asyncio
import os
import sys
from pathlib import Path
import pytest
from unittest.mock import AsyncMock, MagicMock

# Add the project root to Python path so tests can import modules
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Set test environment
os.environ["REGBOT_ENV"] = "test"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    return {
        "browser": {
            "headless": True,
            "timeout": 30000,
            "enable_randomization": False,
            "engines": ["chromium"],
        },
        "email": {
            "domains": [{"domain": "test.example.com", "weight": 100, "enabled": True}]
        },
        "services": {
            "exaai": {"enabled": True, "timeout": 60, "max_retries": 3},
            "assemblyai": {"enabled": True, "timeout": 60, "max_retries": 3},
            "firecrawl": {"enabled": True, "timeout": 120, "max_retries": 3},
        },
    }


@pytest.fixture
def mock_profile():
    """Mock user profile for testing."""
    return {
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "username": "johndoe123",
        "age": 30,
        "gender": "male",
        "country": "US",
        "state": "CA",
        "city": "San Francisco",
        "phone": "******-0123",
    }


@pytest.fixture
def mock_email_content():
    """Mock email content for testing."""
    return {
        "subject": "Test Verification Email",
        "sender": "<EMAIL>",
        "recipient": "<EMAIL>",
        "body": """
        Please verify your account by clicking the link below:
        https://example.com/verify?token=abc123def456
        
        Verification code: 123456
        """,
        "html_body": """
        <html>
        <body>
        <p>Please verify your account by clicking the link below:</p>
        <a href="https://example.com/verify?token=abc123def456">Verify Account</a>
        <p>Verification code: <strong>123456</strong></p>
        </body>
        </html>
        """,
    }


@pytest.fixture
def mock_api_responses():
    """Mock API responses for different services."""
    return {
        "exaai": {
            "api_key": "exa_test_key_123456789abcdef",
            "dashboard_text": "ExaAI Dashboard - Welcome",
            "clipboard_content": "exa_test_key_123456789abcdef",
        },
        "assemblyai": {
            "api_key": "assembly_test_key_987654321",
            "dashboard_text": "AssemblyAI Console",
            "clipboard_content": "assembly_test_key_987654321",
        },
        "firecrawl": {
            "api_key": "fc-test123456789abcdef",
            "dashboard_text": "Firecrawl Dashboard",
            "clipboard_content": "fc-test123456789abcdef",
        },
    }


@pytest.fixture
def mock_browser_manager():
    """Mock browser manager for integration tests."""
    from tests.mocks.mock_browser import MockBrowserManager

    return MockBrowserManager()


@pytest.fixture
def mock_randomizer():
    """Mock randomizer with predictable outputs."""
    mock = MagicMock()
    mock.choice.return_value = "test_choice"
    mock.randint.return_value = 42
    mock.random.return_value = 0.5
    mock.delay = AsyncMock()
    mock.human_type = AsyncMock()
    mock.human_click = AsyncMock()
    return mock


@pytest.fixture
def mock_email_client():
    """Mock email client for testing."""
    mock = AsyncMock()
    mock.get_latest_email_content = AsyncMock(return_value="Mock email content")
    mock.wait_for_email = AsyncMock(return_value=True)
    return mock


@pytest.fixture
def temp_test_dir(tmp_path):
    """Temporary directory for test files."""
    test_dir = tmp_path / "regbot_test"
    test_dir.mkdir()
    return test_dir


@pytest.fixture(autouse=True)
def clean_environment():
    """Clean environment before each test."""
    # Remove any test-related environment variables
    test_vars = [k for k in os.environ.keys() if k.startswith("REGBOT_TEST_")]
    for var in test_vars:
        del os.environ[var]

    yield

    # Cleanup after test
    test_vars = [k for k in os.environ.keys() if k.startswith("REGBOT_TEST_")]
    for var in test_vars:
        del os.environ[var]


# Test markers
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line("markers", "unit: mark test as unit test (fast, isolated)")
    config.addinivalue_line(
        "markers", "integration: mark test as integration test (medium speed)"
    )
    config.addinivalue_line(
        "markers", "browser: mark test as browser test (requires browser)"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as end-to-end test (slow, full system)"
    )
    config.addinivalue_line(
        "markers", "smoke: mark test as smoke test (basic health checks)"
    )
    config.addinivalue_line("markers", "performance: mark test as performance test")


# Custom test collection
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on file location."""
    for item in items:
        # Add markers based on test file location
        if "unit/" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration/" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "browser/" in str(item.fspath):
            item.add_marker(pytest.mark.browser)
        elif "e2e/" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
        elif "smoke/" in str(item.fspath):
            item.add_marker(pytest.mark.smoke)
        elif "performance/" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
