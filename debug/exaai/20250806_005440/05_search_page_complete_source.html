<!DOCTYPE html><html lang="en" data-sentry-element="Html" data-sentry-component="MyDocument" data-sentry-source-file="_document.tsx" style="--banner-offset: 60px;"><head data-sentry-element="Head" data-sentry-source-file="_document.tsx"><meta charset="utf-8"><meta name="viewport" content="width=device-width"><title>Playground | Exa API</title><meta name="next-head-count" content="3"><meta charset="utf-8" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png"><link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png"><meta name="description" content="API Dashboard for the Exa Search API" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta name="keywords" content="dashboard, Metaphor, Exa, API, search, AI, LLMs" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta name="author" content="Exa" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta name="twitter:card" content="summary_large_image" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta name="twitter:title" content="Exa API Dashboard" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta name="twitter:description" content="API Dashboard for the Exa Search API" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta name="twitter:image" content="https://exa.imgix.net/og-image.png/" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta name="twitter:site" content="@metaphorsystems" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta name="twitter:creator" content="@metaphorsystems" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta property="og:title" content="Exa API Dashboard" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta property="og:description" content="API Dashboard for the Exa Search API" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta property="og:image" content="https://exa.imgix.net/og-image.png/" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta property="og:site_name" content="Exa" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><meta property="og:type" content="website" data-sentry-element="meta" data-sentry-source-file="_document.tsx"><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-R43EXQ60NH&amp;cx=c&amp;gtm=45He5840h1v9195906795za200&amp;tag_exp=101509157~103116026~103200004~103233427~104684208~104684211~105033766~105033768~105087538~105087540~105103161~105103163"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-CPMTFL65Z3&amp;cx=c&amp;gtm=45He5840h1v9195906795za200&amp;tag_exp=101509157~103116026~103200004~103233427~104684208~104684211~105033766~105033768~105087538~105087540~105103161~105103163"></script><script src="https://app.chatwoot.com/packs/js/sdk.js" async=""></script><script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-KLW63SD9"></script><script id="gtm-script" data-sentry-element="Script" data-sentry-source-file="_app.tsx" data-nscript="beforeInteractive">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                          })(window,document,'script','dataLayer','GTM-KLW63SD9');</script><link rel="preload" href="/_next/static/css/0f64d85ca3e67de1.css" as="style"><link rel="stylesheet" href="/_next/static/css/0f64d85ca3e67de1.css" data-n-g=""><noscript data-n-css=""></noscript><script defer="" nomodule="" src="/_next/static/chunks/polyfills-78c92fac7aa8fdd8.js"></script><script src="/_next/static/chunks/webpack-1e1819cfb9c3275a.js" defer=""></script><script src="/_next/static/chunks/framework-a3521f82ace7ed6a.js" defer=""></script><script src="/_next/static/chunks/main-f3e19c80ec04ce65.js" defer=""></script><script src="/_next/static/chunks/pages/_app-c5fdb60cac360bd8.js" defer=""></script><script src="/_next/static/chunks/9593-17d538962fa0f59f.js" defer=""></script><script src="/_next/static/chunks/624-4bdcf5f30eea4637.js" defer=""></script><script src="/_next/static/chunks/696-51d915a7733cf088.js" defer=""></script><script src="/_next/static/chunks/9734-9617a7b34acacff1.js" defer=""></script><script src="/_next/static/chunks/153-7b7799fa9c3cf695.js" defer=""></script><script src="/_next/static/chunks/3819-1a3e29d18dc3e573.js" defer=""></script><script src="/_next/static/chunks/504-1a3fdb965476a154.js" defer=""></script><script src="/_next/static/chunks/pages/home-e33eee29971e701b.js" defer=""></script><script src="/_next/static/J2dhssoYK8tVrkfJD7GUX/_buildManifest.js" defer=""></script><script src="/_next/static/J2dhssoYK8tVrkfJD7GUX/_ssgManifest.js" defer=""></script><style data-styled="active" data-styled-version="6.1.12"></style><script src="/_vercel/insights/script.js" defer="" data-sdkn="@vercel/analytics/react" data-sdkv="1.3.1"></script><style>
          .woot-widget-holder {
            width: 320px !important;
            height: 400px !important;
          }
          .woot-widget-holder iframe {
            width: 320px !important;
            height: 400px !important;
          }
          .woot-widget-bubble {
            width: 50px !important;
            height: 50px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
          }
          .woot-widget-bubble svg,
          .woot-widget-bubble img,
          .woot-widget-bubble .woot-widget-bubble__icon {
            margin: 0 !important;
            padding: 0 !important;
            position: relative !important;
            top: auto !important;
            left: auto !important;
            transform: none !important;
          }
          .woot-widget-bubble--close,
          .woot-widget-bubble--close-button {
            width: 50px !important;
            height: 50px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
          }
          .woot-widget-bubble--close svg,
          .woot-widget-bubble--close img,
          .woot-widget-bubble--close .woot-widget-bubble__icon,
          .woot-widget-bubble--close-button svg,
          .woot-widget-bubble--close-button img,
          .woot-widget-bubble--close-button .woot-widget-bubble__icon {
            margin: 0 !important;
            padding: 0 !important;
            position: relative !important;
            top: auto !important;
            left: auto !important;
            transform: none !important;
            width: 20px !important;
            height: 20px !important;
          }
          .woot-widget-bubble--close .woot-widget-bubble__close-button,
          .woot-widget-bubble--close-button {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 100% !important;
            height: 100% !important;
            border: none !important;
            background: transparent !important;
          }
          .woot--close::before,
          .woot--close::after {
            left: 50% !important;
            top: 50% !important;
            transform: translate(-50%, -50%) rotate(45deg) !important;
          }
          .woot--close::after {
            transform: translate(-50%, -50%) rotate(-45deg) !important;
          }
        </style><script type="text/javascript" async="" src="https://googleads.g.doubleclick.net/pagead/viewthroughconversion/16696812396/?random=1754412865923&amp;cv=11&amp;fst=1754412865923&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=gtag.config&amp;gtm=45be5840v9195436136za200zd9195436136xec&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=101509157~103116026~103200004~103233427~104527907~104528501~104684208~104684211~104948813~105087538~105087540~105103161~105103163~105113532&amp;u_w=1661&amp;u_h=1053&amp;url=https%3A%2F%2Fdashboard.exa.ai%2Fhome&amp;ref=https%3A%2F%2Fdashboard.exa.ai%2Flogin%3Femail%3Dcassandra579%2540ai.whatisinitfor.me%26otp%3D031704%26redirect%3Dhttps%253A%252F%252Fdashboard.exa.ai%252F&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=Home%20%7C%20Exa%20API&amp;npa=0&amp;pscdl=noapi&amp;auid=910274139.1754412798&amp;uaa=x86&amp;uab=64&amp;uafvl=%2522Edge%2522%3B135.0.7049.95%7CNot-A.Brand%3B8.0.0.0%7CChromium%3B135.0.7049.95&amp;uamb=0&amp;uam=&amp;uap=macOS&amp;uapv=%2215.2.0%22&amp;uaw=0&amp;fledge=1&amp;data=event%3Dgtag.config&amp;rfmt=3&amp;fmt=4"></script><script type="text/javascript" async="" src="https://googleads.g.doubleclick.net/pagead/viewthroughconversion/16860319264/?random=1754412865981&amp;cv=11&amp;fst=1754412865981&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=gtag.config&amp;gtm=45be5840v9210115056z89195906795za200zd9210115056xec&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=101509157~103116026~103200004~103233427~104527907~104528501~104684208~104684211~104948813~105087538~105087540~105103161~105103163&amp;u_w=1661&amp;u_h=1053&amp;url=https%3A%2F%2Fdashboard.exa.ai%2Fhome&amp;ref=https%3A%2F%2Fdashboard.exa.ai%2Flogin%3Femail%3Dcassandra579%2540ai.whatisinitfor.me%26otp%3D031704%26redirect%3Dhttps%253A%252F%252Fdashboard.exa.ai%252F&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=Home%20%7C%20Exa%20API&amp;npa=0&amp;pscdl=noapi&amp;auid=910274139.1754412798&amp;uaa=x86&amp;uab=64&amp;uafvl=%2522Edge%2522%3B135.0.7049.95%7CNot-A.Brand%3B8.0.0.0%7CChromium%3B135.0.7049.95&amp;uamb=0&amp;uam=&amp;uap=macOS&amp;uapv=%2215.2.0%22&amp;uaw=0&amp;fledge=1&amp;data=event%3Dgtag.config&amp;rfmt=3&amp;fmt=4"></script><link as="script" rel="prefetch" href="/_next/static/chunks/262-dcd81308dbb7bb75.js"><link as="script" rel="prefetch" href="/_next/static/chunks/4886-c589f54f7bf1ae2b.js"><link as="script" rel="prefetch" href="/_next/static/chunks/7541-50e4665de5810762.js"><link as="script" rel="prefetch" href="/_next/static/chunks/7068-ed23f19d6095de95.js"><link as="script" rel="prefetch" href="/_next/static/chunks/3663-6bf08025037234b7.js"><link as="script" rel="prefetch" href="/_next/static/chunks/9089-74425baed342dd09.js"><link as="script" rel="prefetch" href="/_next/static/chunks/pages/playground/%5Bform%5D-f258f32fe49ebb8c.js"><link as="script" rel="prefetch" href="/_next/static/chunks/3061-c76761c0014ff03f.js"><link as="script" rel="prefetch" href="/_next/static/chunks/9623-d0816276e0ae056a.js"><link as="script" rel="prefetch" href="/_next/static/chunks/6535-27b62a02a892ca0e.js"><link as="script" rel="prefetch" href="/_next/static/chunks/8347-00df2172faa3a56e.js"><link as="script" rel="prefetch" href="/_next/static/chunks/pages/usage-a3b01499ad040563.js"><link as="script" rel="prefetch" href="/_next/static/chunks/8821-6ceb47378410bf76.js"><link as="script" rel="prefetch" href="/_next/static/chunks/4955-76bdbb0567f01c10.js"><link as="script" rel="prefetch" href="/_next/static/chunks/1580-a5dee54e8b981950.js"><link as="script" rel="prefetch" href="/_next/static/chunks/pages/billing-1ee56f6865d826d7.js"><link as="script" rel="prefetch" href="/_next/static/chunks/pages/api-keys-4efffef319f5a5cd.js"><link as="script" rel="prefetch" href="/_next/static/chunks/pages/team-settings-a0ba13f0a9e4650c.js"><link as="script" rel="prefetch" href="/_next/static/chunks/5495-6f6d4bcad275be3d.js"><link as="script" rel="prefetch" href="/_next/static/chunks/pages/websets-3b7ae17b0b54d490.js"><link as="script" rel="prefetch" href="/_next/static/chunks/pages/examples-library-d3e1da053b341251.js"><link as="script" rel="prefetch" href="/_next/static/chunks/pages/docs-65e8e5d0754d438d.js"><script src="https://js.stripe.com/v3"></script><script type="text/javascript" async="" src="https://api.commandbar.com/latest/83fe2cea?version=2"></script><script type="text/javascript" async="" src="https://googleads.g.doubleclick.net/pagead/viewthroughconversion/16860319264/?random=1754412876307&amp;cv=11&amp;fst=1754412876307&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=page_view&amp;gtm=45be5840v9210115056za200zd9210115056xec&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=101509157~103116026~103200004~103233427~104527907~104528501~104684208~104684211~104948813~105087538~105087540~105103161~105103163&amp;u_w=1661&amp;u_h=1053&amp;url=https%3A%2F%2Fdashboard.exa.ai%2Fplayground%2Fsearch%3Fq%3Dblog%2520post%2520about%2520AI%26filters%3D%257B%2522text%2522%253A%2522true%2522%252C%2522type%2522%253A%2522auto%2522%257D&amp;ref=https%3A%2F%2Fdashboard.exa.ai%2Fhome&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=Playground%20%7C%20Exa%20API&amp;npa=0&amp;pscdl=noapi&amp;auid=910274139.1754412798&amp;uaa=x86&amp;uab=64&amp;uafvl=%2522Edge%2522%3B135.0.7049.95%7CNot-A.Brand%3B8.0.0.0%7CChromium%3B135.0.7049.95&amp;uamb=0&amp;uam=&amp;uap=macOS&amp;uapv=%2215.2.0%22&amp;uaw=0&amp;fledge=1&amp;data=event%3Dpage_view&amp;rfmt=3&amp;fmt=4"></script></head><body><noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KLW63SD9" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript><div id="__next"><div data-sentry-element="unknown" data-sentry-component="Layout" data-sentry-source-file="Layout.tsx" class="Layout__Container-sc-c48799f0-0 iwyHEf"><main data-sentry-element="unknown" data-sentry-source-file="Layout.tsx" class="Layout__Content-sc-c48799f0-1 fJLNDJ"><div data-sentry-element="PlaygroundLayoutContainer" data-sentry-source-file="PlaygroundLayout.tsx" class="StyledComponents__PlaygroundLayoutContainer-sc-12ec547a-0 cquSlC"><div data-sentry-element="unknown" data-sentry-component="FastSearchBanner" data-sentry-source-file="FastSearchBanner.tsx" class="FastSearchBanner-sc-1ed96779-0 hHEZIV"><button data-sentry-element="unknown" data-sentry-source-file="FastSearchBanner.tsx" class="FastSearchBanner-sc-1ed96779-1 kaiIIa"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x " data-sentry-element="X" data-sentry-source-file="FastSearchBanner.tsx"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg></button><a href="https://exa.ai/blog/fastest-search-api" target="_blank" data-sentry-element="unknown" data-sentry-source-file="FastSearchBanner.tsx" class="FastSearchBanner-sc-1ed96779-2 bhqsgL"><span style="margin-right: 4px;">🏎️💨</span>Introducing Exa Fast: The world's fastest search API<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right " data-sentry-element="ArrowUpRight" data-sentry-source-file="FastSearchBanner.tsx" style="margin-left: 4px;"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></a></div><div data-sentry-element="PlaygroundMain" data-sentry-source-file="PlaygroundLayout.tsx" class="StyledComponents__PlaygroundMain-sc-12ec547a-1 gDXsew"><div data-sentry-element="Container" data-sentry-component="MobileNavBar" data-sentry-source-file="MobileNavBar.tsx" class="MobileNavBar__Container-sc-aff0446b-0 jrPbeX"><a data-sentry-element="LogoContainer" data-sentry-source-file="MobileNavBar.tsx" class="MobileNavBar__LogoContainer-sc-aff0446b-1 NFbZZ" href="/home"><svg xmlns="http://www.w3.org/2000/svg" height="16" viewBox="0 0 26 32" preserveAspectRatio="xMidYMid meet" fill="none" data-sentry-element="svg" data-sentry-component="ExaLogo" data-sentry-source-file="Icons.tsx"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.63623 0H25.6544V2.38806L14.9775 16L25.6544 29.6119V32H0.63623V0ZM13.3026 13.7975L21.9788 2.38806H4.62646L13.3026 13.7975ZM3.45099 5.24727V14.806H10.7198L3.45099 5.24727ZM10.7198 17.194H3.45099V26.7527L10.7198 17.194ZM4.62646 29.6119L13.3026 18.2025L21.9788 29.6119H4.62646Z" fill="#1F40ED" data-sentry-element="path" data-sentry-source-file="Icons.tsx"></path></svg></a><div data-sentry-element="RightSection" data-sentry-source-file="MobileNavBar.tsx" class="MobileNavBar__RightSection-sc-aff0446b-2 jsfgns"><div data-sentry-element="UserAvatarContainer" data-sentry-source-file="MobileNavBar.tsx" class="MobileNavBar__UserAvatarContainer-sc-aff0446b-3 iXabCI"><button type="button" id="radix-:r0:" aria-haspopup="menu" aria-expanded="false" data-state="closed" data-sentry-element="unknown" data-sentry-source-file="AuthMenu.tsx" class="AuthMenu-sc-6e3fd213-2 gbSlnl"><div class="cursor-pointer rounded-full bg-[#C3ECFF] w-[24px] h-[24px] min-w-[24px] flex items-center justify-center"><span class="font-medium text-sm text-blue-800"></span></div><p class="AuthMenu-sc-6e3fd213-1 hImAWH text-[15px] font-medium" data-sentry-element="unknown" data-sentry-source-file="AuthMenu.tsx"></p></button></div><button aria-label="Toggle menu" data-sentry-element="HamburgerButton" data-sentry-source-file="MobileNavBar.tsx" class="MobileNavBar__HamburgerButton-sc-aff0446b-4 kLcyOf"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu "><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div data-sentry-element="SidebarWrapper" data-sentry-component="Sidebar" data-sentry-source-file="Sidebar.tsx" class="Sidebar__SidebarWrapper-sc-287785de-15 hPngcd"><div data-sentry-element="Container" data-sentry-source-file="Sidebar.tsx" class="Sidebar__Container-sc-287785de-5 eYEtMF"><div><div data-sentry-element="TopSectionContent" data-sentry-source-file="Sidebar.tsx" class="Sidebar__TopSectionContent-sc-287785de-16 jPMbN"><a href="/playground" data-sentry-element="unknown" data-sentry-source-file="Sidebar.tsx" class="Sidebar-sc-287785de-13 cEdpDd"><svg xmlns="http://www.w3.org/2000/svg" height="16" viewBox="0 0 26 32" preserveAspectRatio="xMidYMid meet" fill="none" data-sentry-element="svg" data-sentry-component="ExaLogo" data-sentry-source-file="Icons.tsx"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.63623 0H25.6544V2.38806L14.9775 16L25.6544 29.6119V32H0.63623V0ZM13.3026 13.7975L21.9788 2.38806H4.62646L13.3026 13.7975ZM3.45099 5.24727V14.806H10.7198L3.45099 5.24727ZM10.7198 17.194H3.45099V26.7527L10.7198 17.194ZM4.62646 29.6119L13.3026 18.2025L21.9788 29.6119H4.62646Z" fill="#1F40ED" data-sentry-element="path" data-sentry-source-file="Icons.tsx"></path></svg></a><div data-sentry-element="TeamSelectorWrapper" data-sentry-source-file="Sidebar.tsx" class="Sidebar__TeamSelectorWrapper-sc-287785de-17 ARWYm"><div data-sentry-element="unknown" data-sentry-component="TeamSelector" data-sentry-source-file="TeamSelector.tsx" class="TeamSelector-sc-229744ca-0 cdDvCz"><button type="button" id="radix-:r5:" aria-haspopup="menu" aria-expanded="false" data-state="closed" class="TeamSelector-sc-229744ca-2 eqFaVc command-bar-team-selector-trigger" data-sentry-element="unknown" data-sentry-source-file="TeamSelector.tsx"><p class="Text__Base-sc-66499d49-2 TeamSelector-sc-229744ca-3 dWuVca jcmfrN">Personal</p><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevrons-up-down " data-sentry-element="ChevronsUpDown" data-sentry-source-file="TeamSelector.tsx" style="flex-shrink: 0; color: var(--text-light-muted);"><path d="m7 15 5 5 5-5"></path><path d="m7 9 5-5 5 5"></path></svg></button></div></div></div><div class="Sidebar__NavSectionStyled-sc-287785de-6 bikcQC"><div class="Sidebar__NavSectionTitleStyled-sc-287785de-7 pOPdS"></div><a title="Home" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR" href="/home"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-home "><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Home</span></a></div><div class="Sidebar__NavSectionStyled-sc-287785de-6 dBmdy"><div class="Sidebar__NavSectionTitleStyled-sc-287785de-7 pOPdS">API Playground</div><a class="Sidebar__NavItemStyled-sc-287785de-8 gCwIQn command-bar-nav-search" title="Search" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" href="/playground/search"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search "><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Search</span></a><a class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR command-bar-nav-content" title="Crawling" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" href="/playground/get-contents"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bug "><path d="m8 2 1.88 1.88"></path><path d="M14.12 3.88 16 2"></path><path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1"></path><path d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6"></path><path d="M12 20v-9"></path><path d="M6.53 9C4.6 8.8 3 7.1 3 5"></path><path d="M6 13H2"></path><path d="M3 21c0-2.1 1.7-3.9 3.8-4"></path><path d="M20.97 5c0 2.1-1.6 3.8-3.5 4"></path><path d="M22 13h-4"></path><path d="M17.2 17c2.1.1 3.8 1.9 3.8 4"></path></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Crawling</span></a><a class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR command-bar-nav-answer" title="Answer" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" href="/playground/answer"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle-more "><path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path><path d="M8 12h.01"></path><path d="M12 12h.01"></path><path d="M16 12h.01"></path></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Answer</span></a><a class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR command-bar-nav-research" title="Research" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" href="/playground/research"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-microscope "><path d="M6 18h8"></path><path d="M3 22h18"></path><path d="M14 22a7 7 0 1 0 0-14h-1"></path><path d="M9 14h2"></path><path d="M9 12a2 2 0 0 1-2-2V6h6v4a2 2 0 0 1-2 2Z"></path><path d="M12 6V3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3"></path></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Research</span></a></div><div class="Sidebar__NavSectionStyled-sc-287785de-6 dBmdy"><div class="Sidebar__NavSectionTitleStyled-sc-287785de-7 pOPdS">Management</div><a class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR command-bar-nav-usage" title="Usage" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" href="/usage"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bar-chart3 "><path d="M3 3v18h18"></path><path d="M18 17V9"></path><path d="M13 17V5"></path><path d="M8 17v-3"></path></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Usage</span></a><a class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR command-bar-nav-billing" title="Billing" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" href="/billing"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-wallet "><path d="M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1"></path><path d="M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4"></path></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Billing</span></a><a class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR command-bar-nav-api-key" title="API Keys" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" href="/api-keys"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-key "><circle cx="7.5" cy="15.5" r="5.5"></circle><path d="m21 2-9.6 9.6"></path><path d="m15.5 7.5 3 3L22 7l-3-3"></path></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">API Keys</span></a><a class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR command-bar-nav-team-settings" title="Team Settings" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" href="/team-settings"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-round-plus "><path d="M2 21a8 8 0 0 1 13.292-6"></path><circle cx="10" cy="8" r="5"></circle><path d="M19 16v6"></path><path d="M22 19h-6"></path></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Team Settings</span></a><a class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR command-bar-nav-team-settings" title="Websets" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" href="/websets"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-table-properties "><path d="M15 3v18"></path><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M21 9H3"></path><path d="M21 15H3"></path></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Websets</span></a></div><div class="Sidebar__NavSectionStyled-sc-287785de-6 dBmdy"><div class="Sidebar__NavSectionTitleStyled-sc-287785de-7 pOPdS">Learn</div><a class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR command-bar-nav-examples-library" title="Examples Library" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" href="/examples-library"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-marked "><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path><polyline points="10 2 10 10 13 7 16 10 16 2"></polyline></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Examples Library</span></a><a target="_blank" title="Docs" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR" href="/docs"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-text "><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path><path d="M8 7h6"></path><path d="M8 11h8"></path></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Docs</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right " style="color: var(--text-light-muted); margin-left: auto; opacity: 1; width: 14px; overflow: hidden; transition: opacity 0.2s ease-in-out, width 0.2s ease-in-out 0.05s; pointer-events: auto; display: flex; align-items: center; flex-shrink: 0;"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></a><a target="_blank" title="Demos" data-sentry-element="NavItemStyled" data-sentry-source-file="Sidebar.tsx" class="Sidebar__NavItemStyled-sc-287785de-8 bMhOdR" href="https://exa.ai/demos"><span style="display: flex; align-items: center; flex-shrink: 0;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-blocks "><rect width="7" height="7" x="14" y="3" rx="1"></rect><path d="M10 21V8a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1H3"></path></svg></span><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 24px; display: flex; align-items: center; opacity: 1; max-width: 200px; transition: opacity 0.2s ease-in-out, max-width 0.2s ease-in-out 0.05s; flex-shrink: 0;">Demos</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right " style="color: var(--text-light-muted); margin-left: auto; opacity: 1; width: 14px; overflow: hidden; transition: opacity 0.2s ease-in-out, width 0.2s ease-in-out 0.05s; pointer-events: auto; display: flex; align-items: center; flex-shrink: 0;"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></a></div></div><div data-sentry-element="BottomSection" data-sentry-source-file="Sidebar.tsx" class="Sidebar__BottomSection-sc-287785de-10 iyoGvw"><div data-sentry-element="CollapsibleWrapper" data-sentry-source-file="Sidebar.tsx" class="Sidebar__CollapsibleWrapper-sc-287785de-12 fZGlpS"><button type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:r7:" data-state="closed" class="rounded-[6px] bg-[#F5F8FB] border border-[#EBF3FA] text-[#60646C] font-['ABC_Diatype_Unlicensed_Trial'] text-[14px] font-normal cursor-pointer transition-colors duration-200 px-3 py-2 w-full hover:bg-[#EBF3FA]" data-sentry-element="unknown" data-sentry-source-file="FeedbackComponent.tsx"><p class="break-words whitespace-normal leading-[19px] text-left antialiased">Give us feedback</p></button></div><div data-sentry-element="FeedbackDivider" data-sentry-source-file="Sidebar.tsx" class="Sidebar__FeedbackDivider-sc-287785de-18 cVBuNL"></div><button type="button" id="radix-:r8:" aria-haspopup="menu" aria-expanded="false" data-state="closed" data-sentry-element="unknown" data-sentry-source-file="AuthMenu.tsx" class="AuthMenu-sc-6e3fd213-2 gbSlnl"><div class="cursor-pointer rounded-full bg-[#C3ECFF] w-[24px] h-[24px] min-w-[24px] flex items-center justify-center"><span class="font-medium text-sm text-blue-800"></span></div><p class="AuthMenu-sc-6e3fd213-1 hImAWH text-[15px] font-medium" data-sentry-element="unknown" data-sentry-source-file="AuthMenu.tsx"></p></button></div></div><button title="Collapse" data-sentry-element="CollapseButton" data-sentry-source-file="Sidebar.tsx" class="Sidebar__CollapseButton-sc-287785de-14 beBTec"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-minus " style="transform: rotate(90deg);"><path d="M5 12h14"></path></svg></button></div><div data-sentry-element="PlaygroundMainContent" data-sentry-source-file="PlaygroundLayout.tsx" class="StyledComponents__PlaygroundMainContent-sc-12ec547a-2 gXjakY"><div data-sentry-element="PlaygroundContentContainer" data-sentry-source-file="[form].tsx" class="StyledComponents__PlaygroundContentContainer-sc-12ec547a-4 dNMncP"><div data-sentry-element="LeftContainer" data-sentry-source-file="[form].tsx" class="StyledComponents__LeftContainer-sc-12ec547a-5 Esbpb" style="width: 50%; min-width: 400px;"><div data-sentry-element="FormContainer" data-sentry-component="CommonPlaygroundForm" data-sentry-source-file="CommonForm.tsx" class="StyledComponents__FormContainer-sc-3d22e631-4 jxbTCy"><div data-sentry-element="unknown" data-sentry-source-file="CommonForm.tsx" class="StyledComponents__HeaderContainer-sc-12ec547a-23 kcidKs"><div class="flex flex-col md:flex-row md:items-baseline md:justify-between w-full"><div class="flex flex-col items-baseline w-full"><h2 data-sentry-element="unknown" data-sentry-source-file="CommonForm.tsx" class="StyledComponents__HeaderTitle-sc-12ec547a-24 eRiJHN">Search</h2><p data-sentry-element="unknown" data-sentry-source-file="CommonForm.tsx" class="StyledComponents__HeaderDescription-sc-12ec547a-25 gcNEWJ">Return results and their contents</p></div><div class="flex gap-1.5 mt-3 md:mt-0 md:ml-4 md:flex-shrink-0"><button data-sentry-element="DocsButton" data-sentry-source-file="CommonForm.tsx" class="StyledComponents__DocsButton-sc-3d22e631-8 bIVxZs"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-text " data-sentry-element="BookText" data-sentry-source-file="CommonForm.tsx"><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path><path d="M8 7h6"></path><path d="M8 11h8"></path></svg>Docs</button></div></div></div><div data-sentry-element="PlaygroundContainer" data-sentry-source-file="CommonForm.tsx" class="StyledComponents__PlaygroundContainer-sc-12ec547a-3 fINWZP"><div data-sentry-element="unknown" data-sentry-component="SearchBox" data-sentry-source-file="SearchBox.tsx" class="StyledComponents__Root-sc-3d22e631-27 dntkSH"><div data-sentry-element="unknown" data-sentry-source-file="SearchBox.tsx" class="StyledComponents__SearchBar-sc-3d22e631-29 bTcmuP"><div class="flex flex-col gap-1 w-full"><div class="flex gap-2 mb-4 relative" style="width: 100%;"><div class="absolute bottom-0 left-0 right-0" style="height: 1px; background-color: rgb(229, 229, 229);"></div><button type="button" class="px-2 py-1.5 pl-1.5 text-[15px] transition-all duration-100 flex items-center gap-1.5 relative text-[#1A86F3]"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search "><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>Search<div class="absolute bottom-0 left-0 right-0" style="height: 1.5px; background-color: rgb(26, 134, 243);"></div></button><button type="button" class="px-2 py-1.5 pl-1.5 text-[15px] transition-all duration-100 flex items-center gap-1.5 relative text-gray-700 hover:text-gray-900"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layers "><path d="m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z"></path><path d="m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65"></path><path d="m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65"></path></svg>Find Similar Pages</button></div><span data-sentry-element="unknown" data-sentry-source-file="SearchBox.tsx" class="StyledComponents__QueryLabel-sc-3d22e631-31 cUtofl">Query</span><div class="flex flex-col gap-2 w-full rounded-[12px] p-[10px] transition-all duration-100" data-search-box="true" style="box-shadow: rgba(32, 36, 61, 0.02) 0px 7px 9px 0px, rgba(32, 36, 61, 0.03) 0px 3px 7px 0px, rgba(32, 36, 61, 0.03) 0px 1px 4px 0px; border: 1px solid var(--brand-default); display: flex; flex-direction: column; min-height: 40px;"><textarea placeholder="Enter a query" rows="1" data-sentry-element="unknown" data-sentry-source-file="SearchBox.tsx" class="StyledComponents__Input-sc-3d22e631-30 fNYVyi" style="flex: 1 1 auto; margin-bottom: 0px; height: 40px;">blog post about AI</textarea><div class="flex justify-between items-center"><div data-sentry-element="unknown" data-sentry-source-file="SearchBox.tsx" class="StyledComponents__Container-sc-3d22e631-35 kXnJVD"><div data-sentry-element="unknown" data-sentry-source-file="SearchBox.tsx" class="StyledComponents__List-sc-3d22e631-37 iaVWWi"><div style="position: relative;"><button data-has-dropdown="true" data-sentry-element="unknown" data-sentry-source-file="SearchBox.tsx" class="StyledComponents__Button-sc-3d22e631-38 gVBbiT"><span data-is-default="true">Try an example</span></button></div></div></div><div class="flex items-center gap-2"><button data-sentry-element="unknown" data-sentry-source-file="SearchBox.tsx" class="StyledComponents__ResetButton-sc-3d22e631-34 cKugSq"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-rotate-ccw " data-sentry-element="RotateCcw" data-sentry-source-file="SearchBox.tsx"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path></svg>Clear</button><button id="run-search-search" class="StyledComponents__RunButton-sc-3d22e631-33 iwMYY"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles "><path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path><path d="M5 3v4"></path><path d="M19 17v4"></path><path d="M3 5h4"></path><path d="M17 19h4"></path></svg>Run</button></div></div></div></div></div></div><div data-sentry-element="CollapsibleContainer" data-sentry-source-file="MobileCollapsibleForm.tsx" class="MobileCollapsibleForm__CollapsibleContainer-sc-bf3973d3-0 hscTCG"><button data-collapsed="true" aria-expanded="false" aria-controls="mobile-form-content" data-sentry-element="CollapsibleHeader" data-sentry-source-file="MobileCollapsibleForm.tsx" class="MobileCollapsibleForm__CollapsibleHeader-sc-bf3973d3-1 dlTMkX"><h3 data-sentry-element="CollapsibleTitle" data-sentry-source-file="MobileCollapsibleForm.tsx" class="MobileCollapsibleForm__CollapsibleTitle-sc-bf3973d3-2 hxWBWu">Options</h3><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down "><path d="m6 9 6 6 6-6"></path></svg></button><div id="mobile-form-content" role="region" aria-labelledby="mobile-form-header" data-sentry-element="CollapsibleContent" data-sentry-source-file="MobileCollapsibleForm.tsx" class="MobileCollapsibleForm__CollapsibleContent-sc-bf3973d3-3 bBniDl"><form class="StyledComponents__Form-sc-3d22e631-0 hlqMXx"><div class="StyledComponents__FormFieldsContainer-sc-3d22e631-5 bLoPZl"><div id="Search" class="StyledComponents__Container-sc-3d22e631-25 pIdQE h-full"><div class="StyledComponents__SectionHeader-sc-3d22e631-7 bvVbA"><h2 class="StyledComponents__SectionTitle-sc-3d22e631-23 hiMDYg"></h2></div><div class="StyledComponents__FieldsGroup-sc-3d22e631-9 eYtVaw h-full"><div data-sentry-element="SingleRowField" data-sentry-component="SearchTypeSelectField" data-sentry-source-file="SearchTypeSelectField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Search Type</span><span class="Sidebar__NewTag-sc-287785de-0 crzrNP">NEW</span></div><span class="Label-sc-82986fa0-4 fbFknc"><a href="https://docs.exa.ai/reference/how-exa-search-works" target="_blank" rel="noopener noreferrer" style="color: var(--brand-default); text-decoration: none;">See docs</a> for more details</span></div></label><button type="button" role="combobox" aria-controls="radix-:ra:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" data-sentry-element="SelectTrigger" data-sentry-source-file="SearchTypeSelectField.tsx" class="SearchTypeSelectField__SelectTrigger-sc-3371e654-0 lmtTID"><span data-sentry-element="unknown" data-sentry-source-file="SearchTypeSelectField.tsx" style="pointer-events: none;">Auto</span><span aria-hidden="true" data-sentry-element="unknown" data-sentry-source-file="SearchTypeSelectField.tsx"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down " data-sentry-element="ChevronDown" data-sentry-source-file="SearchTypeSelectField.tsx"><path d="m6 9 6 6 6-6"></path></svg></span></button><select aria-hidden="true" tabindex="-1" style="position: absolute; border: 0px; width: 1px; height: 1px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; overflow-wrap: normal;"></select></div><div data-sentry-element="SingleRowField" data-sentry-component="DropdownField" data-sentry-source-file="DropdownField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Result category</span></div></div></label><div style="width: 100%; position: relative;"><button type="button" role="combobox" aria-controls="radix-:rb:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="DropdownField__SelectTrigger-sc-b0d5501e-0 kptMXs command-bar-search-category" data-sentry-element="SelectTrigger" data-sentry-source-file="DropdownField.tsx"><span data-sentry-element="unknown" data-sentry-source-file="DropdownField.tsx" style="pointer-events: none;"><span data-sentry-element="SelectValue" data-sentry-source-file="DropdownField.tsx" class="DropdownField__SelectValue-sc-b0d5501e-1 jrgpve"></span></span><span aria-hidden="true" data-sentry-element="unknown" data-sentry-source-file="DropdownField.tsx"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down DropdownField__SelectIcon-sc-b0d5501e-2 hzQcBN" data-sentry-element="SelectIcon" data-sentry-source-file="DropdownField.tsx"><path d="m6 9 6 6 6-6"></path></svg></span></button><select aria-hidden="true" tabindex="-1" style="position: absolute; border: 0px; width: 1px; height: 1px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; overflow-wrap: normal;"><option value="-" selected="">---</option><option value="company">Company</option><option value="research paper">Research Paper</option><option value="news">News Article</option><option value="pdf">PDF</option><option value="github">Github</option><option value="personal site">Personal Site</option><option value="linkedin profile">LinkedIn Profile</option><option value="financial report">Financial Report</option></select></div></div><div data-sentry-element="SingleRowField" data-sentry-component="TextField" data-sentry-source-file="TextField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>User location</span></div><span class="Label-sc-82986fa0-4 fbFknc">Two-letter <a target="_blank" rel="noopener noreferrer" href="https://en.wikipedia.org/wiki/ISO_3166-1#Codes">ISO country code</a> (e.g., US, GB, CA)</span></div></label><input variant="text" placeholder="Enter country code" data-sentry-element="InputContainer" data-sentry-source-file="TextField.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 sehgj" value="" style=""></div><div data-sentry-element="SingleRowField" data-sentry-component="NumericField" data-sentry-source-file="NumericField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Number of results</span></div><span class="Label-sc-82986fa0-4 fbFknc"><a href="mailto:<EMAIL>">Contact us</a> for more than 100 results</span></div></label><div style="display: flex; gap: 8px; align-items: center;"><input type="text" inputmode="numeric" pattern="[0-9,]*" min="0" max="1000" placeholder="Default: 10, max: 100" data-sentry-element="BaseNumericTextInput" data-sentry-source-file="NumericField.tsx" class="StyledComponents__TextInput-sc-3d22e631-16 StyledComponents__BaseNumericTextInput-sc-3d22e631-18 hXSXOw gAmUFC" value="" style=""></div></div></div></div><div id="Filters" class="StyledComponents__Container-sc-3d22e631-25 pIdQE"><div class="StyledComponents__SectionHeader-sc-3d22e631-7 bvVbA"><h2 class="StyledComponents__SectionTitle-sc-3d22e631-23 hiMDYg">Filters</h2></div><div class="StyledComponents__FieldsGroup-sc-3d22e631-9 eYtVaw"><div data-sentry-element="SingleRowField" data-sentry-component="DateRangeField" data-sentry-source-file="DateRangeField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Published date range</span></div></div></label><div variant="date" data-sentry-element="InputContainer" data-sentry-source-file="DateRangeField.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 hbWlUI"><button class="peer flex w-full cursor-pointer appearance-none items-center gap-x-2 truncated outline-none transition-all h-full bg-transparent text-gray-900 placeholder-secondary-accent2x disabled:pointer-events-none disabled:bg-gray-100 disabled:text-gray-400" data-sentry-element="Trigger" data-sentry-source-file="DatePickerRaw.tsx" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:rc:" data-state="closed"><span class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap text-left text-gray-900 "> - </span><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar " style="color: var(--text-light-muted);"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg></button></div></div><div data-sentry-element="SingleRowField" data-sentry-component="DateRangeField" data-sentry-source-file="DateRangeField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Crawl date range</span></div><span class="Label-sc-82986fa0-4 fbFknc">Return results Exa crawled within this range</span></div></label><div variant="date" data-sentry-element="InputContainer" data-sentry-source-file="DateRangeField.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 hbWlUI"><button class="peer flex w-full cursor-pointer appearance-none items-center gap-x-2 truncated outline-none transition-all h-full bg-transparent text-gray-900 placeholder-secondary-accent2x disabled:pointer-events-none disabled:bg-gray-100 disabled:text-gray-400" data-sentry-element="Trigger" data-sentry-source-file="DatePickerRaw.tsx" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:rd:" data-state="closed"><span class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap text-left text-gray-900 "> - </span><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar " style="color: var(--text-light-muted);"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg></button></div></div><div data-sentry-element="unknown" data-sentry-component="DomainFilter" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-0 kXMlMi"><div data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-1 hkINIl"><div data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-2 hNwLJW"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Domain filter</span></div><span class="Label-sc-82986fa0-4 fbFknc">Base and subdomains only</span></div></label></div><div data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-3 QlrHM"><div data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-4 iazXwF"><div dir="ltr" data-orientation="horizontal" data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx"><div role="tablist" aria-orientation="horizontal" data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-6 bNcsAf" tabindex="0" data-orientation="horizontal" style="outline: none;"><button type="button" role="tab" aria-selected="true" aria-controls="radix-:re:-content-include" data-state="active" id="radix-:re:-trigger-include" class="Tabs__TabsTrigger-sc-7a676f08-1 hqoIyX" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Include</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:re:-content-exclude" data-state="inactive" id="radix-:re:-trigger-exclude" class="Tabs__TabsTrigger-sc-7a676f08-1 hqoIyX" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Exclude</button></div></div><div data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-5 dTFZiu"><input variant="text" placeholder="Enter comma separated domains" data-sentry-element="InputContainer" data-sentry-source-file="DomainFilter.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 sehgj" value="" style=""></div></div></div></div></div><div data-sentry-element="SingleRowField" data-sentry-component="TextField" data-sentry-source-file="TextField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Include text</span></div></div></label><input variant="text" placeholder="Enter phrase up to 5 words" data-sentry-element="InputContainer" data-sentry-source-file="TextField.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 sehgj" value="" style=""></div><div data-sentry-element="SingleRowField" data-sentry-component="TextField" data-sentry-source-file="TextField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Exclude text</span></div></div></label><input variant="text" placeholder="Enter phrase up to 5 words" data-sentry-element="InputContainer" data-sentry-source-file="TextField.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 sehgj" value="" style=""></div></div></div><div id="Crawling" class="StyledComponents__Container-sc-3d22e631-25 pIdQE"><div class="StyledComponents__SectionHeader-sc-3d22e631-7 bvVbA"><h2 class="StyledComponents__SectionTitle-sc-3d22e631-23 hiMDYg">Crawling</h2></div><div class="StyledComponents__FieldsGroup-sc-3d22e631-9 eYtVaw"><div class="StyledComponents__ContentsField-sc-3d22e631-3 cCTCTp"><div class="flex items-center justify-between"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Context</span></div><span class="Label-sc-82986fa0-4 fbFknc">Return page contents as a context string for LLM RAG</span></div></label><button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" data-sentry-element="SwitchRoot" data-sentry-component="Switch" data-sentry-source-file="Switch.tsx" class="Switch__SwitchRoot-sc-f60b4044-0 iQrnCV"><span data-state="unchecked" data-sentry-element="SwitchThumb" data-sentry-source-file="Switch.tsx" class="Switch__SwitchThumb-sc-f60b4044-1 ijmnNR"></span></button><input type="checkbox" aria-hidden="true" tabindex="-1" value="on" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 0px; height: 0px;"></div></div><div data-sentry-element="FieldContainer" data-sentry-component="LivecrawlField" data-sentry-source-file="LivecrawlField.tsx" class="LivecrawlField__FieldContainer-sc-bc2a0696-5 kFEEhd"><div data-sentry-element="LabelSection" data-sentry-source-file="LivecrawlField.tsx" class="LivecrawlField__LabelSection-sc-bc2a0696-6 fjlkKY"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Livecrawl strategy</span></div><span class="Label-sc-82986fa0-4 fbFknc">How we fetch website contents at search time</span></div></label></div><div data-sentry-element="SelectSection" data-sentry-source-file="LivecrawlField.tsx" class="LivecrawlField__SelectSection-sc-bc2a0696-7 fvRxFd"><button type="button" role="combobox" aria-controls="radix-:rh:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" data-sentry-element="SelectTrigger" data-sentry-source-file="LivecrawlField.tsx" class="LivecrawlField__SelectTrigger-sc-bc2a0696-0 hmqknB"><span data-sentry-element="unknown" data-sentry-source-file="LivecrawlField.tsx" style="pointer-events: none;">Fallback</span><span aria-hidden="true" data-sentry-element="unknown" data-sentry-source-file="LivecrawlField.tsx"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down " data-sentry-element="ChevronDown" data-sentry-source-file="LivecrawlField.tsx"><path d="m6 9 6 6 6-6"></path></svg></span></button><select aria-hidden="true" tabindex="-1" style="position: absolute; border: 0px; width: 1px; height: 1px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; overflow-wrap: normal;"></select></div></div><div data-sentry-element="TimeoutFieldContainer" data-sentry-source-file="Contents.tsx" class="Contents__TimeoutFieldContainer-sc-9206aa93-0 bOCKoW"><div data-sentry-element="TimeoutLabelSection" data-sentry-source-file="Contents.tsx" class="Contents__TimeoutLabelSection-sc-9206aa93-1 eZQxIV"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Timeout</span><div class="group cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info ml-1.5 text-gray-400 hover:text-gray-600 transition-colors duration-100"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg><div class="absolute z-10 opacity-0 scale-95 transform transition-all duration-200 ease-out pointer-events-none      rounded-[4px] p-[2px] flex items-center gap-1 max-w-[400px] min-w-[250px]      group-hover:opacity-100 group-hover:scale-100 group-hover:translate-y-0 group-hover:pointer-events-auto      left-1/2 -translate-x-1/2 text-left top-full mt-4 -translate-y-2 rounded-[2px] bg-gray-900 px-3 py-2 text-sm font-medium text-white" data-sentry-component="NewTooltip" data-sentry-source-file="NewTooltip.tsx" style="box-shadow: rgba(32, 67, 61, 0.01) 0px 14px 6px 0px, rgba(32, 67, 61, 0.04) 0px 8px 5px 0px, rgba(32, 67, 61, 0.08) 0px 4px 4px 0px, rgba(32, 67, 61, 0.09) 0px 1px 2px 0px;">Maximum time to wait for live crawling before giving up</div></div></div><span class="Label-sc-82986fa0-4 fbFknc">Max wait time (ms) before stopping livecrawl</span></div></label></div><div data-sentry-element="TimeoutInputSection" data-sentry-source-file="Contents.tsx" class="Contents__TimeoutInputSection-sc-9206aa93-2 jJPTrc"><div data-sentry-element="SingleRowField" data-sentry-component="NumericField" data-sentry-source-file="NumericField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi" style="flex: 1 1 100%;"><div style="display: flex; gap: 8px; align-items: center;"><input type="text" inputmode="numeric" pattern="[0-9,]*" min="0" max="10000" placeholder="Max: 10000ms" data-sentry-element="BaseNumericTextInput" data-sentry-source-file="NumericField.tsx" class="StyledComponents__TextInput-sc-3d22e631-16 StyledComponents__BaseNumericTextInput-sc-3d22e631-18 hXSXOw gAmUFC" value="" style=""></div></div></div></div><div data-sentry-element="ContentsField" data-sentry-component="TextContentField" data-sentry-source-file="TextContentField.tsx" class="StyledComponents__ContentsField-sc-3d22e631-3 cCTCTp"><div class="flex items-center justify-between"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Full webpage text</span></div><span class="Label-sc-82986fa0-4 fbFknc">Return for every result, including for subpages</span></div></label><button type="button" role="switch" aria-checked="true" data-state="checked" value="on" data-sentry-element="SwitchRoot" data-sentry-component="Switch" data-sentry-source-file="Switch.tsx" class="Switch__SwitchRoot-sc-f60b4044-0 iQrnCV"><span data-state="checked" data-sentry-element="SwitchThumb" data-sentry-source-file="Switch.tsx" class="Switch__SwitchThumb-sc-f60b4044-1 ijmnNR"></span></button><input type="checkbox" aria-hidden="true" tabindex="-1" value="on" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 0px; height: 0px;" checked=""></div><div class="TextContentField__MaxCharactersFieldContainer-sc-484e11-0 laTHJJ"><div class="TextContentField__MaxCharactersLabelSection-sc-484e11-1 kLcsZy"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Max characters per result</span></div></div></label></div><div class="TextContentField__MaxCharactersInputSection-sc-484e11-2 bGiGuf"><div data-sentry-element="SingleRowField" data-sentry-component="NumericField" data-sentry-source-file="NumericField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi" style="flex: 1 1 100%;"><div style="display: flex; gap: 8px; align-items: center;"><input type="text" inputmode="numeric" pattern="[0-9,]*" min="0" placeholder="No default, enter a number" data-sentry-element="BaseNumericTextInput" data-sentry-source-file="NumericField.tsx" class="StyledComponents__TextInput-sc-3d22e631-16 StyledComponents__BaseNumericTextInput-sc-3d22e631-18 hXSXOw gAmUFC" value="" style=""></div></div></div></div><div class="flex items-center justify-between"><div data-sentry-element="HorizontalFieldContainer" data-sentry-component="CheckboxField" data-sentry-source-file="Checkbox.tsx" class="StyledComponents__HorizontalFieldContainer-sc-3d22e631-15 jsxcIy"><div class="flex flex-1 flex-col"><div class="flex flex-row items-center gap-2"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Include HTML tags</span></div></div></label><div data-sentry-element="CheckboxContainer" data-sentry-source-file="Checkbox.tsx" class="Checkbox__CheckboxContainer-sc-353bfc51-1 guKFeb"><input type="checkbox" data-sentry-element="CheckboxStyled" data-sentry-source-file="Checkbox.tsx" class="Checkbox__CheckboxStyled-sc-353bfc51-2 btDvgj" style=""></div></div></div></div></div></div><div data-sentry-element="ContentsField" data-sentry-component="SummaryField" data-sentry-source-file="SummaryField.tsx" class="StyledComponents__ContentsField-sc-3d22e631-3 cCTCTp"><div class="flex items-center justify-between"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>AI page summary</span></div></div></label><button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" data-sentry-element="SwitchRoot" data-sentry-component="Switch" data-sentry-source-file="Switch.tsx" class="Switch__SwitchRoot-sc-f60b4044-0 iQrnCV"><span data-state="unchecked" data-sentry-element="SwitchThumb" data-sentry-source-file="Switch.tsx" class="Switch__SwitchThumb-sc-f60b4044-1 ijmnNR"></span></button><input type="checkbox" aria-hidden="true" tabindex="-1" value="on" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 0px; height: 0px;"></div></div><div data-sentry-element="ContentsField" data-sentry-component="SubpagesField" data-sentry-source-file="SubpagesField.tsx" class="StyledComponents__ContentsField-sc-3d22e631-3 cCTCTp"><div class="flex items-center justify-between"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Subpages</span></div><span class="Label-sc-82986fa0-4 fbFknc">Include subpages (e.g. about, faq, pricing)</span></div></label><button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" data-sentry-element="SwitchRoot" data-sentry-component="Switch" data-sentry-source-file="Switch.tsx" class="Switch__SwitchRoot-sc-f60b4044-0 iQrnCV"><span data-state="unchecked" data-sentry-element="SwitchThumb" data-sentry-source-file="Switch.tsx" class="Switch__SwitchThumb-sc-f60b4044-1 ijmnNR"></span></button><input type="checkbox" aria-hidden="true" tabindex="-1" value="on" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 0px; height: 0px;"></div></div><div data-sentry-element="ContentsField" data-sentry-component="LinksField" data-sentry-source-file="LinksField.tsx" class="StyledComponents__ContentsField-sc-3d22e631-3 cCTCTp"><div class="flex items-center justify-between"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Links</span></div><span class="Label-sc-82986fa0-4 fbFknc">Include links found in the webpages</span></div></label><button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" data-sentry-element="SwitchRoot" data-sentry-component="Switch" data-sentry-source-file="Switch.tsx" class="Switch__SwitchRoot-sc-f60b4044-0 iQrnCV"><span data-state="unchecked" data-sentry-element="SwitchThumb" data-sentry-source-file="Switch.tsx" class="Switch__SwitchThumb-sc-f60b4044-1 ijmnNR"></span></button><input type="checkbox" aria-hidden="true" tabindex="-1" value="on" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 0px; height: 0px;"></div></div></div></div><div id="Output" class="StyledComponents__Container-sc-3d22e631-25 pIdQE"><div class="StyledComponents__SectionHeader-sc-3d22e631-7 bvVbA"><h2 class="StyledComponents__SectionTitle-sc-3d22e631-23 hiMDYg"></h2></div><div class="StyledComponents__FieldsGroup-sc-3d22e631-9 eYtVaw"></div></div></div><div class="StyledComponents__FormBottomPadding-sc-3d22e631-11 VSgtP"></div></form></div></div><div data-sentry-element="DesktopWrapper" data-sentry-source-file="MobileCollapsibleForm.tsx" class="MobileCollapsibleForm__DesktopWrapper-sc-bf3973d3-4 eZKFuh"><form class="StyledComponents__Form-sc-3d22e631-0 hlqMXx"><div class="StyledComponents__FormFieldsContainer-sc-3d22e631-5 bLoPZl"><div id="Search" class="StyledComponents__Container-sc-3d22e631-25 pIdQE h-full"><div class="StyledComponents__SectionHeader-sc-3d22e631-7 bvVbA"><h2 class="StyledComponents__SectionTitle-sc-3d22e631-23 hiMDYg"></h2></div><div class="StyledComponents__FieldsGroup-sc-3d22e631-9 eYtVaw h-full"><div data-sentry-element="SingleRowField" data-sentry-component="SearchTypeSelectField" data-sentry-source-file="SearchTypeSelectField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Search Type</span><span class="Sidebar__NewTag-sc-287785de-0 crzrNP">NEW</span></div><span class="Label-sc-82986fa0-4 fbFknc"><a href="https://docs.exa.ai/reference/how-exa-search-works" target="_blank" rel="noopener noreferrer" style="color: var(--brand-default); text-decoration: none;">See docs</a> for more details</span></div></label><button type="button" role="combobox" aria-controls="radix-:ri:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" data-sentry-element="SelectTrigger" data-sentry-source-file="SearchTypeSelectField.tsx" class="SearchTypeSelectField__SelectTrigger-sc-3371e654-0 lmtTID"><span data-sentry-element="unknown" data-sentry-source-file="SearchTypeSelectField.tsx" style="pointer-events: none;">Auto</span><span aria-hidden="true" data-sentry-element="unknown" data-sentry-source-file="SearchTypeSelectField.tsx"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down " data-sentry-element="ChevronDown" data-sentry-source-file="SearchTypeSelectField.tsx"><path d="m6 9 6 6 6-6"></path></svg></span></button><select aria-hidden="true" tabindex="-1" style="position: absolute; border: 0px; width: 1px; height: 1px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; overflow-wrap: normal;"></select></div><div data-sentry-element="SingleRowField" data-sentry-component="DropdownField" data-sentry-source-file="DropdownField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Result category</span></div></div></label><div style="width: 100%; position: relative;"><button type="button" role="combobox" aria-controls="radix-:rj:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="DropdownField__SelectTrigger-sc-b0d5501e-0 kptMXs command-bar-search-category" data-sentry-element="SelectTrigger" data-sentry-source-file="DropdownField.tsx"><span data-sentry-element="unknown" data-sentry-source-file="DropdownField.tsx" style="pointer-events: none;"><span data-sentry-element="SelectValue" data-sentry-source-file="DropdownField.tsx" class="DropdownField__SelectValue-sc-b0d5501e-1 jrgpve"></span></span><span aria-hidden="true" data-sentry-element="unknown" data-sentry-source-file="DropdownField.tsx"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down DropdownField__SelectIcon-sc-b0d5501e-2 hzQcBN" data-sentry-element="SelectIcon" data-sentry-source-file="DropdownField.tsx"><path d="m6 9 6 6 6-6"></path></svg></span></button><select aria-hidden="true" tabindex="-1" style="position: absolute; border: 0px; width: 1px; height: 1px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; overflow-wrap: normal;"><option value="-" selected="">---</option><option value="company">Company</option><option value="research paper">Research Paper</option><option value="news">News Article</option><option value="pdf">PDF</option><option value="github">Github</option><option value="personal site">Personal Site</option><option value="linkedin profile">LinkedIn Profile</option><option value="financial report">Financial Report</option></select></div></div><div data-sentry-element="SingleRowField" data-sentry-component="TextField" data-sentry-source-file="TextField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>User location</span></div><span class="Label-sc-82986fa0-4 fbFknc">Two-letter <a target="_blank" rel="noopener noreferrer" href="https://en.wikipedia.org/wiki/ISO_3166-1#Codes">ISO country code</a> (e.g., US, GB, CA)</span></div></label><input variant="text" placeholder="Enter country code" data-sentry-element="InputContainer" data-sentry-source-file="TextField.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 sehgj" value="" style=""></div><div data-sentry-element="SingleRowField" data-sentry-component="NumericField" data-sentry-source-file="NumericField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Number of results</span></div><span class="Label-sc-82986fa0-4 fbFknc"><a href="mailto:<EMAIL>">Contact us</a> for more than 100 results</span></div></label><div style="display: flex; gap: 8px; align-items: center;"><input type="text" inputmode="numeric" pattern="[0-9,]*" min="0" max="1000" placeholder="Default: 10, max: 100" data-sentry-element="BaseNumericTextInput" data-sentry-source-file="NumericField.tsx" class="StyledComponents__TextInput-sc-3d22e631-16 StyledComponents__BaseNumericTextInput-sc-3d22e631-18 hXSXOw gAmUFC" value="" style=""></div></div></div></div><div id="Filters" class="StyledComponents__Container-sc-3d22e631-25 pIdQE"><div class="StyledComponents__SectionHeader-sc-3d22e631-7 bvVbA"><h2 class="StyledComponents__SectionTitle-sc-3d22e631-23 hiMDYg">Filters</h2></div><div class="StyledComponents__FieldsGroup-sc-3d22e631-9 eYtVaw"><div data-sentry-element="SingleRowField" data-sentry-component="DateRangeField" data-sentry-source-file="DateRangeField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Published date range</span></div></div></label><div variant="date" data-sentry-element="InputContainer" data-sentry-source-file="DateRangeField.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 hbWlUI"><button class="peer flex w-full cursor-pointer appearance-none items-center gap-x-2 truncated outline-none transition-all h-full bg-transparent text-gray-900 placeholder-secondary-accent2x disabled:pointer-events-none disabled:bg-gray-100 disabled:text-gray-400" data-sentry-element="Trigger" data-sentry-source-file="DatePickerRaw.tsx" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:rk:" data-state="closed"><span class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap text-left text-gray-900 "> - </span><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar " style="color: var(--text-light-muted);"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg></button></div></div><div data-sentry-element="SingleRowField" data-sentry-component="DateRangeField" data-sentry-source-file="DateRangeField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Crawl date range</span></div><span class="Label-sc-82986fa0-4 fbFknc">Return results Exa crawled within this range</span></div></label><div variant="date" data-sentry-element="InputContainer" data-sentry-source-file="DateRangeField.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 hbWlUI"><button class="peer flex w-full cursor-pointer appearance-none items-center gap-x-2 truncated outline-none transition-all h-full bg-transparent text-gray-900 placeholder-secondary-accent2x disabled:pointer-events-none disabled:bg-gray-100 disabled:text-gray-400" data-sentry-element="Trigger" data-sentry-source-file="DatePickerRaw.tsx" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:rl:" data-state="closed"><span class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap text-left text-gray-900 "> - </span><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar " style="color: var(--text-light-muted);"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg></button></div></div><div data-sentry-element="unknown" data-sentry-component="DomainFilter" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-0 kXMlMi"><div data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-1 hkINIl"><div data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-2 hNwLJW"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Domain filter</span></div><span class="Label-sc-82986fa0-4 fbFknc">Base and subdomains only</span></div></label></div><div data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-3 QlrHM"><div data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-4 iazXwF"><div dir="ltr" data-orientation="horizontal" data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx"><div role="tablist" aria-orientation="horizontal" data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-6 bNcsAf" tabindex="0" data-orientation="horizontal" style="outline: none;"><button type="button" role="tab" aria-selected="true" aria-controls="radix-:rm:-content-include" data-state="active" id="radix-:rm:-trigger-include" class="Tabs__TabsTrigger-sc-7a676f08-1 hqoIyX" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Include</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:rm:-content-exclude" data-state="inactive" id="radix-:rm:-trigger-exclude" class="Tabs__TabsTrigger-sc-7a676f08-1 hqoIyX" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Exclude</button></div></div><div data-sentry-element="unknown" data-sentry-source-file="DomainFilter.tsx" class="DomainFilter-sc-a57af2f0-5 dTFZiu"><input variant="text" placeholder="Enter comma separated domains" data-sentry-element="InputContainer" data-sentry-source-file="DomainFilter.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 sehgj" value="" style=""></div></div></div></div></div><div data-sentry-element="SingleRowField" data-sentry-component="TextField" data-sentry-source-file="TextField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Include text</span></div></div></label><input variant="text" placeholder="Enter phrase up to 5 words" data-sentry-element="InputContainer" data-sentry-source-file="TextField.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 sehgj" value="" style=""></div><div data-sentry-element="SingleRowField" data-sentry-component="TextField" data-sentry-source-file="TextField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Exclude text</span></div></div></label><input variant="text" placeholder="Enter phrase up to 5 words" data-sentry-element="InputContainer" data-sentry-source-file="TextField.tsx" class="StyledComponents__InputContainer-sc-3d22e631-26 sehgj" value="" style=""></div></div></div><div id="Crawling" class="StyledComponents__Container-sc-3d22e631-25 pIdQE"><div class="StyledComponents__SectionHeader-sc-3d22e631-7 bvVbA"><h2 class="StyledComponents__SectionTitle-sc-3d22e631-23 hiMDYg">Crawling</h2></div><div class="StyledComponents__FieldsGroup-sc-3d22e631-9 eYtVaw"><div class="StyledComponents__ContentsField-sc-3d22e631-3 cCTCTp"><div class="flex items-center justify-between"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Context</span></div><span class="Label-sc-82986fa0-4 fbFknc">Return page contents as a context string for LLM RAG</span></div></label><button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" data-sentry-element="SwitchRoot" data-sentry-component="Switch" data-sentry-source-file="Switch.tsx" class="Switch__SwitchRoot-sc-f60b4044-0 iQrnCV"><span data-state="unchecked" data-sentry-element="SwitchThumb" data-sentry-source-file="Switch.tsx" class="Switch__SwitchThumb-sc-f60b4044-1 ijmnNR"></span></button><input type="checkbox" aria-hidden="true" tabindex="-1" value="on" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 42px; height: 25px;"></div></div><div data-sentry-element="FieldContainer" data-sentry-component="LivecrawlField" data-sentry-source-file="LivecrawlField.tsx" class="LivecrawlField__FieldContainer-sc-bc2a0696-5 kFEEhd"><div data-sentry-element="LabelSection" data-sentry-source-file="LivecrawlField.tsx" class="LivecrawlField__LabelSection-sc-bc2a0696-6 fjlkKY"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Livecrawl strategy</span></div><span class="Label-sc-82986fa0-4 fbFknc">How we fetch website contents at search time</span></div></label></div><div data-sentry-element="SelectSection" data-sentry-source-file="LivecrawlField.tsx" class="LivecrawlField__SelectSection-sc-bc2a0696-7 fvRxFd"><button type="button" role="combobox" aria-controls="radix-:rp:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" data-sentry-element="SelectTrigger" data-sentry-source-file="LivecrawlField.tsx" class="LivecrawlField__SelectTrigger-sc-bc2a0696-0 hmqknB"><span data-sentry-element="unknown" data-sentry-source-file="LivecrawlField.tsx" style="pointer-events: none;">Fallback</span><span aria-hidden="true" data-sentry-element="unknown" data-sentry-source-file="LivecrawlField.tsx"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down " data-sentry-element="ChevronDown" data-sentry-source-file="LivecrawlField.tsx"><path d="m6 9 6 6 6-6"></path></svg></span></button><select aria-hidden="true" tabindex="-1" style="position: absolute; border: 0px; width: 1px; height: 1px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; overflow-wrap: normal;"></select></div></div><div data-sentry-element="TimeoutFieldContainer" data-sentry-source-file="Contents.tsx" class="Contents__TimeoutFieldContainer-sc-9206aa93-0 bOCKoW"><div data-sentry-element="TimeoutLabelSection" data-sentry-source-file="Contents.tsx" class="Contents__TimeoutLabelSection-sc-9206aa93-1 eZQxIV"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Timeout</span><div class="group cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info ml-1.5 text-gray-400 hover:text-gray-600 transition-colors duration-100"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg><div class="absolute z-10 opacity-0 scale-95 transform transition-all duration-200 ease-out pointer-events-none      rounded-[4px] p-[2px] flex items-center gap-1 max-w-[400px] min-w-[250px]      group-hover:opacity-100 group-hover:scale-100 group-hover:translate-y-0 group-hover:pointer-events-auto      left-1/2 -translate-x-1/2 text-left bottom-full mb-2 translate-y-2 rounded-[2px] bg-gray-900 px-3 py-2 text-sm font-medium text-white" data-sentry-component="NewTooltip" data-sentry-source-file="NewTooltip.tsx" style="box-shadow: rgba(32, 67, 61, 0.01) 0px 14px 6px 0px, rgba(32, 67, 61, 0.04) 0px 8px 5px 0px, rgba(32, 67, 61, 0.08) 0px 4px 4px 0px, rgba(32, 67, 61, 0.09) 0px 1px 2px 0px;">Maximum time to wait for live crawling before giving up</div></div></div><span class="Label-sc-82986fa0-4 fbFknc">Max wait time (ms) before stopping livecrawl</span></div></label></div><div data-sentry-element="TimeoutInputSection" data-sentry-source-file="Contents.tsx" class="Contents__TimeoutInputSection-sc-9206aa93-2 jJPTrc"><div data-sentry-element="SingleRowField" data-sentry-component="NumericField" data-sentry-source-file="NumericField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi" style="flex: 1 1 100%;"><div style="display: flex; gap: 8px; align-items: center;"><input type="text" inputmode="numeric" pattern="[0-9,]*" min="0" max="10000" placeholder="Max: 10000ms" data-sentry-element="BaseNumericTextInput" data-sentry-source-file="NumericField.tsx" class="StyledComponents__TextInput-sc-3d22e631-16 StyledComponents__BaseNumericTextInput-sc-3d22e631-18 hXSXOw gAmUFC" value="" style=""></div></div></div></div><div data-sentry-element="ContentsField" data-sentry-component="TextContentField" data-sentry-source-file="TextContentField.tsx" class="StyledComponents__ContentsField-sc-3d22e631-3 cCTCTp"><div class="flex items-center justify-between"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Full webpage text</span></div><span class="Label-sc-82986fa0-4 fbFknc">Return for every result, including for subpages</span></div></label><button type="button" role="switch" aria-checked="true" data-state="checked" value="on" data-sentry-element="SwitchRoot" data-sentry-component="Switch" data-sentry-source-file="Switch.tsx" class="Switch__SwitchRoot-sc-f60b4044-0 iQrnCV"><span data-state="checked" data-sentry-element="SwitchThumb" data-sentry-source-file="Switch.tsx" class="Switch__SwitchThumb-sc-f60b4044-1 ijmnNR"></span></button><input type="checkbox" aria-hidden="true" tabindex="-1" value="on" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 42px; height: 25px;" checked=""></div><div class="TextContentField__MaxCharactersFieldContainer-sc-484e11-0 laTHJJ"><div class="TextContentField__MaxCharactersLabelSection-sc-484e11-1 kLcsZy"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Max characters per result</span></div></div></label></div><div class="TextContentField__MaxCharactersInputSection-sc-484e11-2 bGiGuf"><div data-sentry-element="SingleRowField" data-sentry-component="NumericField" data-sentry-source-file="NumericField.tsx" class="StyledComponents__SingleRowField-sc-3d22e631-1 hhxrRi" style="flex: 1 1 100%;"><div style="display: flex; gap: 8px; align-items: center;"><input type="text" inputmode="numeric" pattern="[0-9,]*" min="0" placeholder="No default, enter a number" data-sentry-element="BaseNumericTextInput" data-sentry-source-file="NumericField.tsx" class="StyledComponents__TextInput-sc-3d22e631-16 StyledComponents__BaseNumericTextInput-sc-3d22e631-18 hXSXOw gAmUFC" value="" style=""></div></div></div></div><div class="flex items-center justify-between"><div data-sentry-element="HorizontalFieldContainer" data-sentry-component="CheckboxField" data-sentry-source-file="Checkbox.tsx" class="StyledComponents__HorizontalFieldContainer-sc-3d22e631-15 jsxcIy"><div class="flex flex-1 flex-col"><div class="flex flex-row items-center gap-2"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Include HTML tags</span></div></div></label><div data-sentry-element="CheckboxContainer" data-sentry-source-file="Checkbox.tsx" class="Checkbox__CheckboxContainer-sc-353bfc51-1 guKFeb"><input type="checkbox" data-sentry-element="CheckboxStyled" data-sentry-source-file="Checkbox.tsx" class="Checkbox__CheckboxStyled-sc-353bfc51-2 btDvgj" style=""></div></div></div></div></div></div><div data-sentry-element="ContentsField" data-sentry-component="SummaryField" data-sentry-source-file="SummaryField.tsx" class="StyledComponents__ContentsField-sc-3d22e631-3 cCTCTp"><div class="flex items-center justify-between"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>AI page summary</span></div></div></label><button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" data-sentry-element="SwitchRoot" data-sentry-component="Switch" data-sentry-source-file="Switch.tsx" class="Switch__SwitchRoot-sc-f60b4044-0 iQrnCV"><span data-state="unchecked" data-sentry-element="SwitchThumb" data-sentry-source-file="Switch.tsx" class="Switch__SwitchThumb-sc-f60b4044-1 ijmnNR"></span></button><input type="checkbox" aria-hidden="true" tabindex="-1" value="on" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 42px; height: 25px;"></div></div><div data-sentry-element="ContentsField" data-sentry-component="SubpagesField" data-sentry-source-file="SubpagesField.tsx" class="StyledComponents__ContentsField-sc-3d22e631-3 cCTCTp"><div class="flex items-center justify-between"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Subpages</span></div><span class="Label-sc-82986fa0-4 fbFknc">Include subpages (e.g. about, faq, pricing)</span></div></label><button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" data-sentry-element="SwitchRoot" data-sentry-component="Switch" data-sentry-source-file="Switch.tsx" class="Switch__SwitchRoot-sc-f60b4044-0 iQrnCV"><span data-state="unchecked" data-sentry-element="SwitchThumb" data-sentry-source-file="Switch.tsx" class="Switch__SwitchThumb-sc-f60b4044-1 ijmnNR"></span></button><input type="checkbox" aria-hidden="true" tabindex="-1" value="on" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 42px; height: 25px;"></div></div><div data-sentry-element="ContentsField" data-sentry-component="LinksField" data-sentry-source-file="LinksField.tsx" class="StyledComponents__ContentsField-sc-3d22e631-3 cCTCTp"><div class="flex items-center justify-between"><label class="Label-sc-82986fa0-1 kVEitM relative w-full"><div class="Label-sc-82986fa0-2 dipqsE"><div class="Label-sc-82986fa0-3 bsoamt"><span>Links</span></div><span class="Label-sc-82986fa0-4 fbFknc">Include links found in the webpages</span></div></label><button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" data-sentry-element="SwitchRoot" data-sentry-component="Switch" data-sentry-source-file="Switch.tsx" class="Switch__SwitchRoot-sc-f60b4044-0 iQrnCV"><span data-state="unchecked" data-sentry-element="SwitchThumb" data-sentry-source-file="Switch.tsx" class="Switch__SwitchThumb-sc-f60b4044-1 ijmnNR"></span></button><input type="checkbox" aria-hidden="true" tabindex="-1" value="on" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 42px; height: 25px;"></div></div></div></div><div id="Output" class="StyledComponents__Container-sc-3d22e631-25 pIdQE"><div class="StyledComponents__SectionHeader-sc-3d22e631-7 bvVbA"><h2 class="StyledComponents__SectionTitle-sc-3d22e631-23 hiMDYg"></h2></div><div class="StyledComponents__FieldsGroup-sc-3d22e631-9 eYtVaw"></div></div></div><div class="StyledComponents__FormBottomPadding-sc-3d22e631-11 VSgtP"></div></form></div></div></div></div><div data-sentry-element="ColumnResizer" data-sentry-source-file="[form].tsx" class="StyledComponents__ColumnResizer-sc-12ec547a-7 bkpDFe" style="width: 5px;"></div><div data-sentry-element="RightContainer" data-sentry-source-file="[form].tsx" class="StyledComponents__RightContainer-sc-12ec547a-6 ijLoJs"><div data-sentry-element="CodeContainer" data-sentry-component="CodeSection" data-sentry-source-file="CodeContainer.tsx" class="CodeContainer-sc-a5f5f37b-19 ezGZSi"><div dir="ltr" data-orientation="horizontal" data-sentry-element="unknown" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__TabsRoot-sc-12ec547a-9 djbglp" style="height: 100%; overflow: auto;"><div data-sentry-element="SlidingTabsContainer" data-sentry-component="SlidingTabsList" data-sentry-source-file="CodeContainer.tsx" class="CodeContainer__SlidingTabsContainer-sc-a5f5f37b-6 henlXn"><div left="12" width="86.63282012939453" data-sentry-element="SlidingBackground" data-sentry-source-file="CodeContainer.tsx" class="CodeContainer__SlidingBackground-sc-a5f5f37b-7 SVLXt"></div><button class="CodeContainer__SlidingTabTrigger-sc-a5f5f37b-8 iPVYGw"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-code " data-sentry-element="SquareCode" data-sentry-source-file="CodeContainer.tsx"><path d="M10 9.5 8 12l2 2.5"></path><path d="m14 9.5 2 2.5-2 2.5"></path><rect width="18" height="18" x="3" y="3" rx="2"></rect></svg>Code</button><button class="CodeContainer__SlidingTabTrigger-sc-a5f5f37b-8 kWNFAj"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-codepen " data-sentry-element="Codepen" data-sentry-source-file="CodeContainer.tsx"><polygon points="12 2 22 8.5 22 15.5 12 22 2 15.5 2 8.5 12 2"></polygon><line x1="12" x2="12" y1="22" y2="15.5"></line><polyline points="22 8.5 12 15.5 2 8.5"></polyline><polyline points="2 15.5 12 8.5 22 15.5"></polyline><line x1="12" x2="12" y1="2" y2="8.5"></line></svg>Output</button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:r1s:-trigger-code" id="radix-:r1s:-content-code" tabindex="0" data-sentry-element="unknown" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__TabsContent-sc-12ec547a-12 bVkXGv" style="height: 100%; overflow: auto;"><div dir="ltr" data-orientation="horizontal" data-sentry-element="unknown" data-sentry-component="CodeTabsSection" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__TabsRoot-sc-12ec547a-9 djbglp" style="height: 100%; overflow: auto;"><div role="tablist" aria-orientation="horizontal" data-sentry-element="unknown" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__TabsList-sc-12ec547a-10 hLbnkA" tabindex="0" data-orientation="horizontal" style="outline: none;"><button type="button" role="tab" aria-selected="true" aria-controls="radix-:r1t:-content-python" data-state="active" id="radix-:r1t:-trigger-python" data-sentry-element="unknown" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__TabsTrigger-sc-12ec547a-11 ejUKyz" tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 16 16" fill="currentColor" data-sentry-element="svg" data-sentry-component="PythonIcon" data-sentry-source-file="Icons.tsx"><path d="M7.90456 0.000130867C7.24482 0.00316288 6.61479 0.0588139 6.06043 0.155836C4.42735 0.441196 4.13085 1.03848 4.13085 2.13997V3.59471H7.99002V4.07962H4.13085H2.68253C1.56095 4.07962 0.578861 4.74639 0.271676 6.01482C-0.0826578 7.46873 -0.0983745 8.37599 0.271676 9.89411C0.546 11.0241 1.20112 11.8293 2.3227 11.8293H3.64957V10.0854C3.64957 8.82554 4.75168 7.71423 6.06043 7.71423H9.9151C10.9881 7.71423 11.8447 6.84041 11.8447 5.77458V2.13997C11.8447 1.10554 10.9624 0.328479 9.9151 0.155836C9.25216 0.0466859 8.56429 -0.00290115 7.90456 0.000130867ZM5.81755 1.17015C6.21617 1.17015 6.5417 1.49738 6.5417 1.89974C6.5417 2.30067 6.21617 2.62488 5.81755 2.62488C5.41749 2.62488 5.09339 2.30067 5.09339 1.89974C5.09339 1.49738 5.41749 1.17015 5.81755 1.17015Z" data-sentry-element="path" data-sentry-source-file="Icons.tsx"></path><path d="M12.3259 4.08008V5.77504C12.3259 7.08913 11.1995 8.19515 9.91503 8.19515H6.06035C5.00449 8.19515 4.13077 9.08896 4.13077 10.1348V13.7694C4.13077 14.8038 5.04021 15.4123 6.06035 15.7091C7.28195 16.0643 8.4534 16.1285 9.91503 15.7091C10.8866 15.4308 11.8446 14.8709 11.8446 13.7694V12.3147H7.98994V11.8298H11.8446H13.7742C14.8958 11.8298 15.3137 11.056 15.7038 9.89457C16.1067 8.69891 16.0896 7.54909 15.7038 6.01528C15.4266 4.91093 14.8972 4.08008 13.7742 4.08008H12.3259ZM10.1579 13.2845C10.558 13.2845 10.8821 13.6087 10.8821 14.0096C10.8821 14.412 10.558 14.7392 10.1579 14.7392C9.75928 14.7392 9.43375 14.412 9.43375 14.0096C9.43375 13.6087 9.75928 13.2845 10.1579 13.2845Z" data-sentry-element="path" data-sentry-source-file="Icons.tsx"></path></svg>Python</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:r1t:-content-javascript" data-state="inactive" id="radix-:r1t:-trigger-javascript" data-sentry-element="unknown" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__TabsTrigger-sc-12ec547a-11 ejUKyz" tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 15 16" fill="currentColor" data-sentry-element="svg" data-sentry-component="JavascriptIcon" data-sentry-source-file="Icons.tsx"><path d="M7.09881 16C6.88455 16 6.67175 15.9438 6.4839 15.8356L4.5266 14.6768C4.23426 14.5133 4.37691 14.4555 4.47333 14.4222C4.86311 14.2857 4.94221 14.2549 5.35827 14.0186C5.40201 13.9937 5.45924 14.0025 5.504 14.0289L7.00768 14.9212C7.06198 14.9505 7.13903 14.9505 7.18936 14.9212L13.0523 11.537C13.1066 11.5061 13.1418 11.443 13.1418 11.3785V4.61295C13.1418 4.54544 13.1066 4.48527 13.0511 4.45152L7.19054 1.07023C7.13624 1.03794 7.06433 1.03794 7.01003 1.07023L1.15059 4.45152C1.0938 4.4838 1.05769 4.54691 1.05769 4.61148V11.377C1.05769 11.4416 1.09292 11.5032 1.14868 11.534L2.75421 12.4615C3.62595 12.8974 4.15868 12.3838 4.15868 11.8672V5.18971C4.15868 5.09431 4.23352 5.02094 4.32892 5.02094H5.07151C5.16396 5.02094 5.24028 5.09431 5.24028 5.18971V11.8701C5.24028 13.0324 4.60629 13.7002 3.50414 13.7002C3.16513 13.7002 2.89803 13.7002 2.1525 13.3333L0.614486 12.4469C0.234385 12.2267 -0.000427246 11.8158 -0.000427246 11.377V4.61148C-0.000427246 4.17121 0.234385 3.76029 0.614486 3.54309L6.48331 0.157397C6.85461 -0.0524658 7.34771 -0.0524658 7.71608 0.157397L13.5776 3.54456C13.9562 3.76323 14.1925 4.17268 14.1925 4.61295V11.3785C14.1925 11.8173 13.9562 12.2267 13.5776 12.4469L7.71608 15.8326C7.52823 15.9412 7.31543 15.9969 7.09823 15.9969" data-sentry-element="path" data-sentry-source-file="Icons.tsx"></path><path d="M8.9098 11.3382C6.34448 11.3382 5.80661 10.1606 5.80661 9.17294C5.80661 9.07902 5.88234 9.00417 5.97641 9.00417H6.73368C6.81733 9.00417 6.88777 9.06508 6.90098 9.1477C7.01545 9.91935 7.35593 10.3085 8.90715 10.3085C10.1428 10.3085 10.6682 10.0294 10.6682 9.37415C10.6682 8.99698 10.5185 8.71667 8.59896 8.52882C6.99344 8.37033 6.00136 8.01664 6.00136 6.73251C6.00136 5.54965 6.99931 4.84521 8.67087 4.84521C10.5479 4.84521 11.4783 5.49682 11.5957 6.89688C11.6002 6.94531 11.5825 6.99227 11.5503 7.0275C11.518 7.06125 11.4725 7.0818 11.4255 7.0818H10.6638C10.5846 7.0818 10.5156 7.02603 10.4995 6.94971C10.316 6.13815 9.87282 5.87839 8.66794 5.87839C7.31924 5.87839 7.16221 6.34801 7.16221 6.70023C7.16221 7.12729 7.34712 7.25203 9.16691 7.49272C10.9691 7.73193 11.8247 8.06947 11.8247 9.33599C11.8247 10.6128 10.7592 11.3451 8.90128 11.3451" data-sentry-element="path" data-sentry-source-file="Icons.tsx"></path></svg>Javascript</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:r1t:-content-curl" data-state="inactive" id="radix-:r1t:-trigger-curl" data-sentry-element="unknown" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__TabsTrigger-sc-12ec547a-11 ejUKyz" tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-sentry-element="svg" data-sentry-component="CurlIcon" data-sentry-source-file="Icons.tsx"><polyline points="4 17 10 11 4 5" data-sentry-element="polyline" data-sentry-source-file="Icons.tsx"></polyline><line x1="12" x2="20" y1="19" y2="19" data-sentry-element="line" data-sentry-source-file="Icons.tsx"></line></svg>curl</button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:r1t:-trigger-python" id="radix-:r1t:-content-python" tabindex="0" data-sentry-element="unknown" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__TabsContent-sc-12ec547a-12 bVkXGv" style=""><div data-sentry-element="unknown" data-sentry-component="renderCodeBlock" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__Container-sc-12ec547a-13 ghwtlp"><pre class="CodeContainer__Bash-sc-a5f5f37b-13 bJnLhE" style="color: rgb(212, 212, 212); font-size: 13px; text-shadow: none; font-family: Menlo, Monaco, Consolas, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, &quot;Courier New&quot;, monospace; direction: ltr; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto; background: rgb(30, 30, 30);"><code class="language-bash" style="white-space: pre; color: rgb(212, 212, 212); font-size: 13px; text-shadow: none; font-family: Menlo, Monaco, Consolas, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, &quot;Courier New&quot;, monospace; direction: ltr; text-align: left; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span>pip </span><span class="token" style="color: rgb(220, 220, 170);">install</span><span> exa-py</span></code></pre><button data-sentry-element="unknown" data-sentry-component="CodeCopyButtonWithState" data-sentry-source-file="CodeContainer.tsx" class="CodeContainer__CodeCopyButton-sc-a5f5f37b-9 kPhuRB" style="top: 72px; right: 16px; position: absolute;"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy "><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button><pre data-sentry-element="unknown" data-sentry-source-file="CodeContainer.tsx" class="CodeContainer__Code-sc-a5f5f37b-14 bWaX" style="color: rgb(212, 212, 212); font-size: 13px; text-shadow: none; font-family: Menlo, Monaco, Consolas, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, &quot;Courier New&quot;, monospace; direction: ltr; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 4; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto; background: rgb(30, 30, 30);"><code class="language-python" style="white-space: pre; color: rgb(212, 212, 212); font-size: 13px; text-shadow: none; font-family: Menlo, Monaco, Consolas, &quot;Andale Mono&quot;, &quot;Ubuntu Mono&quot;, &quot;Courier New&quot;, monospace; direction: ltr; text-align: left; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 4; hyphens: none;"><span class="linenumber react-syntax-highlighter-line-number" style="display: inline-block; min-width: 2.25em; padding-right: 1em; text-align: right; user-select: none; color: rgb(106, 153, 85);">1</span><span class="token" style="color: rgb(86, 156, 214);">from</span><span> exa_py </span><span class="token" style="color: rgb(86, 156, 214);">import</span><span> Exa
</span><span class="linenumber react-syntax-highlighter-line-number" style="display: inline-block; min-width: 2.25em; padding-right: 1em; text-align: right; user-select: none; color: rgb(106, 153, 85);">2</span>
<span class="linenumber react-syntax-highlighter-line-number" style="display: inline-block; min-width: 2.25em; padding-right: 1em; text-align: right; user-select: none; color: rgb(106, 153, 85);">3</span><span>exa </span><span class="token" style="color: rgb(212, 212, 212);">=</span><span> Exa</span><span class="token" style="color: rgb(212, 212, 212);">(</span><span>api_key </span><span class="token" style="color: rgb(212, 212, 212);">=</span><span> </span><span class="token" style="color: rgb(206, 145, 120);">"5bb2092a-a214-4a0b-abf0-4cc91034449d"</span><span class="token" style="color: rgb(212, 212, 212);">)</span><span>
</span><span class="linenumber react-syntax-highlighter-line-number" style="display: inline-block; min-width: 2.25em; padding-right: 1em; text-align: right; user-select: none; color: rgb(106, 153, 85);">4</span>
<span class="linenumber react-syntax-highlighter-line-number" style="display: inline-block; min-width: 2.25em; padding-right: 1em; text-align: right; user-select: none; color: rgb(106, 153, 85);">5</span><span>result </span><span class="token" style="color: rgb(212, 212, 212);">=</span><span> exa</span><span class="token" style="color: rgb(212, 212, 212);">.</span><span>search_and_contents</span><span class="token" style="color: rgb(212, 212, 212);">(</span><span>
</span><span class="linenumber react-syntax-highlighter-line-number" style="display: inline-block; min-width: 2.25em; padding-right: 1em; text-align: right; user-select: none; color: rgb(106, 153, 85);">6</span><span>  </span><span class="token" style="color: rgb(206, 145, 120);">"blog post about AI"</span><span class="token" style="color: rgb(212, 212, 212);">,</span><span>
</span><span class="linenumber react-syntax-highlighter-line-number" style="display: inline-block; min-width: 2.25em; padding-right: 1em; text-align: right; user-select: none; color: rgb(106, 153, 85);">7</span><span style="">  text </span><span class="token" style="color: rgb(212, 212, 212);">=</span><span> </span><span class="token" style="color: rgb(86, 156, 214);">True</span><span class="token" style="color: rgb(212, 212, 212);">,</span><span>
</span><span class="linenumber react-syntax-highlighter-line-number" style="display: inline-block; min-width: 2.25em; padding-right: 1em; text-align: right; user-select: none; color: rgb(106, 153, 85);">8</span><span>  </span><span class="token" style="color: rgb(206, 145, 120);">type</span><span> </span><span class="token" style="color: rgb(212, 212, 212);">=</span><span> </span><span class="token" style="color: rgb(206, 145, 120);">"auto"</span><span>
</span><span class="linenumber react-syntax-highlighter-line-number" style="display: inline-block; min-width: 2.25em; padding-right: 1em; text-align: right; user-select: none; color: rgb(106, 153, 85);">9</span><span></span><span class="token" style="color: rgb(212, 212, 212);">)</span></code></pre></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:r1t:-trigger-javascript" hidden="" id="radix-:r1t:-content-javascript" tabindex="0" data-sentry-element="unknown" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__TabsContent-sc-12ec547a-12 bVkXGv"></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:r1t:-trigger-curl" hidden="" id="radix-:r1t:-content-curl" tabindex="0" data-sentry-element="unknown" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__TabsContent-sc-12ec547a-12 bVkXGv"></div></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:r1s:-trigger-output" hidden="" id="radix-:r1s:-content-output" tabindex="0" data-sentry-element="unknown" data-sentry-source-file="CodeContainer.tsx" class="StyledComponents__TabsContent-sc-12ec547a-12 bVkXGv" style="height: 100%; overflow: hidden;"></div></div></div></div></div></div></div></div></main><div class="DocBot__Container-sc-f9324145-1 fBvIWb"><div class="DocBot__CollapsedContainer-sc-f9324145-2 cYkJFO"><div class="DocBot__CollapsedTitle-sc-f9324145-3 jWKHom"><span>Ask ExaBot</span></div><button class="DocBot__CollapsedButton-sc-f9324145-5 lfUQFU"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up "><path d="m5 12 7-7 7 7"></path><path d="M12 19V5"></path></svg></button></div></div><div data-sentry-element="unknown" data-sentry-source-file="Layout.tsx" class="Layout__ChatwootContainer-sc-c48799f0-2 jcNueG"></div></div><div class="Toastify"></div></div><script type="text/javascript" crossorigin="anonymous" src="/ingest/array/phc_ebUSDbDOBtuw7SuEOBFtu315tugMMml4W8zoT4DNMC4/config.js"></script><script type="text/javascript" crossorigin="anonymous" src="/ingest/static/recorder.js?v=1.209.1"></script><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"session":{"user":{"name":null,"email":"<EMAIL>","image":null,"currentTeamId":"cmdys3zzn000rwhrv1r5va1mp"},"expires":"2025-09-04T16:54:25.520Z"},"_sentryTraceData":"2e1cd3a8515a30c4ee3519331f7e9340-1693b98d23e77abf-1","_sentryBaggage":"sentry-environment=production,sentry-release=933f30c05bb3e8442e7c43d92c797951c24e0577,sentry-public_key=99e4eeca01a54f92813b60808e3e7387,sentry-trace_id=2e1cd3a8515a30c4ee3519331f7e9340,sentry-sample_rate=1,sentry-transaction=GET%20%2Fhome,sentry-sampled=true"},"__N_SSP":true},"page":"/home","query":{},"buildId":"J2dhssoYK8tVrkfJD7GUX","isFallback":false,"isExperimentalCompile":false,"gssp":true,"scriptLoader":[]}</script><div id="modal-portal"></div><script defer="" src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon="{&quot;rayId&quot;:&quot;96a7d0f8be9d492f&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.7.0&quot;,&quot;token&quot;:&quot;ba7f9a3d4a8f4676843194096109a485&quot;}" crossorigin="anonymous"></script>
<script src="https://www.googletagmanager.com/gtag/js?id=AW-16696812396" data-sentry-element="Script" data-sentry-source-file="_app.tsx" data-nscript="afterInteractive"></script><script id="google-analytics" data-sentry-element="Script" data-sentry-source-file="_app.tsx" data-nscript="afterInteractive">
                          window.dataLayer = window.dataLayer || [];
                          function gtag(){dataLayer.push(arguments);}
                          gtag('js', new Date());
                          gtag('config', 'AW-16696812396');
                        </script><script src="https://www.googletagmanager.com/gtag/js?id=AW-16860319264" async="true" data-sentry-element="Script" data-sentry-source-file="GoogleAdsScript.tsx" data-nscript="afterInteractive"></script><next-route-announcer><p aria-live="assertive" id="__next-route-announcer__" role="alert" style="border: 0px; clip: rect(0px, 0px, 0px, 0px); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; top: 0px; width: 1px; white-space: nowrap; overflow-wrap: normal;">Playground | Exa API</p></next-route-announcer><style id="cw-widget-styles" data-turbo-permanent="true">
:root {
  --b-100: #F2F3F7;
  --s-700: #37546D;
}

.woot-widget-holder {
  box-shadow: 0 5px 40px rgba(0, 0, 0, .16);
  opacity: 1;
  will-change: transform, opacity;
  transform: translateY(0);
  overflow: hidden !important;
  position: fixed !important;
  transition: opacity 0.2s linear, transform 0.25s linear;
  z-index: 2147483000 !important;
}

.woot-widget-holder.woot-widget-holder--flat {
  box-shadow: none;
  border-radius: 0;
  border: 1px solid var(--b-100);
}

.woot-widget-holder iframe {
  border: 0;
  color-scheme: normal;
  height: 100% !important;
  width: 100% !important;
  max-height: 100vh !important;
}

.woot-widget-holder.has-unread-view {
  border-radius: 0 !important;
  min-height: 80px !important;
  height: auto;
  bottom: 94px;
  box-shadow: none !important;
  border: 0;
}

.woot-widget-bubble {
  background: #1f93ff;
  border-radius: 100px;
  border-width: 0px;
  bottom: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, .16) !important;
  cursor: pointer;
  height: 64px;
  padding: 0px;
  position: fixed;
  user-select: none;
  width: 64px;
  z-index: 2147483000 !important;
  overflow: hidden;
}

.woot-widget-bubble.woot-widget-bubble--flat {
  border-radius: 0;
}

.woot-widget-holder.woot-widget-holder--flat {
  bottom: 90px;
}

.woot-widget-bubble.woot-widget-bubble--flat {
  height: 56px;
  width: 56px;
}

.woot-widget-bubble.woot-widget-bubble--flat svg {
  margin: 16px;
}

.woot-widget-bubble.woot-widget-bubble--flat.woot--close::before,
.woot-widget-bubble.woot-widget-bubble--flat.woot--close::after {
  left: 28px;
  top: 16px;
}

.woot-widget-bubble.unread-notification::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  background: #ff4040;
  border-radius: 100%;
  top: 0px;
  right: 0px;
  border: 2px solid #ffffff;
  transition: background 0.2s ease;
}

.woot-widget-bubble.woot-widget--expanded {
  bottom: 24px;
  display: flex;
  height: 48px !important;
  width: auto !important;
  align-items: center;
}

.woot-widget-bubble.woot-widget--expanded div {
  align-items: center;
  color: #fff;
  display: flex;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen-Sans, Ubuntu, Cantarell, Helvetica Neue, Arial, sans-serif;
  font-size: 16px;
  font-weight: 500;
  justify-content: center;
  padding-right: 20px;
  width: auto !important;
}

.woot-widget-bubble.woot-widget--expanded.woot-widget-bubble-color--lighter div{
  color: var(--s-700);
}

.woot-widget-bubble.woot-widget--expanded svg {
  height: 20px;
  margin: 14px 8px 14px 16px;
  width: 20px;
}

.woot-widget-bubble.woot-elements--left {
  left: 20px;
}

.woot-widget-bubble.woot-elements--right {
  right: 20px;
}

.woot-widget-bubble:hover {
  background: #1f93ff;
  box-shadow: 0 8px 32px rgba(0, 0, 0, .4) !important;
}

.woot-widget-bubble svg {
  all: revert;
  height: 24px;
  margin: 20px;
  width: 24px;
}

.woot-widget-bubble.woot-widget-bubble-color--lighter path{
  fill: var(--s-700);
}

@media only screen and (min-width: 667px) {
  .woot-widget-holder.woot-elements--left {
    left: 20px;
 }
  .woot-widget-holder.woot-elements--right {
    right: 20px;
 }
}

.woot--close:hover {
  opacity: 1;
}

.woot--close::before, .woot--close::after {
  background-color: #fff;
  content: ' ';
  display: inline;
  height: 24px;
  left: 32px;
  position: absolute;
  top: 20px;
  width: 2px;
}

.woot-widget-bubble-color--lighter.woot--close::before, .woot-widget-bubble-color--lighter.woot--close::after {
  background-color: var(--s-700);
}

.woot--close::before {
  transform: rotate(45deg);
}

.woot--close::after {
  transform: rotate(-45deg);
}

.woot--hide {
  bottom: -100vh !important;
  top: unset !important;
  opacity: 0;
  visibility: hidden !important;
  z-index: -1 !important;
}

.woot-widget--without-bubble {
  bottom: 20px !important;
}
.woot-widget-holder.woot--hide{
  transform: translateY(40px);
}
.woot-widget-bubble.woot--close {
  transform: translateX(0px) scale(1) rotate(0deg);
  transition: transform 300ms ease, opacity 100ms ease, visibility 0ms linear 0ms, bottom 0ms linear 0ms;
}
.woot-widget-bubble.woot--close.woot--hide {
  transform: translateX(8px) scale(.75) rotate(45deg);
  transition: transform 300ms ease, opacity 200ms ease, visibility 0ms linear 500ms, bottom 0ms ease 200ms;
}

.woot-widget-bubble {
  transform-origin: center;
  will-change: transform, opacity;
  transform: translateX(0) scale(1) rotate(0deg);
  transition: transform 300ms ease, opacity 100ms ease, visibility 0ms linear 0ms, bottom 0ms linear 0ms;
}
.woot-widget-bubble.woot--hide {
  transform: translateX(8px) scale(.75) rotate(-30deg);
  transition: transform 300ms ease, opacity 200ms ease, visibility 0ms linear 500ms, bottom 0ms ease 200ms;
}

.woot-widget-bubble.woot-widget--expanded {
  transform: translateX(0px);
  transition: transform 300ms ease, opacity 100ms ease, visibility 0ms linear 0ms, bottom 0ms linear 0ms;
}
.woot-widget-bubble.woot-widget--expanded.woot--hide {
  transform: translateX(8px);
  transition: transform 300ms ease, opacity 200ms ease, visibility 0ms linear 500ms, bottom 0ms ease 200ms;
}
.woot-widget-bubble.woot-widget-bubble--flat.woot--close {
  transform: translateX(0px);
  transition: transform 300ms ease, opacity 10ms ease, visibility 0ms linear 0ms, bottom 0ms linear 0ms;
}
.woot-widget-bubble.woot-widget-bubble--flat.woot--close.woot--hide {
  transform: translateX(8px);
  transition: transform 300ms ease, opacity 200ms ease, visibility 0ms linear 500ms, bottom 0ms ease 200ms;
}
.woot-widget-bubble.woot-widget--expanded.woot-widget-bubble--flat {
  transform: translateX(0px);
  transition: transform 300ms ease, opacity 200ms ease, visibility 0ms linear 0ms, bottom 0ms linear 0ms;
}
.woot-widget-bubble.woot-widget--expanded.woot-widget-bubble--flat.woot--hide {
  transform: translateX(8px);
  transition: transform 300ms ease, opacity 200ms ease, visibility 0ms linear 500ms, bottom 0ms ease 200ms;
}

@media only screen and (max-width: 667px) {
  .woot-widget-holder {
    height: 100%;
    right: 0;
    top: 0;
    width: 100%;
 }

 .woot-widget-holder iframe {
    min-height: 100% !important;
  }


 .woot-widget-holder.has-unread-view {
    height: auto;
    right: 0;
    width: auto;
    bottom: 0;
    top: auto;
    max-height: 100vh;
    padding: 0 8px;
  }

  .woot-widget-holder.has-unread-view iframe {
    min-height: unset !important;
  }

 .woot-widget-holder.has-unread-view.woot-elements--left {
    left: 0;
  }

  .woot-widget-bubble.woot--close {
    bottom: 60px;
    opacity: 0;
    visibility: hidden !important;
    z-index: -1 !important;
  }
}

@media only screen and (min-width: 667px) {
  .woot-widget-holder {
    border-radius: 16px;
    bottom: 104px;
    height: calc(90% - 64px - 20px);
    max-height: 640px !important;
    min-height: 250px !important;
    width: 400px !important;
 }
}

.woot-hidden {
  display: none !important;
}
</style><iframe allow="join-ad-interest-group" data-tagging-id="AW-16696812396" data-load-time="1754412865955" height="0" width="0" src="https://td.doubleclick.net/td/rul/16696812396?random=1754412865923&amp;cv=11&amp;fst=1754412865923&amp;fmt=3&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=gtag.config&amp;gtm=45be5840v9195436136za200zd9195436136xec&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=101509157~103116026~103200004~103233427~104527907~104528501~104684208~104684211~104948813~105087538~105087540~105103161~105103163~105113532&amp;u_w=1661&amp;u_h=1053&amp;url=https%3A%2F%2Fdashboard.exa.ai%2Fhome&amp;ref=https%3A%2F%2Fdashboard.exa.ai%2Flogin%3Femail%3Dcassandra579%2540ai.whatisinitfor.me%26otp%3D031704%26redirect%3Dhttps%253A%252F%252Fdashboard.exa.ai%252F&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=Home%20%7C%20Exa%20API&amp;npa=0&amp;pscdl=noapi&amp;auid=910274139.1754412798&amp;uaa=x86&amp;uab=64&amp;uafvl=%2522Edge%2522%3B135.0.7049.95%7CNot-A.Brand%3B8.0.0.0%7CChromium%3B135.0.7049.95&amp;uamb=0&amp;uam=&amp;uap=macOS&amp;uapv=%2215.2.0%22&amp;uaw=0&amp;fledge=1&amp;data=event%3Dgtag.config" style="display: none; visibility: hidden;"></iframe><div class="woot-widget-holder woot--hide woot-elements--right" id="cw-widget-holder" data-turbo-permanent="true"><iframe src="https://app.chatwoot.com/widget?website_token=QU8iJYu14Up4SzDikQUerUqn&amp;cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiIwY2ExYWE5Mi0zZjc3LTRkMGEtODRiNS03MDk5ZWU2M2JhYjUiLCJpbmJveF9pZCI6MzE1Njl9.leG6wbIkf_D6FgdS6eovEPlFDwMivQIvkuojDh8eEcA" allow="camera;microphone;fullscreen;display-capture;picture-in-picture;clipboard-write;" id="chatwoot_live_chat_widget" style=""></iframe></div><iframe allow="join-ad-interest-group" data-tagging-id="AW-16860319264" data-load-time="1754412865990" height="0" width="0" src="https://td.doubleclick.net/td/rul/16860319264?random=1754412865981&amp;cv=11&amp;fst=1754412865981&amp;fmt=3&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=gtag.config&amp;gtm=45be5840v9210115056z89195906795za200zd9210115056xec&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=101509157~103116026~103200004~103233427~104527907~104528501~104684208~104684211~104948813~105087538~105087540~105103161~105103163&amp;u_w=1661&amp;u_h=1053&amp;url=https%3A%2F%2Fdashboard.exa.ai%2Fhome&amp;ref=https%3A%2F%2Fdashboard.exa.ai%2Flogin%3Femail%3Dcassandra579%2540ai.whatisinitfor.me%26otp%3D031704%26redirect%3Dhttps%253A%252F%252Fdashboard.exa.ai%252F&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=Home%20%7C%20Exa%20API&amp;npa=0&amp;pscdl=noapi&amp;auid=910274139.1754412798&amp;uaa=x86&amp;uab=64&amp;uafvl=%2522Edge%2522%3B135.0.7049.95%7CNot-A.Brand%3B8.0.0.0%7CChromium%3B135.0.7049.95&amp;uamb=0&amp;uam=&amp;uap=macOS&amp;uapv=%2215.2.0%22&amp;uaw=0&amp;fledge=1&amp;data=event%3Dgtag.config" style="display: none; visibility: hidden;"></iframe><iframe height="0" width="0" style="display: none; visibility: hidden;"></iframe><script type="text/javascript" id="" charset="">document.querySelector("#trigger-search-button \x3e span \x3e svg").setAttribute("id","logout");</script><script src="/_next/static/chunks/262-dcd81308dbb7bb75.js"></script><script src="/_next/static/chunks/4886-c589f54f7bf1ae2b.js"></script><script src="/_next/static/chunks/7541-50e4665de5810762.js"></script><script src="/_next/static/chunks/7068-ed23f19d6095de95.js"></script><script src="/_next/static/chunks/3663-6bf08025037234b7.js"></script><script src="/_next/static/chunks/9089-74425baed342dd09.js"></script><script src="/_next/static/chunks/pages/playground/%5Bform%5D-f258f32fe49ebb8c.js"></script><script src="/_next/static/chunks/9623-d0816276e0ae056a.js"></script><script src="/_next/static/chunks/6535-27b62a02a892ca0e.js"></script><script src="/_next/static/chunks/pages/team-settings-a0ba13f0a9e4650c.js"></script><script src="/_next/static/chunks/3061-c76761c0014ff03f.js"></script><script src="/_next/static/chunks/8347-00df2172faa3a56e.js"></script><script src="/_next/static/chunks/pages/usage-a3b01499ad040563.js"></script><script src="/_next/static/chunks/8821-6ceb47378410bf76.js"></script><script src="/_next/static/chunks/4955-76bdbb0567f01c10.js"></script><script src="/_next/static/chunks/1580-a5dee54e8b981950.js"></script><script src="/_next/static/chunks/pages/billing-1ee56f6865d826d7.js"></script><script src="/_next/static/chunks/pages/examples-library-d3e1da053b341251.js"></script><script src="/_next/static/chunks/pages/api-keys-4efffef319f5a5cd.js"></script><script src="/_next/static/chunks/pages/docs-65e8e5d0754d438d.js"></script><div class="woot--bubble-holder" id="cw-bubble-holder" data-turbo-permanent="true"><button class="woot-widget-bubble woot-elements--right" title="Open chat window" style="background: rgb(31, 64, 237);"><svg id="woot-widget-bubble-icon" width="24" height="24" viewBox="0 0 240 240" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M240.808 240.808H122.123C56.6994 240.808 3.45695 187.562 3.45695 122.122C3.45695 56.7031 56.6994 3.45697 122.124 3.45697C187.566 3.45697 240.808 56.7031 240.808 122.122V240.808Z" fill="#FFFFFF"></path></svg></button><button class="woot-elements--right woot-widget-bubble woot--close woot--hide" style="background: rgb(31, 64, 237);"></button></div><script src="/_next/static/chunks/5495-6f6d4bcad275be3d.js"></script><script src="/_next/static/chunks/pages/websets-3b7ae17b0b54d490.js"></script><iframe name="__privateStripeController9661" frameborder="0" allowtransparency="true" scrolling="no" role="presentation" allow="payment *" src="https://js.stripe.com/v3/controller-with-preconnect-b4de5dbfa7b4066168700c1afa067445.html#__shared_params__[version]=v3&amp;apiKey=pk_live_51Mz2Y9IPY1HW7v4WSE9HjPBPvtiuQazWDioXpxVuj1vk72AYPoSWx8yB9CTm4dTaKOP9DMnNo7fFYw835pQyPViz00jbQ8tZu3&amp;stripeJsId=818b9578-de17-4950-a3f1-c7b4d0596dbf&amp;stripeObjId=sobj-e541cce8-dc16-4f7e-b68b-7b6eb60a878c&amp;firstStripeInstanceCreatedLatency=15&amp;controllerCount=1&amp;isCheckout=false&amp;stripeJsLoadTime=1754412868698&amp;manualBrowserDeprecationRollout=false&amp;mids[guid]=NA&amp;mids[muid]=NA&amp;mids[sid]=NA&amp;referrer=https%3A%2F%2Fdashboard.exa.ai%2Fhome&amp;controllerId=__privateStripeController9661" aria-hidden="true" tabindex="-1" style="border: none !important; margin: 0px !important; padding: 0px !important; width: 1px !important; min-width: 100% !important; overflow: hidden !important; display: block !important; visibility: hidden !important; position: fixed !important; height: 1px !important; pointer-events: none !important; user-select: none !important;"></iframe><script type="text/javascript" id="" charset="">(function(){var a=document.querySelector("p#upcoming-plan \x3e span.text-brand.font-medium");console.log("Checking element:",a?"\u2705 Found: "+a.innerText:"\u274c Not Found");a&&(a=a.innerText.trim(),window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"payment_success"}),window.localStorage.setItem("muntaqim",a),console.log("\ud83d\udd25 dataLayer pushed with value:",payment))})();</script>
<div id="commandbar-wrapper"><div data-commandbar-toplevel-styles="" hidden=""><style data-emotion="commandbar-toplevel" data-s=""></style></div><div data-commandbar="1" id="commandbar"><div data-commandbar-toplevel-styles="" hidden=""></div><div class="cb-widget-ce80a7eb65-0" style="display: contents;"><div class="cb-widget-ce80a7eb65-0" id="commandbar-themeV2-root"><div id="commandbar-rc-tooltip-container" style=""></div><div></div><div style="position: absolute; top: 35vh; right: 50vw; z-index: 2147483647;"><div style="position: relative;"></div></div><div class="cb-widget-ce80a7eb65-2" style="display: contents;"><div role="region" aria-label="Learn and earn!" tabindex="-1" class="commandbar-checklist commandbar-toplevel-4riklp" data-testid="commandbar-checklist-Learn and earn!"><div style="width: 100%; display: flex; flex-direction: column;"><button type="button" data-testid="collapse-button" aria-expanded="true" aria-label="Collapse Checklist" style="color-scheme: unset; forced-color-adjust: unset; mask: unset; math-depth: unset; position: unset; position-anchor: unset; text-size-adjust: unset; appearance: unset; color: unset; font: unset; font-palette: unset; font-synthesis: unset; position-area: unset; text-orientation: unset; text-rendering: unset; text-spacing-trim: unset; -webkit-font-smoothing: unset; -webkit-locale: unset; -webkit-text-orientation: unset; -webkit-writing-mode: unset; writing-mode: unset; zoom: unset; accent-color: unset; place-content: unset; place-items: unset; place-self: unset; alignment-baseline: unset; anchor-name: unset; anchor-scope: unset; animation-composition: unset; animation: unset; app-region: unset; aspect-ratio: unset; backdrop-filter: unset; backface-visibility: unset; background: none; background-blend-mode: unset; baseline-shift: unset; baseline-source: unset; block-size: unset; border-block: unset; border: none; border-radius: unset; border-collapse: unset; border-end-end-radius: unset; border-end-start-radius: unset; border-inline: unset; border-start-end-radius: unset; border-start-start-radius: unset; inset: unset; box-decoration-break: unset; box-shadow: unset; box-sizing: unset; break-after: unset; break-before: unset; break-inside: unset; buffered-rendering: unset; caption-side: unset; caret-color: unset; clear: unset; clip: unset; clip-path: unset; clip-rule: unset; color-interpolation: unset; color-interpolation-filters: unset; color-rendering: unset; columns: unset; column-fill: unset; gap: unset; column-rule: unset; column-span: unset; contain: unset; contain-intrinsic-block-size: unset; contain-intrinsic-size: unset; contain-intrinsic-inline-size: unset; container: unset; content: unset; content-visibility: unset; counter-increment: unset; counter-reset: unset; counter-set: unset; cursor: pointer; cx: unset; cy: unset; d: unset; display: unset; dominant-baseline: unset; empty-cells: unset; field-sizing: unset; fill: unset; fill-opacity: unset; fill-rule: unset; filter: unset; flex: unset; flex-flow: unset; float: unset; flood-color: unset; flood-opacity: unset; grid: unset; grid-area: unset; height: unset; hyphenate-character: unset; hyphenate-limit-chars: unset; hyphens: unset; image-orientation: unset; image-rendering: unset; initial-letter: unset; inline-size: unset; inset-block: unset; inset-inline: unset; interpolate-size: unset; isolation: unset; letter-spacing: unset; lighting-color: unset; line-break: unset; list-style: unset; margin-block: unset; margin: 0px; margin-inline: unset; marker: unset; mask-type: unset; math-shift: unset; math-style: unset; max-block-size: unset; max-height: unset; max-inline-size: unset; max-width: unset; min-block-size: unset; min-height: unset; min-inline-size: unset; min-width: unset; mix-blend-mode: unset; object-fit: unset; object-position: unset; object-view-box: unset; offset: unset; opacity: unset; order: unset; orphans: unset; outline: unset; outline-offset: unset; overflow-anchor: unset; overflow-block: unset; overflow-clip-margin: unset; overflow-inline: unset; overflow-wrap: unset; overflow: unset; overlay: unset; overscroll-behavior-block: unset; overscroll-behavior-inline: unset; overscroll-behavior: unset; padding-block: unset; padding: 0px; padding-inline: unset; page: unset; page-orientation: unset; paint-order: unset; perspective: unset; perspective-origin: unset; pointer-events: unset; position-try: unset; position-visibility: unset; quotes: unset; r: unset; resize: unset; rotate: unset; ruby-align: unset; ruby-position: unset; rx: unset; ry: unset; scale: unset; scroll-behavior: unset; scroll-initial-target: unset; scroll-margin-block: unset; scroll-margin: unset; scroll-margin-inline: unset; scroll-marker-group: unset; scroll-padding-block: unset; scroll-padding: unset; scroll-padding-inline: unset; scroll-snap-align: unset; scroll-snap-stop: unset; scroll-snap-type: unset; scroll-timeline: unset; scrollbar-color: unset; scrollbar-gutter: unset; scrollbar-width: unset; shape-image-threshold: unset; shape-margin: unset; shape-outside: unset; shape-rendering: unset; size: unset; speak: unset; stop-color: unset; stop-opacity: unset; stroke: unset; stroke-dasharray: unset; stroke-dashoffset: unset; stroke-linecap: unset; stroke-linejoin: unset; stroke-miterlimit: unset; stroke-opacity: unset; stroke-width: unset; tab-size: unset; table-layout: unset; text-align: left; text-align-last: unset; text-anchor: unset; text-box: unset; text-combine-upright: unset; text-decoration: unset; text-decoration-skip-ink: unset; text-emphasis: unset; text-emphasis-position: unset; text-indent: unset; text-overflow: unset; text-shadow: unset; text-transform: unset; text-underline-offset: unset; text-underline-position: unset; text-wrap: unset; timeline-scope: unset; touch-action: unset; transform: unset; transform-box: unset; transform-origin: unset; transform-style: unset; transition: unset; translate: unset; user-select: unset; vector-effect: unset; vertical-align: unset; view-timeline: unset; view-transition-class: unset; view-transition-name: unset; visibility: unset; border-spacing: unset; -webkit-box-align: unset; -webkit-box-decoration-break: unset; -webkit-box-direction: unset; -webkit-box-flex: unset; -webkit-box-ordinal-group: unset; -webkit-box-orient: unset; -webkit-box-pack: unset; -webkit-box-reflect: unset; -webkit-line-break: unset; -webkit-line-clamp: unset; -webkit-mask-box-image: unset; -webkit-print-color-adjust: unset; -webkit-rtl-ordering: unset; -webkit-ruby-position: unset; -webkit-tap-highlight-color: unset; -webkit-text-combine: unset; -webkit-text-decorations-in-effect: unset; -webkit-text-fill-color: unset; -webkit-text-security: unset; -webkit-text-stroke: unset; -webkit-user-drag: unset; white-space-collapse: unset; widows: unset; width: 100%; will-change: unset; word-break: unset; word-spacing: unset; x: unset; y: unset; z-index: unset;"><div data-draggable="drag-zone" data-testid="drag-zone" aria-label="Drag to move checklist" class="commandbar-toplevel-9oyx36"><div class="commandbar-toplevel-1rdyw8h"><div style="display: flex; align-items: center;"><div class="commandbar-toplevel-10r3dg1">Learn and earn!</div><div class="commandbar-toplevel-1cbh52g"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m6 9 6 6 6-6"></path></svg></div></div><button aria-label="close" class="commandbar-toplevel-1r11y0c"><div class="commandbar-toplevel-152a6ol"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 6 6 18M6 6l12 12"></path></svg></div></button></div><div class="commandbar-toplevel-10ir9s7"><p>Explore our API and get some more credits on us!</p></div></div><div class="commandbar-toplevel-1n9r659"><div class="commandbar-toplevel-om8qpd">0/4</div><div class="commandbar-toplevel-1tgietq"><div class="commandbar-toplevel-1jrl5cc" style="width: 4%;"></div></div></div></button><div style="height: 293px; overflow: hidden;"><div><div class="commandbar-toplevel-be1ho2"></div><div style="max-height: var(--checklist-max-height); overflow: hidden auto;"><div style="width: 100%; display: flex; flex-direction: column;"><button type="button" data-testid="collapse-button" aria-expanded="true" aria-label="Collapse Checklist" style="color-scheme: unset; forced-color-adjust: unset; mask: unset; math-depth: unset; position: unset; position-anchor: unset; text-size-adjust: unset; appearance: unset; color: unset; font: unset; font-palette: unset; font-synthesis: unset; position-area: unset; text-orientation: unset; text-rendering: unset; text-spacing-trim: unset; -webkit-font-smoothing: unset; -webkit-locale: unset; -webkit-text-orientation: unset; -webkit-writing-mode: unset; writing-mode: unset; zoom: unset; accent-color: unset; place-content: unset; place-items: unset; place-self: unset; alignment-baseline: unset; anchor-name: unset; anchor-scope: unset; animation-composition: unset; animation: unset; app-region: unset; aspect-ratio: unset; backdrop-filter: unset; backface-visibility: unset; background: none; background-blend-mode: unset; baseline-shift: unset; baseline-source: unset; block-size: unset; border-block: unset; border: none; border-radius: unset; border-collapse: unset; border-end-end-radius: unset; border-end-start-radius: unset; border-inline: unset; border-start-end-radius: unset; border-start-start-radius: unset; inset: unset; box-decoration-break: unset; box-shadow: unset; box-sizing: unset; break-after: unset; break-before: unset; break-inside: unset; buffered-rendering: unset; caption-side: unset; caret-color: unset; clear: unset; clip: unset; clip-path: unset; clip-rule: unset; color-interpolation: unset; color-interpolation-filters: unset; color-rendering: unset; columns: unset; column-fill: unset; gap: unset; column-rule: unset; column-span: unset; contain: unset; contain-intrinsic-block-size: unset; contain-intrinsic-size: unset; contain-intrinsic-inline-size: unset; container: unset; content: unset; content-visibility: unset; counter-increment: unset; counter-reset: unset; counter-set: unset; cursor: pointer; cx: unset; cy: unset; d: unset; display: unset; dominant-baseline: unset; empty-cells: unset; field-sizing: unset; fill: unset; fill-opacity: unset; fill-rule: unset; filter: unset; flex: unset; flex-flow: unset; float: unset; flood-color: unset; flood-opacity: unset; grid: unset; grid-area: unset; height: unset; hyphenate-character: unset; hyphenate-limit-chars: unset; hyphens: unset; image-orientation: unset; image-rendering: unset; initial-letter: unset; inline-size: unset; inset-block: unset; inset-inline: unset; interpolate-size: unset; isolation: unset; letter-spacing: unset; lighting-color: unset; line-break: unset; list-style: unset; margin-block: unset; margin: 0px; margin-inline: unset; marker: unset; mask-type: unset; math-shift: unset; math-style: unset; max-block-size: unset; max-height: unset; max-inline-size: unset; max-width: unset; min-block-size: unset; min-height: unset; min-inline-size: unset; min-width: unset; mix-blend-mode: unset; object-fit: unset; object-position: unset; object-view-box: unset; offset: unset; opacity: unset; order: unset; orphans: unset; outline: unset; outline-offset: unset; overflow-anchor: unset; overflow-block: unset; overflow-clip-margin: unset; overflow-inline: unset; overflow-wrap: unset; overflow: unset; overlay: unset; overscroll-behavior-block: unset; overscroll-behavior-inline: unset; overscroll-behavior: unset; padding-block: unset; padding: 0px; padding-inline: unset; page: unset; page-orientation: unset; paint-order: unset; perspective: unset; perspective-origin: unset; pointer-events: unset; position-try: unset; position-visibility: unset; quotes: unset; r: unset; resize: unset; rotate: unset; ruby-align: unset; ruby-position: unset; rx: unset; ry: unset; scale: unset; scroll-behavior: unset; scroll-initial-target: unset; scroll-margin-block: unset; scroll-margin: unset; scroll-margin-inline: unset; scroll-marker-group: unset; scroll-padding-block: unset; scroll-padding: unset; scroll-padding-inline: unset; scroll-snap-align: unset; scroll-snap-stop: unset; scroll-snap-type: unset; scroll-timeline: unset; scrollbar-color: unset; scrollbar-gutter: unset; scrollbar-width: unset; shape-image-threshold: unset; shape-margin: unset; shape-outside: unset; shape-rendering: unset; size: unset; speak: unset; stop-color: unset; stop-opacity: unset; stroke: unset; stroke-dasharray: unset; stroke-dashoffset: unset; stroke-linecap: unset; stroke-linejoin: unset; stroke-miterlimit: unset; stroke-opacity: unset; stroke-width: unset; tab-size: unset; table-layout: unset; text-align: left; text-align-last: unset; text-anchor: unset; text-box: unset; text-combine-upright: unset; text-decoration: unset; text-decoration-skip-ink: unset; text-emphasis: unset; text-emphasis-position: unset; text-indent: unset; text-overflow: unset; text-shadow: unset; text-transform: unset; text-underline-offset: unset; text-underline-position: unset; text-wrap: unset; timeline-scope: unset; touch-action: unset; transform: unset; transform-box: unset; transform-origin: unset; transform-style: unset; transition: unset; translate: unset; user-select: unset; vector-effect: unset; vertical-align: unset; view-timeline: unset; view-transition-class: unset; view-transition-name: unset; visibility: unset; border-spacing: unset; -webkit-box-align: unset; -webkit-box-decoration-break: unset; -webkit-box-direction: unset; -webkit-box-flex: unset; -webkit-box-ordinal-group: unset; -webkit-box-orient: unset; -webkit-box-pack: unset; -webkit-box-reflect: unset; -webkit-line-break: unset; -webkit-line-clamp: unset; -webkit-mask-box-image: unset; -webkit-print-color-adjust: unset; -webkit-rtl-ordering: unset; -webkit-ruby-position: unset; -webkit-tap-highlight-color: unset; -webkit-text-combine: unset; -webkit-text-decorations-in-effect: unset; -webkit-text-fill-color: unset; -webkit-text-security: unset; -webkit-text-stroke: unset; -webkit-user-drag: unset; white-space-collapse: unset; widows: unset; width: 100%; will-change: unset; word-break: unset; word-spacing: unset; x: unset; y: unset; z-index: unset;"><div class="commandbar-toplevel-cegnkb"><div class="commandbar-toplevel-1p1xlo6"></div><div class="commandbar-toplevel-1v1ldgd">Make your first search</div></div></button><div style="height: 108px; overflow: hidden;"><div><div class="commandbar-toplevel-14bnwk3" style="visibility: visible;"><div class="commandbar-toplevel-16n36u3"><p>Try your first Exa search and see the difference</p></div><div class="commandbar-toplevel-1r94d30"><button class="commandbar-toplevel-x76kx5"><span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">Start</span></button><button class="commandbar-toplevel-16qjjh4" style="flex: unset; width: unset;"><span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">Skip</span></button></div></div></div></div></div><div class="commandbar-toplevel-be1ho2"></div><div style="width: 100%; display: flex; flex-direction: column;"><button type="button" data-testid="collapse-button" aria-expanded="false" aria-label="Expand Checklist" style="color-scheme: unset; forced-color-adjust: unset; mask: unset; math-depth: unset; position: unset; position-anchor: unset; text-size-adjust: unset; appearance: unset; color: unset; font: unset; font-palette: unset; font-synthesis: unset; position-area: unset; text-orientation: unset; text-rendering: unset; text-spacing-trim: unset; -webkit-font-smoothing: unset; -webkit-locale: unset; -webkit-text-orientation: unset; -webkit-writing-mode: unset; writing-mode: unset; zoom: unset; accent-color: unset; place-content: unset; place-items: unset; place-self: unset; alignment-baseline: unset; anchor-name: unset; anchor-scope: unset; animation-composition: unset; animation: unset; app-region: unset; aspect-ratio: unset; backdrop-filter: unset; backface-visibility: unset; background: none; background-blend-mode: unset; baseline-shift: unset; baseline-source: unset; block-size: unset; border-block: unset; border: none; border-radius: unset; border-collapse: unset; border-end-end-radius: unset; border-end-start-radius: unset; border-inline: unset; border-start-end-radius: unset; border-start-start-radius: unset; inset: unset; box-decoration-break: unset; box-shadow: unset; box-sizing: unset; break-after: unset; break-before: unset; break-inside: unset; buffered-rendering: unset; caption-side: unset; caret-color: unset; clear: unset; clip: unset; clip-path: unset; clip-rule: unset; color-interpolation: unset; color-interpolation-filters: unset; color-rendering: unset; columns: unset; column-fill: unset; gap: unset; column-rule: unset; column-span: unset; contain: unset; contain-intrinsic-block-size: unset; contain-intrinsic-size: unset; contain-intrinsic-inline-size: unset; container: unset; content: unset; content-visibility: unset; counter-increment: unset; counter-reset: unset; counter-set: unset; cursor: pointer; cx: unset; cy: unset; d: unset; display: unset; dominant-baseline: unset; empty-cells: unset; field-sizing: unset; fill: unset; fill-opacity: unset; fill-rule: unset; filter: unset; flex: unset; flex-flow: unset; float: unset; flood-color: unset; flood-opacity: unset; grid: unset; grid-area: unset; height: unset; hyphenate-character: unset; hyphenate-limit-chars: unset; hyphens: unset; image-orientation: unset; image-rendering: unset; initial-letter: unset; inline-size: unset; inset-block: unset; inset-inline: unset; interpolate-size: unset; isolation: unset; letter-spacing: unset; lighting-color: unset; line-break: unset; list-style: unset; margin-block: unset; margin: 0px; margin-inline: unset; marker: unset; mask-type: unset; math-shift: unset; math-style: unset; max-block-size: unset; max-height: unset; max-inline-size: unset; max-width: unset; min-block-size: unset; min-height: unset; min-inline-size: unset; min-width: unset; mix-blend-mode: unset; object-fit: unset; object-position: unset; object-view-box: unset; offset: unset; opacity: unset; order: unset; orphans: unset; outline: unset; outline-offset: unset; overflow-anchor: unset; overflow-block: unset; overflow-clip-margin: unset; overflow-inline: unset; overflow-wrap: unset; overflow: unset; overlay: unset; overscroll-behavior-block: unset; overscroll-behavior-inline: unset; overscroll-behavior: unset; padding-block: unset; padding: 0px; padding-inline: unset; page: unset; page-orientation: unset; paint-order: unset; perspective: unset; perspective-origin: unset; pointer-events: unset; position-try: unset; position-visibility: unset; quotes: unset; r: unset; resize: unset; rotate: unset; ruby-align: unset; ruby-position: unset; rx: unset; ry: unset; scale: unset; scroll-behavior: unset; scroll-initial-target: unset; scroll-margin-block: unset; scroll-margin: unset; scroll-margin-inline: unset; scroll-marker-group: unset; scroll-padding-block: unset; scroll-padding: unset; scroll-padding-inline: unset; scroll-snap-align: unset; scroll-snap-stop: unset; scroll-snap-type: unset; scroll-timeline: unset; scrollbar-color: unset; scrollbar-gutter: unset; scrollbar-width: unset; shape-image-threshold: unset; shape-margin: unset; shape-outside: unset; shape-rendering: unset; size: unset; speak: unset; stop-color: unset; stop-opacity: unset; stroke: unset; stroke-dasharray: unset; stroke-dashoffset: unset; stroke-linecap: unset; stroke-linejoin: unset; stroke-miterlimit: unset; stroke-opacity: unset; stroke-width: unset; tab-size: unset; table-layout: unset; text-align: left; text-align-last: unset; text-anchor: unset; text-box: unset; text-combine-upright: unset; text-decoration: unset; text-decoration-skip-ink: unset; text-emphasis: unset; text-emphasis-position: unset; text-indent: unset; text-overflow: unset; text-shadow: unset; text-transform: unset; text-underline-offset: unset; text-underline-position: unset; text-wrap: unset; timeline-scope: unset; touch-action: unset; transform: unset; transform-box: unset; transform-origin: unset; transform-style: unset; transition: unset; translate: unset; user-select: unset; vector-effect: unset; vertical-align: unset; view-timeline: unset; view-transition-class: unset; view-transition-name: unset; visibility: unset; border-spacing: unset; -webkit-box-align: unset; -webkit-box-decoration-break: unset; -webkit-box-direction: unset; -webkit-box-flex: unset; -webkit-box-ordinal-group: unset; -webkit-box-orient: unset; -webkit-box-pack: unset; -webkit-box-reflect: unset; -webkit-line-break: unset; -webkit-line-clamp: unset; -webkit-mask-box-image: unset; -webkit-print-color-adjust: unset; -webkit-rtl-ordering: unset; -webkit-ruby-position: unset; -webkit-tap-highlight-color: unset; -webkit-text-combine: unset; -webkit-text-decorations-in-effect: unset; -webkit-text-fill-color: unset; -webkit-text-security: unset; -webkit-text-stroke: unset; -webkit-user-drag: unset; white-space-collapse: unset; widows: unset; width: 100%; will-change: unset; word-break: unset; word-spacing: unset; x: unset; y: unset; z-index: unset;"><div class="commandbar-toplevel-1kk896k"><div class="commandbar-toplevel-1p1xlo6"></div><div class="commandbar-toplevel-1v1ldgd">Try a filter</div></div></button><div style="height: 0px; overflow: hidden;"><div><div class="commandbar-toplevel-14bnwk3" style="visibility: hidden;"><div class="commandbar-toplevel-16n36u3"><p>Narrow your search with our filters</p></div><div class="commandbar-toplevel-1r94d30"><button class="commandbar-toplevel-x76kx5"><span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">Start</span></button><button class="commandbar-toplevel-16qjjh4" style="flex: unset; width: unset;"><span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">Skip</span></button></div></div></div></div></div><div class="commandbar-toplevel-be1ho2"></div><div style="width: 100%; display: flex; flex-direction: column;"><button type="button" data-testid="collapse-button" aria-expanded="false" aria-label="Expand Checklist" style="color-scheme: unset; forced-color-adjust: unset; mask: unset; math-depth: unset; position: unset; position-anchor: unset; text-size-adjust: unset; appearance: unset; color: unset; font: unset; font-palette: unset; font-synthesis: unset; position-area: unset; text-orientation: unset; text-rendering: unset; text-spacing-trim: unset; -webkit-font-smoothing: unset; -webkit-locale: unset; -webkit-text-orientation: unset; -webkit-writing-mode: unset; writing-mode: unset; zoom: unset; accent-color: unset; place-content: unset; place-items: unset; place-self: unset; alignment-baseline: unset; anchor-name: unset; anchor-scope: unset; animation-composition: unset; animation: unset; app-region: unset; aspect-ratio: unset; backdrop-filter: unset; backface-visibility: unset; background: none; background-blend-mode: unset; baseline-shift: unset; baseline-source: unset; block-size: unset; border-block: unset; border: none; border-radius: unset; border-collapse: unset; border-end-end-radius: unset; border-end-start-radius: unset; border-inline: unset; border-start-end-radius: unset; border-start-start-radius: unset; inset: unset; box-decoration-break: unset; box-shadow: unset; box-sizing: unset; break-after: unset; break-before: unset; break-inside: unset; buffered-rendering: unset; caption-side: unset; caret-color: unset; clear: unset; clip: unset; clip-path: unset; clip-rule: unset; color-interpolation: unset; color-interpolation-filters: unset; color-rendering: unset; columns: unset; column-fill: unset; gap: unset; column-rule: unset; column-span: unset; contain: unset; contain-intrinsic-block-size: unset; contain-intrinsic-size: unset; contain-intrinsic-inline-size: unset; container: unset; content: unset; content-visibility: unset; counter-increment: unset; counter-reset: unset; counter-set: unset; cursor: pointer; cx: unset; cy: unset; d: unset; display: unset; dominant-baseline: unset; empty-cells: unset; field-sizing: unset; fill: unset; fill-opacity: unset; fill-rule: unset; filter: unset; flex: unset; flex-flow: unset; float: unset; flood-color: unset; flood-opacity: unset; grid: unset; grid-area: unset; height: unset; hyphenate-character: unset; hyphenate-limit-chars: unset; hyphens: unset; image-orientation: unset; image-rendering: unset; initial-letter: unset; inline-size: unset; inset-block: unset; inset-inline: unset; interpolate-size: unset; isolation: unset; letter-spacing: unset; lighting-color: unset; line-break: unset; list-style: unset; margin-block: unset; margin: 0px; margin-inline: unset; marker: unset; mask-type: unset; math-shift: unset; math-style: unset; max-block-size: unset; max-height: unset; max-inline-size: unset; max-width: unset; min-block-size: unset; min-height: unset; min-inline-size: unset; min-width: unset; mix-blend-mode: unset; object-fit: unset; object-position: unset; object-view-box: unset; offset: unset; opacity: unset; order: unset; orphans: unset; outline: unset; outline-offset: unset; overflow-anchor: unset; overflow-block: unset; overflow-clip-margin: unset; overflow-inline: unset; overflow-wrap: unset; overflow: unset; overlay: unset; overscroll-behavior-block: unset; overscroll-behavior-inline: unset; overscroll-behavior: unset; padding-block: unset; padding: 0px; padding-inline: unset; page: unset; page-orientation: unset; paint-order: unset; perspective: unset; perspective-origin: unset; pointer-events: unset; position-try: unset; position-visibility: unset; quotes: unset; r: unset; resize: unset; rotate: unset; ruby-align: unset; ruby-position: unset; rx: unset; ry: unset; scale: unset; scroll-behavior: unset; scroll-initial-target: unset; scroll-margin-block: unset; scroll-margin: unset; scroll-margin-inline: unset; scroll-marker-group: unset; scroll-padding-block: unset; scroll-padding: unset; scroll-padding-inline: unset; scroll-snap-align: unset; scroll-snap-stop: unset; scroll-snap-type: unset; scroll-timeline: unset; scrollbar-color: unset; scrollbar-gutter: unset; scrollbar-width: unset; shape-image-threshold: unset; shape-margin: unset; shape-outside: unset; shape-rendering: unset; size: unset; speak: unset; stop-color: unset; stop-opacity: unset; stroke: unset; stroke-dasharray: unset; stroke-dashoffset: unset; stroke-linecap: unset; stroke-linejoin: unset; stroke-miterlimit: unset; stroke-opacity: unset; stroke-width: unset; tab-size: unset; table-layout: unset; text-align: left; text-align-last: unset; text-anchor: unset; text-box: unset; text-combine-upright: unset; text-decoration: unset; text-decoration-skip-ink: unset; text-emphasis: unset; text-emphasis-position: unset; text-indent: unset; text-overflow: unset; text-shadow: unset; text-transform: unset; text-underline-offset: unset; text-underline-position: unset; text-wrap: unset; timeline-scope: unset; touch-action: unset; transform: unset; transform-box: unset; transform-origin: unset; transform-style: unset; transition: unset; translate: unset; user-select: unset; vector-effect: unset; vertical-align: unset; view-timeline: unset; view-transition-class: unset; view-transition-name: unset; visibility: unset; border-spacing: unset; -webkit-box-align: unset; -webkit-box-decoration-break: unset; -webkit-box-direction: unset; -webkit-box-flex: unset; -webkit-box-ordinal-group: unset; -webkit-box-orient: unset; -webkit-box-pack: unset; -webkit-box-reflect: unset; -webkit-line-break: unset; -webkit-line-clamp: unset; -webkit-mask-box-image: unset; -webkit-print-color-adjust: unset; -webkit-rtl-ordering: unset; -webkit-ruby-position: unset; -webkit-tap-highlight-color: unset; -webkit-text-combine: unset; -webkit-text-decorations-in-effect: unset; -webkit-text-fill-color: unset; -webkit-text-security: unset; -webkit-text-stroke: unset; -webkit-user-drag: unset; white-space-collapse: unset; widows: unset; width: 100%; will-change: unset; word-break: unset; word-spacing: unset; x: unset; y: unset; z-index: unset;"><div class="commandbar-toplevel-1kk896k"><div class="commandbar-toplevel-1p1xlo6"></div><div class="commandbar-toplevel-1v1ldgd">Invite a team member</div></div></button><div style="height: 0px; overflow: hidden;"><div><div class="commandbar-toplevel-14bnwk3" style="visibility: hidden;"><div class="commandbar-toplevel-16n36u3"><p>Exa works best with collaborators. Add a team member to get</p></div><div class="commandbar-toplevel-1r94d30"><button class="commandbar-toplevel-x76kx5"><span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">Start</span></button><button class="commandbar-toplevel-16qjjh4" style="flex: unset; width: unset;"><span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">Skip</span></button></div></div></div></div></div><div class="commandbar-toplevel-be1ho2"></div><div style="width: 100%; display: flex; flex-direction: column;"><button type="button" data-testid="collapse-button" aria-expanded="false" aria-label="Expand Checklist" style="color-scheme: unset; forced-color-adjust: unset; mask: unset; math-depth: unset; position: unset; position-anchor: unset; text-size-adjust: unset; appearance: unset; color: unset; font: unset; font-palette: unset; font-synthesis: unset; position-area: unset; text-orientation: unset; text-rendering: unset; text-spacing-trim: unset; -webkit-font-smoothing: unset; -webkit-locale: unset; -webkit-text-orientation: unset; -webkit-writing-mode: unset; writing-mode: unset; zoom: unset; accent-color: unset; place-content: unset; place-items: unset; place-self: unset; alignment-baseline: unset; anchor-name: unset; anchor-scope: unset; animation-composition: unset; animation: unset; app-region: unset; aspect-ratio: unset; backdrop-filter: unset; backface-visibility: unset; background: none; background-blend-mode: unset; baseline-shift: unset; baseline-source: unset; block-size: unset; border-block: unset; border: none; border-radius: unset; border-collapse: unset; border-end-end-radius: unset; border-end-start-radius: unset; border-inline: unset; border-start-end-radius: unset; border-start-start-radius: unset; inset: unset; box-decoration-break: unset; box-shadow: unset; box-sizing: unset; break-after: unset; break-before: unset; break-inside: unset; buffered-rendering: unset; caption-side: unset; caret-color: unset; clear: unset; clip: unset; clip-path: unset; clip-rule: unset; color-interpolation: unset; color-interpolation-filters: unset; color-rendering: unset; columns: unset; column-fill: unset; gap: unset; column-rule: unset; column-span: unset; contain: unset; contain-intrinsic-block-size: unset; contain-intrinsic-size: unset; contain-intrinsic-inline-size: unset; container: unset; content: unset; content-visibility: unset; counter-increment: unset; counter-reset: unset; counter-set: unset; cursor: pointer; cx: unset; cy: unset; d: unset; display: unset; dominant-baseline: unset; empty-cells: unset; field-sizing: unset; fill: unset; fill-opacity: unset; fill-rule: unset; filter: unset; flex: unset; flex-flow: unset; float: unset; flood-color: unset; flood-opacity: unset; grid: unset; grid-area: unset; height: unset; hyphenate-character: unset; hyphenate-limit-chars: unset; hyphens: unset; image-orientation: unset; image-rendering: unset; initial-letter: unset; inline-size: unset; inset-block: unset; inset-inline: unset; interpolate-size: unset; isolation: unset; letter-spacing: unset; lighting-color: unset; line-break: unset; list-style: unset; margin-block: unset; margin: 0px; margin-inline: unset; marker: unset; mask-type: unset; math-shift: unset; math-style: unset; max-block-size: unset; max-height: unset; max-inline-size: unset; max-width: unset; min-block-size: unset; min-height: unset; min-inline-size: unset; min-width: unset; mix-blend-mode: unset; object-fit: unset; object-position: unset; object-view-box: unset; offset: unset; opacity: unset; order: unset; orphans: unset; outline: unset; outline-offset: unset; overflow-anchor: unset; overflow-block: unset; overflow-clip-margin: unset; overflow-inline: unset; overflow-wrap: unset; overflow: unset; overlay: unset; overscroll-behavior-block: unset; overscroll-behavior-inline: unset; overscroll-behavior: unset; padding-block: unset; padding: 0px; padding-inline: unset; page: unset; page-orientation: unset; paint-order: unset; perspective: unset; perspective-origin: unset; pointer-events: unset; position-try: unset; position-visibility: unset; quotes: unset; r: unset; resize: unset; rotate: unset; ruby-align: unset; ruby-position: unset; rx: unset; ry: unset; scale: unset; scroll-behavior: unset; scroll-initial-target: unset; scroll-margin-block: unset; scroll-margin: unset; scroll-margin-inline: unset; scroll-marker-group: unset; scroll-padding-block: unset; scroll-padding: unset; scroll-padding-inline: unset; scroll-snap-align: unset; scroll-snap-stop: unset; scroll-snap-type: unset; scroll-timeline: unset; scrollbar-color: unset; scrollbar-gutter: unset; scrollbar-width: unset; shape-image-threshold: unset; shape-margin: unset; shape-outside: unset; shape-rendering: unset; size: unset; speak: unset; stop-color: unset; stop-opacity: unset; stroke: unset; stroke-dasharray: unset; stroke-dashoffset: unset; stroke-linecap: unset; stroke-linejoin: unset; stroke-miterlimit: unset; stroke-opacity: unset; stroke-width: unset; tab-size: unset; table-layout: unset; text-align: left; text-align-last: unset; text-anchor: unset; text-box: unset; text-combine-upright: unset; text-decoration: unset; text-decoration-skip-ink: unset; text-emphasis: unset; text-emphasis-position: unset; text-indent: unset; text-overflow: unset; text-shadow: unset; text-transform: unset; text-underline-offset: unset; text-underline-position: unset; text-wrap: unset; timeline-scope: unset; touch-action: unset; transform: unset; transform-box: unset; transform-origin: unset; transform-style: unset; transition: unset; translate: unset; user-select: unset; vector-effect: unset; vertical-align: unset; view-timeline: unset; view-transition-class: unset; view-transition-name: unset; visibility: unset; border-spacing: unset; -webkit-box-align: unset; -webkit-box-decoration-break: unset; -webkit-box-direction: unset; -webkit-box-flex: unset; -webkit-box-ordinal-group: unset; -webkit-box-orient: unset; -webkit-box-pack: unset; -webkit-box-reflect: unset; -webkit-line-break: unset; -webkit-line-clamp: unset; -webkit-mask-box-image: unset; -webkit-print-color-adjust: unset; -webkit-rtl-ordering: unset; -webkit-ruby-position: unset; -webkit-tap-highlight-color: unset; -webkit-text-combine: unset; -webkit-text-decorations-in-effect: unset; -webkit-text-fill-color: unset; -webkit-text-security: unset; -webkit-text-stroke: unset; -webkit-user-drag: unset; white-space-collapse: unset; widows: unset; width: 100%; will-change: unset; word-break: unset; word-spacing: unset; x: unset; y: unset; z-index: unset;"><div class="commandbar-toplevel-1kk896k"><div class="commandbar-toplevel-1p1xlo6"></div><div class="commandbar-toplevel-1v1ldgd">Tell us what you are using Exa for</div></div></button><div style="height: 0px; overflow: hidden;"><div><div class="commandbar-toplevel-14bnwk3" style="visibility: hidden;"><div class="commandbar-toplevel-16n36u3"><p>Let us know what your use case is so we can improve the product for you</p></div><div class="commandbar-toplevel-1r94d30"><button class="commandbar-toplevel-x76kx5"><span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">Start</span></button><button class="commandbar-toplevel-16qjjh4" style="flex: unset; width: unset;"><span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">Skip</span></button></div></div></div></div></div><div class="commandbar-toplevel-be1ho2"></div></div></div></div><div class="commandbar-toplevel-1ewkueq" style="justify-content: space-between;"><div class="commandbar-toplevel-dr9jhu"><svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.0831 5.91814L12.7927 6.90401L11.5289 7.63423L10.3031 6.92761C10.2022 6.86993 10.1301 6.79651 10.0724 6.69556L8.99998 4.8392L7.92759 6.69556C7.86991 6.79651 7.79649 6.86993 7.69555 6.92761C5.60189 8.13634 3.50693 9.34508 1.41459 10.5538L8.35366 14.5589C8.76532 14.7962 9.23596 14.7962 9.64761 14.5589L16.5841 10.5538L14.0565 9.09468L15.3216 8.36446L17.216 9.45782C17.6184 9.68987 17.8479 10.0884 17.8479 10.5525C17.8479 11.0166 17.6171 11.4151 17.216 11.6472L10.2979 15.6431C9.47325 16.119 8.52934 16.119 7.70604 15.6431L0.785309 11.6472C0.382834 11.4151 0.153411 11.0166 0.153411 10.5525C0.153411 10.0884 0.384145 9.68987 0.785309 9.45782C2.83046 8.27662 4.87429 7.09673 6.91813 5.91683L8.4533 3.25814C8.56998 3.05756 8.76925 2.94219 9.00129 2.94219C9.23334 2.94219 9.43261 3.05756 9.54798 3.25814L11.0831 5.91683V5.91814ZM11.0831 10.0819L9.54798 12.7405C9.43261 12.9411 9.23334 13.0565 9.00129 13.0565C8.76925 13.0565 8.56998 12.9411 8.4533 12.7405L6.91813 10.0819L5.20859 9.09599L6.4737 8.36577L7.69686 9.07239C7.7978 9.13007 7.87122 9.20218 7.9289 9.30313L9.00129 11.1608L10.0737 9.30313C10.1314 9.20218 10.2035 9.13007 10.3044 9.07239C12.3994 7.86366 14.493 6.65492 16.5854 5.44619L9.64892 1.44111C9.23727 1.20251 8.76663 1.20251 8.35498 1.44111L1.4159 5.44619L3.94349 6.90532L2.67969 7.63554L0.783998 6.54218C0.381523 6.31013 0.1521 5.91159 0.1521 5.4475C0.1521 4.98341 0.382834 4.58487 0.783998 4.35282L7.70472 0.356918C8.52803 -0.118973 9.47194 -0.118973 10.2966 0.356918L17.2147 4.35282C17.6171 4.58487 17.8466 4.98341 17.8466 5.4475C17.8466 5.91159 17.6158 6.31013 17.2147 6.54218C15.1708 7.72338 13.1257 8.90327 11.0818 10.0832L11.0831 10.0819Z" fill="#706E78"></path></svg>Powered by Command AI</div></div></div></div></div><div><div id="commandbar-nudges-rc-tooltip-container"></div></div><div id="commandbar-nudge-container" style="position: relative;"><div id="commandbar-toast-container" class="commandbar-toplevel-ey5axd" style="position: fixed; pointer-events: none; z-index: 2147483644;"></div></div><div id="commandbar-nudges-banner-sticky-container" style="width: 100%; top: 0px; position: fixed; pointer-events: none; z-index: 2147483644; height: 100%;"></div><div id="commandbar-nudges-banner-inline-container-top" style="width: 100%; position: absolute; top: 0px; pointer-events: none; z-index: 2147483644;"></div><div id="commandbar-nudges-banner-inline-container-bottom" style="width: 100%; position: relative; pointer-events: none; z-index: 2147483644;"></div><div class="cb-widget-ce80a7eb65-1" style="display: contents;"></div></div></div></div><script async="" src="https://cdn.commandbar.com/prod/commandbar/5cc6f6627f160edc37af6f371ee8ff638b270b3c/split/index.js?cb-snippet=1&amp;org_uuid=83fe2cea" type="module" data-commandbar="1"></script><link rel="stylesheet" type="text/css" href="https://cdn.commandbar.com/prod/commandbar/5cc6f6627f160edc37af6f371ee8ff638b270b3c/split/index.css?cb-snippet=1&amp;org_uuid=83fe2cea" data-commandbar="1"></div><iframe allow="join-ad-interest-group" data-tagging-id="AW-16860319264/u2XuCJ2A4dIaEKCc0Oc-" data-load-time="1754412874301" height="0" width="0" src="https://td.doubleclick.net/td/rul/16860319264?random=1754412874294&amp;cv=11&amp;fst=1754412874294&amp;fmt=3&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=purchase&amp;gcl_ctr=2&amp;gtm=45be5840v9210115056z89195906795za200zd9210115056xea&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=101509157~103116026~103200004~103233427~104527907~104528501~104684208~104684211~104948813~105087538~105087540~105103161~105103163&amp;u_w=1661&amp;u_h=1053&amp;url=https%3A%2F%2Fdashboard.exa.ai%2Fhome&amp;ref=https%3A%2F%2Fdashboard.exa.ai%2Flogin%3Femail%3Dcassandra579%2540ai.whatisinitfor.me%26otp%3D031704%26redirect%3Dhttps%253A%252F%252Fdashboard.exa.ai%252F&amp;label=u2XuCJ2A4dIaEKCc0Oc-&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=Home%20%7C%20Exa%20API&amp;value=0&amp;bttype=purchase&amp;npa=0&amp;pscdl=noapi&amp;auid=910274139.1754412798&amp;uaa=x86&amp;uab=64&amp;uafvl=%2522Edge%2522%3B135.0.7049.95%7CNot-A.Brand%3B8.0.0.0%7CChromium%3B135.0.7049.95&amp;uamb=0&amp;uam=&amp;uap=macOS&amp;uapv=%2215.2.0%22&amp;uaw=0&amp;ec_mode=a&amp;oid=563299220.1754412874&amp;ecsid=1292802002.1754412817&amp;fledge=1&amp;capi=2&amp;_tu=ChA&amp;em=tv.1&amp;ct_cookie_present=0" style="display: none; visibility: hidden;"></iframe><iframe allow="join-ad-interest-group" data-tagging-id="AW-16860319264" data-load-time="1754412876318" height="0" width="0" src="https://td.doubleclick.net/td/rul/16860319264?random=1754412876307&amp;cv=11&amp;fst=1754412876307&amp;fmt=3&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=page_view&amp;gtm=45be5840v9210115056za200zd9210115056xec&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=101509157~103116026~103200004~103233427~104527907~104528501~104684208~104684211~104948813~105087538~105087540~105103161~105103163&amp;u_w=1661&amp;u_h=1053&amp;url=https%3A%2F%2Fdashboard.exa.ai%2Fplayground%2Fsearch%3Fq%3Dblog%2520post%2520about%2520AI%26filters%3D%257B%2522text%2522%253A%2522true%2522%252C%2522type%2522%253A%2522auto%2522%257D&amp;ref=https%3A%2F%2Fdashboard.exa.ai%2Fhome&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=Playground%20%7C%20Exa%20API&amp;npa=0&amp;pscdl=noapi&amp;auid=910274139.1754412798&amp;uaa=x86&amp;uab=64&amp;uafvl=%2522Edge%2522%3B135.0.7049.95%7CNot-A.Brand%3B8.0.0.0%7CChromium%3B135.0.7049.95&amp;uamb=0&amp;uam=&amp;uap=macOS&amp;uapv=%2215.2.0%22&amp;uaw=0&amp;fledge=1&amp;data=event%3Dpage_view" style="display: none; visibility: hidden;"></iframe><iframe name="__privateStripeMetricsController9660" frameborder="0" allowtransparency="true" scrolling="no" role="presentation" allow="payment *" src="https://js.stripe.com/v3/m-outer-3437aaddcdf6922d623e172c2d6f9278.html#url=https%3A%2F%2Fdashboard.exa.ai%2Fhome&amp;title=Home%20%7C%20Exa%20API&amp;referrer=https%3A%2F%2Fdashboard.exa.ai%2Flogin%3Femail%3Dcassandra579%2540ai.whatisinitfor.me%26otp%3D031704%26redirect%3Dhttps%253A%252F%252Fdashboard.exa.ai%252F&amp;muid=NA&amp;sid=NA&amp;version=6&amp;preview=false&amp;__shared_params__[version]=v3" aria-hidden="true" tabindex="-1" style="border: none !important; margin: 0px !important; padding: 0px !important; width: 1px !important; min-width: 100% !important; overflow: hidden !important; display: block !important; visibility: hidden !important; position: fixed !important; height: 1px !important; pointer-events: none !important; user-select: none !important;"></iframe></body></html>