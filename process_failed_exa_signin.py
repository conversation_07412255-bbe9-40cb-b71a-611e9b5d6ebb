"""
This script processes failed ExaAI sign-in attempts by reusing archived emails.

This is the final, corrected version implementing a robust, linear workflow that
faithfully mirrors the user's guidance and interactive session discoveries.

Key Logic:
1.  A fresh, isolated browser session is created for each account.
2.  Navigate to the magic link, which ALWAYS lands on the verification page.
3.  Click "VERIFY CODE" to test the initial code.
4.  After clicking, intelligently "read the screen" by performing a sequence of
    actions to robustly verify the dashboard is fully loaded and usable.
    a. If verification succeeds, proceed.
    b. If verification fails, the full "resend" flow is initiated.
5.  Once logged in, simulate human activity.
6.  Extract the default API key via the clipboard.
7.  Save credentials to a structured YAML file.
8.  Move the processed email to 'done' or 'discard'.
"""

import asyncio
import logging
import re
import time
from pathlib import Path
import random
import argparse
from typing import Optional

from playwright.async_api import Page, TimeoutError as PlaywrightTimeoutError

from core.browser import BrowserManager
from core.models import ServiceCredentials
from core.results_storage import ResultsStorage
from services.exaai import ExaAIService
from core.webhook_client import WebhookClient

# --- Setup ---
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

WIP_DIR = Path("data/wip/exaai/email")
DONE_DIR = Path("data/done/exaai/email")
DISCARD_DIR = Path("data/discard/exaai/email")
DEBUG_DIR = Path("debug")


def _parse_verification_code(content: str) -> Optional[str]:
    """Parses the 6-digit verification code from email content."""
    match = re.search(r"otp=(\d{6})", content)
    if match:
        return match.group(1)
    match = re.search(r"Your verification code for Exa Dashboard is: (\d{6})", content)
    if match:
        return match.group(1)
    return None


class EmailProcessor:
    """Reads and parses an email file."""

    def __init__(self, file_path: Path):
        self.file_path = file_path
        self.email_address: str | None = None
        self.dashboard_link: str | None = None

    def read_and_parse(self) -> bool:
        """Reads email file, extracting email address and link."""
        try:
            content = self.file_path.read_text()
            match = re.search(r"(?:\d{8}_\d{6}_|email_)(.+)\.txt", self.file_path.name)
            if not match:
                logger.error(
                    f"Could not parse email address from filename: {self.file_path.name}"
                )
                return False
            self.email_address = match.group(1).replace("_", "@", 1).replace("_", ".")
            self.dashboard_link = ExaAIService.parse_magic_link(content)
            if not self.dashboard_link:
                logger.error(
                    f"Could not parse dashboard link from email: {self.file_path.name}"
                )
                return False
            logger.info(f"Successfully parsed email for: {self.email_address}")
            return True
        except Exception as e:
            logger.error(
                f"Failed to read or parse email file {self.file_path.name}: {e}"
            )
            return False


class RecoveryOrchestrator:
    """Orchestrates the entire recovery and API key extraction process."""

    def __init__(self, headless: bool = True):
        self.headless = headless
        self.storage = ResultsStorage()
        self.webhook_client = WebhookClient()

    async def _debug_step(self, page, name: str):
        DEBUG_DIR.mkdir(exist_ok=True)
        screenshot_path = DEBUG_DIR / f"step_{name}.png"
        await page.screenshot(path=str(screenshot_path))
        logger.info(f"📸 Saved screenshot: {screenshot_path}")

    async def _get_new_verification_data(self, email_address: str) -> dict | None:
        """Polls the webhook for a new verification email with both code and link."""
        logger.info(
            f"Polling webhook for new verification email for {email_address}..."
        )
        start_time = time.time()
        while time.time() - start_time < 90:
            email_data = await self.webhook_client.get_latest_email_data(
                "exaai", email_address
            )
            if email_data and email_data.get("raw"):
                # Parse both verification code and magic link from new email
                raw_content = email_data["raw"]
                code = _parse_verification_code(raw_content)
                magic_link = ExaAIService.parse_magic_link(raw_content)

                if code and magic_link:
                    logger.info(f"Found new verification email with code: {code}")
                    return {"code": code, "magic_link": magic_link, "raw": raw_content}
            await asyncio.sleep(5)
        logger.error("Timed out waiting for new verification email.")
        return None

    async def _verify_dashboard_access(self, page: Page) -> bool:
        """Robustly checks if the dashboard is fully loaded and interactive."""
        try:
            logger.info("Verifying dashboard access...")

            # Wait for the page to settle and animations to complete
            await asyncio.sleep(5)

            # Check current URL to understand where we are
            current_url = page.url
            logger.info(f"Current URL: {current_url}")

            # Take screenshot to analyze current state
            await self._debug_step(page, "verify_dashboard_state")

            # Check for error messages first - be more liberal in detection
            error_indicators = [
                'text="Invalid or expired verification code"',
                'text="Code has expired"',
                'text="Verification failed"',
                '*text*="invalid"*',
                '*text*="expired"*',
                '*text*="error"*',
                '[class*="error"]',
                '[class*="invalid"]',
            ]

            error_found = False
            for error_selector in error_indicators:
                try:
                    error_element = await page.query_selector(error_selector)
                    if error_element and await error_element.is_visible():
                        logger.warning(f"Found error indicator: {error_selector}")
                        error_found = True
                        break
                except:
                    continue

            # Also check page text content for error messages
            try:
                page_text = await page.text_content("body")
                if page_text:
                    error_keywords = ["invalid", "expired", "error", "failed", "wrong"]
                    for keyword in error_keywords:
                        if keyword.lower() in page_text.lower():
                            logger.warning(
                                f"Found error keyword in page text: {keyword}"
                            )
                            error_found = True
                            break
            except:
                pass

            if error_found:
                return False

            # Check if we're still on verification page
            verification_indicators = [
                'button:has-text("VERIFY CODE")',
                'button:has-text("Resend verification email")',
                'input[aria-label="Enter verification code"]',
                'text="Enter the 6-digit code"',
                'text="Verification"',
            ]

            still_on_verification = False
            for verify_selector in verification_indicators:
                try:
                    verify_element = await page.query_selector(verify_selector)
                    if verify_element and await verify_element.is_visible():
                        logger.info(
                            f"Still on verification page - found: {verify_selector}"
                        )
                        still_on_verification = True
                        break
                except:
                    continue

            if still_on_verification:
                logger.warning("Still on verification page, login not successful")
                return False

            # Check if we're on the actual dashboard (not login page)
            if "/login" in current_url or "/verify" in current_url:
                logger.warning(f"Still on login/verify page: {current_url}")
                return False

            # Look for dashboard elements that indicate successful login
            dashboard_indicators = [
                'a:has-text("Search")',
                'a:has-text("API Keys")',
                'a:has-text("Playground")',
                'button:has-text("Run")',
                "nav",
                ".nav",
                '[data-testid*="nav"]',
                "header",
                ".header",
            ]

            found_dashboard = False
            for indicator in dashboard_indicators:
                try:
                    element = await page.query_selector(indicator)
                    if element and await element.is_visible():
                        logger.info(f"✅ Found dashboard indicator: {indicator}")
                        found_dashboard = True
                        break
                except:
                    continue

            if not found_dashboard:
                logger.warning("❌ No dashboard navigation elements found")
                # Take another screenshot to see what's actually on screen
                await self._debug_step(page, "no_dashboard_elements_found")

                # Check page title and body text for clues
                try:
                    page_title = await page.title()
                    logger.info(f"Page title: {page_title}")

                    body_text = await page.text_content("body")
                    if body_text:
                        body_preview = body_text[:200].replace("\n", " ").strip()
                        logger.info(f"Body text preview: {body_preview}")
                except:
                    pass

                return False

            logger.info("✅ Dashboard access verified successfully")
            return True

        except Exception as e:
            logger.error(f"Error verifying dashboard access: {e}")
            await self._debug_step(page, f"verify_dashboard_error")
            return False

    async def process_email(self, processor: EmailProcessor) -> bool:
        """Processes a single email to recover an API key."""
        if not all([processor.dashboard_link, processor.email_address]):
            return False

        browser_manager = BrowserManager(
            headless=self.headless, permissions=["clipboard-read", "clipboard-write"]
        )
        try:
            await browser_manager.start()
            page = browser_manager.page

            logger.info(
                f"🔗 Navigating to verification page for {processor.email_address}..."
            )
            await page.goto(processor.dashboard_link, wait_until="domcontentloaded")
            await asyncio.sleep(3)  # Let page settle
            await self._debug_step(
                page, f"01_landed_{processor.email_address.replace('@', '_')}"
            )

            # Check what page we landed on
            current_url = page.url
            logger.info(f"📍 Landed on: {current_url}")

            # Look for verification page elements
            verification_page = False
            try:
                verify_button = await page.query_selector(
                    'button:has-text("VERIFY CODE")'
                )
                if verify_button and await verify_button.is_visible():
                    verification_page = True
                    logger.info("✅ On verification page, found VERIFY CODE button")
            except:
                pass

            if not verification_page:
                logger.warning("❌ Not on verification page or button not found")
                await self._debug_step(
                    page,
                    f"01_not_verification_page_{processor.email_address.replace('@', '_')}",
                )
                return False

            logger.info("🔄 Clicking 'VERIFY CODE' with original code...")
            await page.click('button:has-text("VERIFY CODE")', timeout=10000)

            # Wait for verification to complete (look for "VERIFYING ..." to disappear)
            logger.info("⏳ Waiting for initial verification to complete...")
            verification_complete = False
            for attempt in range(12):  # Wait up to 60 seconds
                try:
                    verifying_button = await page.query_selector(
                        'button:has-text("VERIFYING")'
                    )
                    if verifying_button and await verifying_button.is_visible():
                        logger.info(f"Still verifying... attempt {attempt + 1}/12")
                        await asyncio.sleep(5)
                        continue
                    else:
                        verification_complete = True
                        logger.info("✅ Initial verification process completed!")
                        break
                except:
                    # If we can't find the verifying button, assume verification is done
                    verification_complete = True
                    break

            if not verification_complete:
                logger.warning("⚠️ Initial verification process timed out")

            # Additional wait for page to settle after verification
            await asyncio.sleep(5)
            await self._debug_step(
                page,
                f"02_after_verify_click_{processor.email_address.replace('@', '_')}",
            )

            # Robustly check if login was successful
            login_successful = await self._verify_dashboard_access(page)

            if not login_successful:
                logger.warning("⚠️ Initial login failed. Starting resend flow...")
                await self._debug_step(
                    page,
                    f"03_verify_failed_{processor.email_address.replace('@', '_')}",
                )

                # Check for expired code message - be more liberal
                expired_code_found = False

                # Method 1: Look for specific error text
                try:
                    error_selectors = [
                        'text="Invalid or expired verification code"',
                        ':text("Invalid")',
                        ':text("expired")',
                        ':text("error")',
                    ]
                    for selector in error_selectors:
                        expired_element = await page.query_selector(selector)
                        if expired_element and await expired_element.is_visible():
                            expired_code_found = True
                            logger.info(
                                f"✅ Found error message with selector: {selector}"
                            )
                            break
                except:
                    pass

                # Method 2: If still on verification page, assume code is expired
                if not expired_code_found:
                    try:
                        verify_button = await page.query_selector(
                            'button:has-text("VERIFY CODE")'
                        )
                        if verify_button and await verify_button.is_visible():
                            logger.info(
                                "✅ Still on verification page - assuming code is expired"
                            )
                            expired_code_found = True
                    except:
                        pass

                # Method 3: Check page text content
                if not expired_code_found:
                    try:
                        page_text = await page.text_content("body")
                        if page_text and any(
                            word in page_text.lower()
                            for word in ["invalid", "expired", "wrong"]
                        ):
                            logger.info("✅ Found error keywords in page text")
                            expired_code_found = True
                    except:
                        pass

                if expired_code_found:
                    logger.info("🔄 Initiating resend flow...")

                    # Clear old emails before requesting resend
                    await self.webhook_client.clear_emails(
                        "exaai", processor.email_address
                    )
                    logger.info("🗑️ Cleared old emails")

                    # Look for resend button more liberally
                    resend_clicked = False
                    resend_selectors = [
                        'button:has-text("Resend verification email")',
                        'button:has-text("Resend")',
                        'a:has-text("Resend")',
                        '*text*="resend"*',
                        'button[class*="resend"]',
                    ]

                    for selector in resend_selectors:
                        try:
                            resend_button = await page.query_selector(selector)
                            if resend_button and await resend_button.is_visible():
                                await resend_button.click()
                                logger.info(
                                    f"📧 Clicked resend button with selector: {selector}"
                                )
                                resend_clicked = True
                                await asyncio.sleep(3)
                                break
                        except Exception as e:
                            logger.debug(
                                f"Could not click resend with selector {selector}: {e}"
                            )
                            continue

                    if not resend_clicked:
                        # Take screenshot to see what's on the page
                        await self._debug_step(
                            page,
                            f"04_no_resend_button_{processor.email_address.replace('@', '_')}",
                        )
                        raise Exception("Could not find or click resend button")

                    # Wait for new verification email with both code and magic link
                    verification_data = await self._get_new_verification_data(
                        processor.email_address
                    )
                    if not verification_data:
                        raise Exception("Failed to retrieve new verification email")

                    logger.info(
                        f"📧 Got new verification email with code: {verification_data['code']}"
                    )
                    logger.info(f"🔗 Navigating to new magic link...")

                    # Navigate to the NEW magic link (fresh verification page)
                    await page.goto(
                        verification_data["magic_link"], wait_until="domcontentloaded"
                    )
                    await asyncio.sleep(3)
                    await self._debug_step(
                        page,
                        f"05_new_magic_link_{processor.email_address.replace('@', '_')}",
                    )

                    # Verify we're on a fresh verification page
                    try:
                        verify_button = await page.query_selector(
                            'button:has-text("VERIFY CODE")'
                        )
                        if not (verify_button and await verify_button.is_visible()):
                            raise Exception(
                                "Not on verification page after navigating to new magic link"
                            )
                        logger.info("✅ On fresh verification page with new code")
                    except Exception as e:
                        raise Exception(
                            f"Failed to navigate to fresh verification page: {e}"
                        )

                    # Click verify with the new code (already embedded in the URL)
                    await page.click('button:has-text("VERIFY CODE")')
                    logger.info("🔄 Clicked VERIFY CODE with new embedded code")

                    # Wait for verification to complete (look for "VERIFYING ..." to disappear)
                    logger.info("⏳ Waiting for verification to complete...")
                    verification_complete = False
                    for attempt in range(12):  # Wait up to 60 seconds
                        try:
                            verifying_button = await page.query_selector(
                                'button:has-text("VERIFYING")'
                            )
                            if verifying_button and await verifying_button.is_visible():
                                logger.info(
                                    f"Still verifying... attempt {attempt + 1}/12"
                                )
                                await asyncio.sleep(5)
                                continue
                            else:
                                verification_complete = True
                                logger.info("✅ Verification process completed!")
                                break
                        except:
                            # If we can't find the verifying button, assume verification is done
                            verification_complete = True
                            break

                    if not verification_complete:
                        logger.warning("⚠️ Verification process timed out")

                    # Additional wait for page to settle after verification
                    await asyncio.sleep(5)

                    # Verify again after resend
                    if not await self._verify_dashboard_access(page):
                        raise Exception(
                            "Failed to log in even after resending and using new magic link"
                        )
                else:
                    raise Exception(
                        "Could not determine verification failure state or find resend option"
                    )

            logger.info("✅ Successfully verified and on dashboard")
            await self._debug_step(
                page, f"04_on_dashboard_{processor.email_address.replace('@', '_')}"
            )

            # Navigate to Search page first, then run search
            logger.info("🔍 Navigating to Search page under API Playground...")
            try:
                # Try direct URL first - more reliable
                search_url = "https://dashboard.exa.ai/playground/search?q=blog%20post%20about%20AI&filters=%7B%22text%22%3A%22true%22%2C%22type%22%3A%22auto%22%7D"
                await page.goto(search_url)
                logger.info("✅ Navigated directly to Search playground")
                await asyncio.sleep(3)
                await self._debug_step(
                    page, f"05_search_page_{processor.email_address.replace('@', '_')}"
                )
            except Exception as e:
                logger.warning(f"Could not navigate to Search page: {e}")

            # Now look for "Run" button and result category options
            logger.info("🏃 Looking for result category options and Run button...")
            try:
                # First, look for result category dropdown/options and select a random one
                category_options = [
                    'button:has-text("Neural search")',
                    'button:has-text("Keyword search")',
                    'button:has-text("Similar")',
                    'button:has-text("Auto")',
                    'option[value="neural"]',
                    'option[value="keyword"]',
                    'option[value="similar"]',
                    'option[value="auto"]',
                    '[data-value="neural"]',
                    '[data-value="keyword"]',
                    '[data-value="similar"]',
                    '[data-value="auto"]',
                ]

                # Try to find and click a random category option
                category_selected = False
                for selector in category_options:
                    try:
                        category_button = await page.query_selector(selector)
                        if category_button and await category_button.is_visible():
                            await category_button.click()
                            logger.info(f"✅ Selected search category: {selector}")
                            category_selected = True
                            await asyncio.sleep(1)
                            break
                    except:
                        continue

                if not category_selected:
                    logger.info("ℹ️ No category options found, proceeding with default")

                # Now click the Run button
                run_button = await page.query_selector('button:has-text("Run")')
                if run_button and await run_button.is_visible():
                    await run_button.click()
                    logger.info("✅ Clicked 'Run' button to simulate search")
                    await asyncio.sleep(random.uniform(8, 12))
                    await self._debug_step(
                        page,
                        f"06_after_run_{processor.email_address.replace('@', '_')}",
                    )
                else:
                    logger.warning("⚠️ No 'Run' button found on Search page")
            except Exception as e:
                logger.warning(f"Could not complete search simulation: {e}")

            logger.info("🔑 Now navigating to API Keys page...")
            try:
                api_keys_link = await page.query_selector('a:has-text("API Keys")')
                if api_keys_link and await api_keys_link.is_visible():
                    await api_keys_link.click()
                    await asyncio.sleep(random.uniform(3, 5))
                    await self._debug_step(
                        page,
                        f"07_api_keys_page_{processor.email_address.replace('@', '_')}",
                    )
                    logger.info("✅ Navigated to API Keys page")
                else:
                    raise Exception("API Keys link not found")
            except Exception as e:
                raise Exception(f"Failed to navigate to API Keys: {e}")

            logger.info("📋 Clicking on masked API key area to copy to clipboard...")
            try:
                # Take screenshot before attempting reveal
                await self._debug_step(
                    page, f"07a_before_copy_{processor.email_address.replace('@', '_')}"
                )

                # Strategy: Click directly on the masked key area to copy to clipboard
                masked_key_selectors = [
                    'tr:has-text("default") span:has-text("•")',  # Masked key span with dots
                    'tr:has-text("default") [data-testid*="secret-key"]',  # Secret key element
                    'tr:has-text("default") td:nth-child(2) span',  # Second column span (Secret Key column)
                    'tr:has-text("default") td:nth-child(2)',  # Second column (Secret Key column)
                    'tr:has-text("default") span:contains("*")',  # Any span with asterisks
                ]

                api_key = None
                copy_successful = False

                for i, selector in enumerate(masked_key_selectors):
                    try:
                        masked_element = await page.query_selector(selector)
                        if masked_element and await masked_element.is_visible():
                            logger.info(
                                f"✅ Found masked key element with selector #{i + 1}: {selector}"
                            )

                            # Click on the masked area to copy
                            await masked_element.click()
                            logger.info(f"✅ Clicked on masked key area")
                            await asyncio.sleep(2)

                            # Check clipboard immediately
                            try:
                                clipboard_content = await page.evaluate(
                                    "() => navigator.clipboard.readText()"
                                )
                                if (
                                    clipboard_content
                                    and len(clipboard_content) > 30
                                    and not "•" in clipboard_content
                                    and not "*" in clipboard_content
                                ):
                                    api_key = clipboard_content.strip()
                                    copy_successful = True
                                    logger.info(
                                        f"🎯 Successfully copied FULL API key from clipboard: {len(api_key)} chars"
                                    )
                                    break
                                else:
                                    logger.info(
                                        f"Clipboard content not a full key: {clipboard_content[:20] if clipboard_content else 'None'}..."
                                    )
                            except Exception as e:
                                logger.debug(
                                    f"Could not read clipboard after clicking: {e}"
                                )
                    except Exception as e:
                        logger.debug(
                            f"Could not click masked area with selector {selector}: {e}"
                        )
                        continue

                if not copy_successful:
                    # Fallback: Try clicking any button in the default row
                    logger.info(
                        "⚠️ Direct masked area click didn't work, trying buttons in default row..."
                    )
                    button_selectors = [
                        'tr:has-text("default") button',  # Any button in default row
                        'tr:has-text("default") [role="button"]',  # Any role button in default row
                        'tr:has-text("default") svg',  # Any SVG icon in default row
                    ]

                    for selector in button_selectors:
                        try:
                            button_element = await page.query_selector(selector)
                            if button_element and await button_element.is_visible():
                                await button_element.click()
                                logger.info(f"✅ Clicked button: {selector}")
                                await asyncio.sleep(2)

                                clipboard_content = await page.evaluate(
                                    "() => navigator.clipboard.readText()"
                                )
                                if (
                                    clipboard_content
                                    and len(clipboard_content) > 30
                                    and not "•" in clipboard_content
                                    and not "*" in clipboard_content
                                ):
                                    api_key = clipboard_content.strip()
                                    copy_successful = True
                                    logger.info(
                                        f"🎯 Got FULL API key from clipboard via button: {len(api_key)} chars"
                                    )
                                    break
                        except:
                            continue

                if not copy_successful:
                    await self._debug_step(
                        page,
                        f"08_copy_failed_{processor.email_address.replace('@', '_')}",
                    )
                    raise ValueError(
                        "Could not copy API key to clipboard - still appears to be masked"
                    )

                await self._debug_step(
                    page,
                    f"09_api_key_copied_{processor.email_address.replace('@', '_')}",
                )

                if len(api_key) < 20:
                    raise ValueError(
                        f"API key too short ({len(api_key)} chars) - may still be masked"
                    )

                logger.info(
                    f"🔑 Successfully COPIED FULL API key: {api_key[:12]}... (total length: {len(api_key)} chars)"
                )

            except Exception as e:
                raise Exception(f"Failed to copy API key: {e}")

            # Save credentials
            from core.models import ServiceCredentials

            credential = ServiceCredentials(
                service="exaai", email=processor.email_address, api_key=api_key
            )
            self.storage.add_credential(credential)
            logger.info("💾 Saved credentials successfully")

            return True

        except Exception as e:
            logger.error(f"❌ Error processing {processor.email_address}: {e}")
            if browser_manager.page:
                await self._debug_step(
                    browser_manager.page,
                    f"error_{processor.email_address.replace('@', '_')}",
                )
            return False
        finally:
            await browser_manager.stop()
            logger.info("🔒 Browser session closed")
            logger.info("-" * 50)


def prime_wip_directory():
    if not DISCARD_DIR.exists():
        return
    files_to_move = list(DISCARD_DIR.glob("*.txt"))
    if not files_to_move:
        return
    logger.info("Priming WIP directory from discard pile...")
    WIP_DIR.mkdir(parents=True, exist_ok=True)
    for f in files_to_move:
        f.rename(WIP_DIR / f.name)


async def main(headless: bool):
    logger.info("🤖 RegBot ExaAI Account Recovery - Final Corrected Version")
    logger.info("=========================================================")

    DONE_DIR.mkdir(parents=True, exist_ok=True)
    DISCARD_DIR.mkdir(parents=True, exist_ok=True)

    prime_wip_directory()

    email_files = sorted(list(WIP_DIR.glob("*.txt")))
    logger.info(f"📧 Found {len(email_files)} email(s) to process.")
    if not email_files:
        return

    orchestrator = RecoveryOrchestrator(headless=headless)

    # Process ALL email files
    for file_path in email_files:
        logger.info(f"▶️ Processing {file_path.name}...")
        processor = EmailProcessor(file_path)

        if not processor.read_and_parse():
            file_path.rename(DISCARD_DIR / file_path.name)
            logger.warning(f"Moved unparsable email to discard: {file_path.name}")
            continue

        success = await orchestrator.process_email(processor)

        if success:
            file_path.rename(DONE_DIR / file_path.name)
            logger.info(
                f"✅ Successfully processed and moved to done: {file_path.name}"
            )
        else:
            file_path.rename(DISCARD_DIR / file_path.name)
            logger.warning(f"❌ Failed to process, moved to discard: {file_path.name}")

    logger.info("🏁 Finished processing all emails")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process failed ExaAI sign-ins.")
    parser.add_argument(
        "--headless",
        action=argparse.BooleanOptionalAction,
        default=True,
        help="Run the browser in headless mode. Use --no-headless to show UI.",
    )
    args = parser.parse_args()
    asyncio.run(main(headless=args.headless))
